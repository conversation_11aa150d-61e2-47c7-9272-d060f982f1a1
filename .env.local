# TRON钱包应用环境变量配置

# JWT密钥 - 用于用户认证token签名
# 生产环境请使用强随机字符串
JWT_SECRET=your_jwt_secret_key_here

# TRON API密钥配置
# 主网API密钥 - 用于提高TRON主网API调用限制
# 可在 https://www.trongrid.io/ 申请
TRON_API_KEY=3f67c2cc-9119-468b-a336-f3f95b7bfec3
NEXT_PUBLIC_TRON_API_KEY=3f67c2cc-9119-468b-a336-f3f95b7bfec3

# 测试网API密钥（可选）
# Nile测试网通常不需要API密钥，如果需要可以设置
# TRON_NILE_API_KEY=your_nile_api_key_here
# Shasta测试网通常不需要API密钥，如果需要可以设置
# TRON_SHASTA_API_KEY=your_shasta_api_key_here

# 应用基础URL
# 开发环境
NEXT_PUBLIC_BASE_URL=http://localhost:3000
# 生产环境示例
# NEXT_PUBLIC_BASE_URL=https://your-domain.com

# Cloudflare D1 数据库配置
# 这些变量通常由Cloudflare自动注入，本地开发时可能需要配置
# DATABASE_URL=your_d1_database_url
# DATABASE_ID=your_d1_database_id

# 应用配置
NEXT_PUBLIC_APP_NAME=TRON钱包
NEXT_PUBLIC_DEFAULT_NETWORK=nile

# 安全配置
# 密码加密轮数（建议12或更高）
BCRYPT_ROUNDS=12

# MFA配置
# MFA服务名称（显示在验证器应用中）
MFA_SERVICE_NAME=TRON钱包

# 开发环境配置
NODE_ENV=development

# 日志级别
LOG_LEVEL=info
