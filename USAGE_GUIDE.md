# TRON钱包应用使用指南

## 快速开始

### 1. 项目部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd tron-wallet-app

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置必要的环境变量

# 4. 初始化数据库
npx wrangler d1 create tron-wallet
npx wrangler d1 execute tron-wallet --local --file=./schema.sql

# 5. 启动开发服务器
npm run dev
```

### 2. 初始化超级管理员

```bash
# 1. 访问注册页面
http://localhost:3000/auth/register

# 2. 使用系统邀请码注册第一个用户
邀请码: SUPER_ADMIN_INIT
用户名: admin
密码: 设置强密码

# 3. 手动设置为超级管理员（在数据库中执行）
INSERT INTO admins (user_id, role, permissions, is_active) VALUES 
(1, 'super_admin', '["manage_users", "manage_admins", "view_all_wallets", "view_all_transactions", "system_config"]', TRUE);
```

## 功能使用说明

### H5钱包应用（用户端）

#### 1. 用户注册和登录
- **注册**: 需要有效的邀请码才能注册
- **登录**: 支持用户名密码登录
- **MFA**: 可选择启用双重验证

#### 2. 钱包管理
- **创建钱包**: 系统生成新的TRON地址和私钥
- **导入钱包**: 通过64位私钥导入现有钱包
- **多钱包**: 支持管理多个钱包地址
- **默认钱包**: 可设置默认使用的钱包

#### 3. 转账功能
- **支持币种**: TRX和USDT
- **转账流程**:
  1. 选择发送钱包
  2. 输入接收地址
  3. 输入转账金额和币种
  4. 输入钱包密码确认
  5. 等待区块链确认

#### 4. 收款功能
- **地址分享**: 复制钱包地址
- **二维码**: 生成收款二维码（需要安装qrcode库）
- **金额指定**: 可指定收款金额

### 管理后台（管理端）

#### 1. 权限层级
- **超级管理员**: 拥有所有权限，可以管理所有用户和代理
- **代理**: 只能管理自己邀请的用户，不能管理其他管理员

#### 2. 用户管理
- **查看用户**: 根据权限查看用户列表
- **用户状态**: 启用/禁用用户账户
- **邀请关系**: 查看用户的邀请关系链

#### 3. 代理管理（仅超级管理员）
- **创建代理**: 创建新的代理账户
- **权限分配**: 为代理分配特定权限
- **层级管理**: 管理代理的上下级关系

#### 4. 邀请码管理
- **创建邀请码**: 设置使用次数和过期时间
- **查看状态**: 监控邀请码使用情况
- **权限控制**: 代理只能查看自己创建的邀请码

## 安全最佳实践

### 1. 私钥安全
- 私钥采用密码加密存储
- 用户需要设置钱包密码
- 建议使用强密码保护

### 2. 账户安全
- 启用MFA双重验证
- 定期更换密码
- 避免在公共网络使用

### 3. 管理员安全
- 超级管理员账户需要特别保护
- 定期审查代理权限
- 监控异常操作

## 常见问题

### Q: 如何重置用户密码？
A: 目前需要管理员在数据库中手动重置，后续版本会添加密码重置功能。

### Q: 忘记钱包密码怎么办？
A: 钱包密码用于解密私钥，忘记后无法恢复。建议用户安全保存密码。

### Q: 如何备份钱包？
A: 用户需要安全保存私钥，私钥是恢复钱包的唯一方式。

### Q: 转账失败怎么办？
A: 检查网络连接、余额是否充足、地址是否正确。查看交易记录了解具体错误。

### Q: 如何查看交易状态？
A: 在钱包页面的交易记录中可以查看所有转账状态。

## 技术支持

### 1. 日志查看
- 浏览器控制台查看前端错误
- 服务器日志查看后端错误
- 数据库日志查看数据操作

### 2. 数据库操作
```sql
-- 查看用户列表
SELECT * FROM users;

-- 查看钱包列表
SELECT * FROM wallets;

-- 查看交易记录
SELECT * FROM transactions;

-- 查看邀请码
SELECT * FROM invite_codes;
```

### 3. 常用命令
```bash
# 查看数据库
npx wrangler d1 execute tron-wallet --local --command="SELECT * FROM users LIMIT 10"

# 备份数据库
npx wrangler d1 export tron-wallet --local --output=backup.sql

# 部署到生产环境
npm run deploy
```

## 更新日志

### v1.0.0
- 完成基础钱包功能
- 实现管理后台
- 支持邀请码注册
- 集成TRON网络

### 后续计划
- [ ] 添加密码重置功能
- [ ] 支持更多代币
- [ ] 添加交易手续费估算
- [ ] 优化移动端体验
- [ ] 添加交易统计图表
