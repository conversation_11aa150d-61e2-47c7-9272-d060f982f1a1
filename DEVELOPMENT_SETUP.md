# 开发环境配置指南

## 本地开发设置

### 1. 安装依赖
```bash
pnpm install
```

### 2. 配置环境变量
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：
```env
JWT_SECRET=your_development_jwt_secret
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NODE_ENV=development
```

### 3. 配置Cloudflare D1数据库

#### 方法一：使用Cloudflare D1本地开发
```bash
# 安装wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 创建D1数据库
wrangler d1 create tron-wallet-dev

# 初始化数据库架构
wrangler d1 execute tron-wallet-dev --local --file=./schema.sql

# 启动本地D1数据库
wrangler d1 execute tron-wallet-dev --local --command="SELECT 1"
```

#### 方法二：使用SQLite本地开发（推荐）
```bash
# 安装sqlite3
# macOS: brew install sqlite3
# Ubuntu: sudo apt-get install sqlite3

# 创建本地数据库
sqlite3 local.db < schema.sql
```

### 4. 启动开发服务器
```bash
pnpm run dev
```

## 开发环境注意事项

### 数据库连接
- 当前代码包含了开发环境的fallback机制
- 如果没有配置真实的D1数据库，会使用模拟对象
- 建议配置真实的本地SQLite数据库进行开发

### 认证系统
- 开发环境使用JWT token认证
- 默认的JWT密钥仅用于开发，生产环境请使用强随机字符串

### 初始化超级管理员
1. 访问 http://localhost:3000/auth/register
2. 使用邀请码：`SUPER_ADMIN_INIT`
3. 注册第一个用户
4. 在数据库中手动设置为超级管理员：
```sql
INSERT INTO admins (user_id, role, permissions, is_active) VALUES 
(1, 'super_admin', '["manage_users", "manage_admins", "view_all_wallets", "view_all_transactions", "system_config"]', TRUE);
```

### TRON网络配置
- 开发环境默认使用TRON主网
- 可以通过环境变量切换到测试网：
```env
NEXT_PUBLIC_DEFAULT_NETWORK=nile
```

### 常见问题

#### Q: 数据库操作失败
A: 确保已正确配置D1数据库或本地SQLite数据库

#### Q: 字体加载失败
A: 这是网络问题，不影响应用功能，可以忽略

#### Q: TRON网络连接失败
A: 检查网络连接，确保可以访问TRON网络API

## 生产环境部署

### 1. 配置Cloudflare
```bash
# 创建生产环境D1数据库
wrangler d1 create tron-wallet-prod

# 初始化生产数据库
wrangler d1 execute tron-wallet-prod --file=./schema.sql

# 配置wrangler.toml
```

### 2. 部署应用
```bash
pnpm run deploy
```

### 3. 配置环境变量
在Cloudflare Workers中配置：
- `JWT_SECRET`: 强随机字符串
- `TRON_API_KEY`: TRON网络API密钥（可选）

## 调试技巧

### 1. 查看日志
- 浏览器控制台：前端错误和网络请求
- 终端输出：服务器端错误和警告

### 2. 数据库调试
```bash
# 查看本地数据库内容
sqlite3 local.db "SELECT * FROM users;"
sqlite3 local.db "SELECT * FROM wallets;"
```

### 3. API测试
使用curl或Postman测试API端点：
```bash
# 测试注册API
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"123456","inviteCode":"SUPER_ADMIN_INIT"}'
```

## 代码结构

```
src/
├── app/                    # Next.js 应用路由
│   ├── page.tsx           # 主页
│   ├── wallet/            # 钱包页面
│   ├── admin/             # 管理后台
│   ├── auth/              # 认证页面
│   └── api/               # API路由
├── components/            # React组件
├── lib/                   # 工具库
│   ├── wallet-service.ts  # 钱包服务
│   ├── auth-middleware.ts # 认证中间件
│   ├── tronweb.ts        # TronWeb集成
│   └── db.ts             # 数据库工具
└── hooks/                # React Hooks
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

请确保：
- 代码符合TypeScript规范
- 添加适当的错误处理
- 更新相关文档
