name = "amazon-tk-system-dev"
compatibility_date = "2024-01-01"

# 本地开发环境配置
[env.development]

# KV 存储绑定（本地开发）
[[env.development.kv_namespaces]]
binding = "KV"
id = "rate_limit_kv_dev"
preview_id = "rate_limit_kv_dev"

# D1 数据库绑定（本地开发）
[[env.development.d1_databases]]
binding = "DB"
database_name = "amazon-tk-system-dev"
database_id = "local-dev-db"

# 本地开发环境变量
[env.development.vars]
NODE_ENV = "development"
NEXTAUTH_URL = "http://localhost:3000"
