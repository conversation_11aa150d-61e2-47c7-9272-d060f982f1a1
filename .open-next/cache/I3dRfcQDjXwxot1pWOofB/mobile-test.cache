{"type": "app", "meta": {"headers": {"x-nextjs-stale-time": "300", "x-nextjs-prerender": "1", "x-next-cache-tags": "_N_T_/layout,_N_T_/mobile-test/layout,_N_T_/mobile-test/page,_N_T_/mobile-test"}}, "html": "<!DOCTYPE html><html lang=\"zh-CN\" class=\"light\"><head><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no\"/><link rel=\"stylesheet\" href=\"/_next/static/css/f698dd6eafc1bbea.css\" data-precedence=\"next\"/><link rel=\"preload\" as=\"script\" fetchPriority=\"low\" href=\"/_next/static/chunks/webpack-f18f0730d178edcc.js\"/><script src=\"/_next/static/chunks/bd6e9829-09265336e2d82d8f.js\" async=\"\"></script><script src=\"/_next/static/chunks/110-fff82fab0400228b.js\" async=\"\"></script><script src=\"/_next/static/chunks/main-app-bc62707b6b88ca52.js\" async=\"\"></script><script src=\"/_next/static/chunks/8031-01f7a000112f070b.js\" async=\"\"></script><script src=\"/_next/static/chunks/4110-dd4c5a38b4e9039b.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/layout-9da448754823ad56.js\" async=\"\"></script><script src=\"/_next/static/chunks/7443-ce068aed18e0bfae.js\" async=\"\"></script><script src=\"/_next/static/chunks/4273-5091bf51000c6d49.js\" async=\"\"></script><script src=\"/_next/static/chunks/8688-71a419a1805f60df.js\" async=\"\"></script><script src=\"/_next/static/chunks/201-dc948d51f380cef1.js\" async=\"\"></script><script src=\"/_next/static/chunks/6029-868259c6a7ab3447.js\" async=\"\"></script><script src=\"/_next/static/chunks/7740-b58251a3f97c8f6a.js\" async=\"\"></script><script src=\"/_next/static/chunks/app/mobile-test/page-05e800dca676f147.js\" async=\"\"></script><meta name=\"theme-color\" content=\"#3b82f6\"/><meta name=\"format-detection\" content=\"telephone=no\"/><meta name=\"mobile-web-app-capable\" content=\"yes\"/><meta name=\"apple-mobile-web-app-capable\" content=\"yes\"/><meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\"/><meta name=\"apple-mobile-web-app-title\" content=\"TRON钱包\"/><meta name=\"apple-touch-fullscreen\" content=\"yes\"/><meta name=\"msapplication-TileColor\" content=\"#3b82f6\"/><meta name=\"msapplication-tap-highlight\" content=\"no\"/><link rel=\"manifest\" href=\"/manifest.json\"/><link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/icons/icon-32x32.png\"/><link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/icons/icon-16x16.png\"/><link rel=\"apple-touch-icon\" href=\"/icons/icon-180x180.png\"/><link rel=\"apple-touch-icon\" sizes=\"152x152\" href=\"/icons/icon-152x152.png\"/><link rel=\"apple-touch-icon\" sizes=\"144x144\" href=\"/icons/icon-144x144.png\"/><link rel=\"apple-touch-icon\" sizes=\"120x120\" href=\"/icons/icon-120x120.png\"/><link rel=\"apple-touch-icon\" sizes=\"114x114\" href=\"/icons/icon-114x114.png\"/><link rel=\"apple-touch-icon\" sizes=\"76x76\" href=\"/icons/icon-76x76.png\"/><link rel=\"apple-touch-icon\" sizes=\"72x72\" href=\"/icons/icon-72x72.png\"/><link rel=\"apple-touch-icon\" sizes=\"60x60\" href=\"/icons/icon-60x60.png\"/><link rel=\"apple-touch-icon\" sizes=\"57x57\" href=\"/icons/icon-57x57.png\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphone5_splash.png\" media=\"(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphone6_splash.png\" media=\"(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphoneplus_splash.png\" media=\"(device-width: 621px) and (device-height: 1104px) and (-webkit-device-pixel-ratio: 3)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphonex_splash.png\" media=\"(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphonexr_splash.png\" media=\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/iphonexsmax_splash.png\" media=\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/ipad_splash.png\" media=\"(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/ipadpro1_splash.png\" media=\"(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/ipadpro3_splash.png\" media=\"(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)\"/><link rel=\"apple-touch-startup-image\" href=\"/splash/ipadpro2_splash.png\" media=\"(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)\"/><title>TRON钱包 - 安全便捷的数字资产管理</title><meta name=\"description\" content=\"专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款。安全可靠，操作简单。\"/><meta name=\"keywords\" content=\"TRON钱包,TRX,USDT,区块链钱包,数字资产,转账,收款\"/><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" sizes=\"16x16\"/><script>document.querySelectorAll('body link[rel=\"icon\"], body link[rel=\"apple-touch-icon\"]').forEach(el => document.head.appendChild(el))</script><script src=\"/_next/static/chunks/polyfills-42372ed130431b0a.js\" noModule=\"\"></script></head><body class=\"antialiased bg-white font-sans\"><div hidden=\"\"><!--$--><!--/$--></div><div data-overlay-container=\"true\"><div class=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 px-4\"><div class=\"max-w-md mx-auto space-y-4\"><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h1 class=\"text-xl font-bold text-center w-full\">移动端输入框测试</h1></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased space-y-4\"><div class=\"text-center text-sm text-gray-600\"><p>在移动设备上测试输入框是否会导致页面缩放</p></div></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold\">标准输入框</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased space-y-4\"><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RckurlbH1»\" for=\"react-aria-«Rckurlb»\">文本输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground\" aria-label=\"文本输入\" type=\"text\" placeholder=\"点击测试是否缩放\" tabindex=\"0\" id=\"react-aria-«Rckurlb»\" aria-labelledby=\"react-aria-«Rckurlb» react-aria-«RckurlbH1»\" aria-describedby=\"react-aria-«RckurlbH3» react-aria-«RckurlbH4»\" value=\"\"/></div></div></div><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RkkurlbH1»\" for=\"react-aria-«Rkkurlb»\">邮箱输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" data-type=\"email\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground\" aria-label=\"邮箱输入\" type=\"email\" placeholder=\"输入邮箱地址\" tabindex=\"0\" id=\"react-aria-«Rkkurlb»\" aria-labelledby=\"react-aria-«Rkkurlb» react-aria-«RkkurlbH1»\" aria-describedby=\"react-aria-«RkkurlbH3» react-aria-«RkkurlbH4»\" value=\"\"/></div></div></div><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RskurlbH1»\" for=\"react-aria-«Rskurlb»\">密码输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" data-type=\"password\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground [&amp;::-ms-reveal]:hidden\" aria-label=\"密码输入\" type=\"password\" placeholder=\"输入密码\" tabindex=\"0\" id=\"react-aria-«Rskurlb»\" aria-labelledby=\"react-aria-«Rskurlb» react-aria-«RskurlbH1»\" aria-describedby=\"react-aria-«RskurlbH3» react-aria-«RskurlbH4»\" value=\"\"/></div></div></div><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«R14kurlbH1»\" for=\"react-aria-«R14kurlb»\">数字输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" data-type=\"number\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text text-small group-data-[has-value=true]:text-default-foreground\" aria-label=\"数字输入\" type=\"number\" placeholder=\"输入数字\" tabindex=\"0\" id=\"react-aria-«R14kurlb»\" aria-labelledby=\"react-aria-«R14kurlb» react-aria-«R14kurlbH1»\" aria-describedby=\"react-aria-«R14kurlbH3» react-aria-«R14kurlbH4»\" value=\"\"/></div></div></div></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold\">移动端优化输入框</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased space-y-4\"><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RcmurlbH1»\" for=\"react-aria-«Rcmurlb»\">优化文本输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text group-data-[has-value=true]:text-default-foreground text-base sm:text-sm\" aria-label=\"优化文本输入\" type=\"text\" placeholder=\"防缩放输入框\" tabindex=\"0\" id=\"react-aria-«Rcmurlb»\" aria-labelledby=\"react-aria-«Rcmurlb» react-aria-«RcmurlbH1»\" aria-describedby=\"react-aria-«RcmurlbH3» react-aria-«RcmurlbH4»\" style=\"font-size:16px\" value=\"\"/></div></div></div><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RkmurlbH1»\" for=\"react-aria-«Rkmurlb»\">优化邮箱输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" data-type=\"email\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text group-data-[has-value=true]:text-default-foreground text-base sm:text-sm\" aria-label=\"优化邮箱输入\" type=\"email\" placeholder=\"防缩放邮箱输入\" tabindex=\"0\" id=\"react-aria-«Rkmurlb»\" aria-labelledby=\"react-aria-«Rkmurlb» react-aria-«RkmurlbH1»\" aria-describedby=\"react-aria-«RkmurlbH3» react-aria-«RkmurlbH4»\" style=\"font-size:16px\" value=\"\"/></div></div></div><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\"><label data-slot=\"label\" class=\"absolute z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small group-data-[filled-within=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RsmurlbH1»\" for=\"react-aria-«Rsmurlb»\">优化密码输入</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full items-center h-full box-border group-data-[has-label=true]:items-end pb-0.5\"><input data-slot=\"input\" data-type=\"password\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text group-data-[has-value=true]:text-default-foreground text-base sm:text-sm [&amp;::-ms-reveal]:hidden\" aria-label=\"优化密码输入\" type=\"password\" placeholder=\"防缩放密码输入\" tabindex=\"0\" id=\"react-aria-«Rsmurlb»\" aria-labelledby=\"react-aria-«Rsmurlb» react-aria-«RsmurlbH1»\" aria-describedby=\"react-aria-«RsmurlbH3» react-aria-«RsmurlbH4»\" style=\"font-size:16px\" value=\"\"/></div></div></div></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border bg-content1 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold\">其他表单元素</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased space-y-4\"><div class=\"group flex flex-col data-[hidden=true]:hidden w-full\" data-slot=\"base\" data-filled=\"true\" data-filled-within=\"true\" data-has-elements=\"true\" data-has-label=\"true\" data-has-value=\"true\"><div data-slot=\"input-wrapper\" class=\"relative w-full inline-flex tap-highlight-transparent shadow-sm px-3 bg-default-100 data-[hover=true]:bg-default-200 group-data-[focus=true]:bg-default-100 min-h-10 rounded-medium flex-col items-start justify-center gap-0 !h-auto transition-background motion-reduce:transition-none !duration-150 outline-none group-data-[focus-visible=true]:z-10 group-data-[focus-visible=true]:ring-2 group-data-[focus-visible=true]:ring-focus group-data-[focus-visible=true]:ring-offset-2 group-data-[focus-visible=true]:ring-offset-background h-14 py-2\" style=\"cursor:text\" data-has-multiple-rows=\"true\"><label data-slot=\"label\" class=\"z-10 pointer-events-none origin-top-left flex-shrink-0 rtl:origin-top-right subpixel-antialiased block text-foreground-500 cursor-text relative will-change-auto !duration-200 !ease-out motion-reduce:transition-none transition-[transform,color,left,opacity] group-data-[filled-within=true]:text-default-600 group-data-[filled-within=true]:pointer-events-auto group-data-[filled-within=true]:scale-85 text-small pb-0.5 pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RcourlbH1»\" for=\"react-aria-«Rcourlb»\">文本域</label><div data-slot=\"inner-wrapper\" class=\"inline-flex w-full h-full box-border items-start group-data-[has-label=true]:items-start pb-0.5\"><textarea data-slot=\"input\" class=\"w-full font-normal bg-transparent !outline-none placeholder:text-foreground-500 focus-visible:outline-none data-[has-start-content=true]:ps-1.5 data-[has-end-content=true]:pe-1.5 data-[type=color]:rounded-none file:cursor-pointer file:bg-transparent file:border-0 autofill:bg-transparent bg-clip-text resize-none data-[hide-scroll=true]:scrollbar-hide group-data-[has-value=true]:text-default-foreground pt-0 transition-height !duration-100 motion-reduce:transition-none text-base sm:text-sm pe-0\" aria-label=\"文本域\" placeholder=\"输入多行文本\" tabindex=\"0\" id=\"react-aria-«Rcourlb»\" aria-labelledby=\"react-aria-«Rcourlb» react-aria-«RcourlbH1»\" aria-describedby=\"react-aria-«RcourlbH3» react-aria-«RcourlbH4»\" data-hide-scroll=\"true\"></textarea></div></div></div><div data-slot=\"base\" data-filled=\"true\" data-has-label=\"true\" class=\"group inline-flex flex-col relative w-full transition-background motion-reduce:transition-none !duration-150\"><div style=\"border:0;clip:rect(0 0 0 0);clip-path:inset(50%);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap\" aria-hidden=\"true\" data-a11y-ignore=\"aria-hidden-focus\" data-testid=\"hidden-select-container\"><label>选择框<select tabindex=\"-1\"><option selected=\"\"></option><option value=\"option1\">选项1</option><option value=\"option2\">选项2</option><option value=\"option3\">选项3</option></select></label></div><div data-slot=\"mainWrapper\" class=\"w-full flex flex-col\"><button data-slot=\"trigger\" class=\"relative px-3 w-full inline-flex shadow-sm tap-highlight-transparent group-data-[focus=true]:bg-default-200 rounded-medium flex-col items-start justify-center gap-0 bg-default-100 data-[hover=true]:bg-default-200 outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 h-14 min-h-14 py-2\" type=\"button\" tabindex=\"0\" data-react-aria-pressable=\"true\" id=\"react-aria-«RkourlbH2»\" aria-labelledby=\"react-aria-«RkourlbH7» react-aria-«RkourlbH3»\" aria-describedby=\"react-aria-«RkourlbH5» react-aria-«RkourlbH6»\" aria-haspopup=\"listbox\" aria-expanded=\"false\"><label data-slot=\"label\" class=\"block absolute z-10 flex-shrink-0 subpixel-antialiased text-foreground-500 pointer-events-none group-data-[has-label-outside=true]:pointer-events-auto cursor-pointer will-change-auto origin-top-left rtl:origin-top-right !duration-200 !ease-out transition-[transform,color,left,opacity] motion-reduce:transition-none group-data-[filled=true]:text-default-600 group-data-[filled=true]:scale-85 text-small group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)] pe-2 max-w-full text-ellipsis overflow-hidden\" id=\"react-aria-«RkourlbH3»\">选择框</label><div data-slot=\"innerWrapper\" class=\"inline-flex h-fit w-[calc(100%_-_theme(spacing.6))] min-h-4 items-center gap-1.5 box-border group-data-[has-label=true]:pt-4\"><span data-slot=\"value\" class=\"text-foreground-500 font-normal w-full text-start text-small truncate group-data-[has-value=true]:text-default-foreground\" id=\"react-aria-«RkourlbH7»\">选择一个选项</span></div><svg aria-hidden=\"true\" fill=\"none\" focusable=\"false\" height=\"1em\" role=\"presentation\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\" viewBox=\"0 0 24 24\" width=\"1em\" data-slot=\"selectorIcon\" class=\"absolute end-3 w-4 h-4 transition-transform duration-150 ease motion-reduce:transition-none data-[open=true]:rotate-180\"><path d=\"m6 9 6 6 6-6\"></path></svg></button></div></div><button type=\"button\" tabindex=\"0\" data-react-aria-pressable=\"true\" class=\"z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent transform-gpu data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 px-4 min-w-20 h-10 text-small gap-2 rounded-medium [&amp;&gt;svg]:max-w-[theme(spacing.8)] transition-transform-colors-opacity motion-reduce:transition-none bg-primary text-primary-foreground data-[hover=true]:opacity-hover w-full\">测试按钮</button></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none bg-blue-50\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold text-blue-800\">测试说明</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased\"><div class=\"text-sm text-blue-700 space-y-2\"><h3 class=\"font-semibold\">移动端测试步骤：</h3><ol class=\"list-decimal list-inside space-y-1\"><li>在移动设备上打开此页面</li><li>点击各种输入框</li><li>观察页面是否发生缩放</li><li>测试输入和编辑功能</li><li>测试按钮和选择框</li></ol><h3 class=\"font-semibold mt-4\">期望结果：</h3><ul class=\"list-disc list-inside space-y-1\"><li>点击输入框时页面不缩放</li><li>输入框可以正常输入文字</li><li>键盘弹出时布局正常</li><li>所有交互功能正常工作</li></ul></div></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none bg-green-50\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold text-green-800\">技术实现</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased\"><div class=\"text-sm text-green-700 space-y-2\"><h3 class=\"font-semibold\">防缩放措施：</h3><ul class=\"list-disc list-inside space-y-1\"><li>viewport meta标签设置user-scalable=no</li><li>输入框字体大小设为16px（iOS要求）</li><li>CSS防缩放样式</li><li>JavaScript事件监听和处理</li><li>专门的移动端输入框组件</li></ul><h3 class=\"font-semibold mt-4\">兼容性：</h3><ul class=\"list-disc list-inside space-y-1\"><li>iOS Safari</li><li>Android Chrome</li><li>移动端其他浏览器</li><li>桌面端正常显示</li></ul></div></div></div><div class=\"flex flex-col relative overflow-hidden h-auto text-foreground box-border outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 shadow-medium rounded-large transition-transform-background motion-reduce:transition-none bg-gray-50\" tabindex=\"-1\"><div class=\"flex p-3 z-10 w-full justify-start items-center shrink-0 overflow-inherit color-inherit subpixel-antialiased rounded-t-large\"><h2 class=\"text-lg font-semibold\">当前表单数据</h2></div><div class=\"relative flex w-full p-3 flex-auto flex-col place-content-inherit align-items-inherit h-auto break-words text-left overflow-y-auto subpixel-antialiased\"><pre class=\"text-xs bg-white p-2 rounded overflow-auto\">{\n  &quot;text&quot;: &quot;&quot;,\n  &quot;email&quot;: &quot;&quot;,\n  &quot;password&quot;: &quot;&quot;,\n  &quot;number&quot;: &quot;&quot;,\n  &quot;textarea&quot;: &quot;&quot;,\n  &quot;select&quot;: &quot;&quot;\n}</pre></div></div></div></div><!--$--><!--/$--></div><script src=\"/_next/static/chunks/webpack-f18f0730d178edcc.js\" async=\"\"></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,\"1:\\\"$Sreact.fragment\\\"\\n2:I[14384,[\\\"8031\\\",\\\"static/chunks/8031-01f7a000112f070b.js\\\",\\\"4110\\\",\\\"static/chunks/4110-dd4c5a38b4e9039b.js\\\",\\\"7177\\\",\\\"static/chunks/app/layout-9da448754823ad56.js\\\"],\\\"default\\\"]\\n3:I[44132,[\\\"8031\\\",\\\"static/chunks/8031-01f7a000112f070b.js\\\",\\\"4110\\\",\\\"static/chunks/4110-dd4c5a38b4e9039b.js\\\",\\\"7177\\\",\\\"static/chunks/app/layout-9da448754823ad56.js\\\"],\\\"default\\\"]\\n4:I[77081,[\\\"8031\\\",\\\"static/chunks/8031-01f7a000112f070b.js\\\",\\\"4110\\\",\\\"static/chunks/4110-dd4c5a38b4e9039b.js\\\",\\\"7177\\\",\\\"static/chunks/app/layout-9da448754823ad56.js\\\"],\\\"default\\\"]\\n5:I[58387,[],\\\"\\\"]\\n6:I[1007,[],\\\"\\\"]\\n7:I[85599,[\\\"8031\\\",\\\"static/chunks/8031-01f7a000112f070b.js\\\",\\\"4110\\\",\\\"static/chunks/4110-dd4c5a38b4e9039b.js\\\",\\\"7177\\\",\\\"static/chunks/app/layout-9da448754823ad56.js\\\"],\\\"default\\\"]\\n8:I[33790,[],\\\"ClientPageRoot\\\"]\\n9:I[49038,[\\\"8031\\\",\\\"static/chunks/8031-01f7a000112f070b.js\\\",\\\"4110\\\",\\\"static/chunks/4110-dd4c5a38b4e9039b.js\\\",\\\"7443\\\",\\\"static/chunks/7443-ce068aed18e0bfae.js\\\",\\\"4273\\\",\\\"static/chunks/4273-5091bf51000c6d49.js\\\",\\\"8688\\\",\\\"static/chunks/8688-71a419a1805f60df.js\\\",\\\"201\\\",\\\"static/chunks/201-dc948d51f380cef1.js\\\",\\\"6029\\\",\\\"static/chunks/6029-868259c6a7ab3447.js\\\",\\\"7740\\\",\\\"static/chunks/7740-b58251a3f97c8f6a.js\\\",\\\"4500\\\",\\\"static/chunks/app/mobile-test/page-05e800dca676f147.js\\\"],\\\"default\\\"]\\nc:I[83809,[],\\\"OutletBoundary\\\"]\\nf:I[20751,[],\\\"AsyncMetadataOutlet\\\"]\\n11:I[83809,[],\\\"ViewportBoundary\\\"]\\n13:I[83809,[],\\\"MetadataBoundary\\\"]\\n15:I[61254,[],\\\"\\\"]\\n:HL[\\\"/_next/static/css/f698dd6eafc1bbea.css\\\",\\\"style\\\"]\\n\"])</script><script>self.__next_f.push([1,\"0:{\\\"P\\\":null,\\\"b\\\":\\\"I3dRfcQDjXwxot1pWOofB\\\",\\\"p\\\":\\\"\\\",\\\"c\\\":[\\\"\\\",\\\"mobile-test\\\"],\\\"i\\\":false,\\\"f\\\":[[[\\\"\\\",{\\\"children\\\":[\\\"mobile-test\\\",{\\\"children\\\":[\\\"__PAGE__\\\",{}]}]},\\\"$undefined\\\",\\\"$undefined\\\",true],[\\\"\\\",[\\\"$\\\",\\\"$1\\\",\\\"c\\\",{\\\"children\\\":[[[\\\"$\\\",\\\"link\\\",\\\"0\\\",{\\\"rel\\\":\\\"stylesheet\\\",\\\"href\\\":\\\"/_next/static/css/f698dd6eafc1bbea.css\\\",\\\"precedence\\\":\\\"next\\\",\\\"crossOrigin\\\":\\\"$undefined\\\",\\\"nonce\\\":\\\"$undefined\\\"}]],[\\\"$\\\",\\\"html\\\",null,{\\\"lang\\\":\\\"zh-CN\\\",\\\"className\\\":\\\"light\\\",\\\"children\\\":[[\\\"$\\\",\\\"head\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"theme-color\\\",\\\"content\\\":\\\"#3b82f6\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"format-detection\\\",\\\"content\\\":\\\"telephone=no\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"mobile-web-app-capable\\\",\\\"content\\\":\\\"yes\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-capable\\\",\\\"content\\\":\\\"yes\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-status-bar-style\\\",\\\"content\\\":\\\"default\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-mobile-web-app-title\\\",\\\"content\\\":\\\"TRON钱包\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"apple-touch-fullscreen\\\",\\\"content\\\":\\\"yes\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"msapplication-TileColor\\\",\\\"content\\\":\\\"#3b82f6\\\"}],[\\\"$\\\",\\\"meta\\\",null,{\\\"name\\\":\\\"msapplication-tap-highlight\\\",\\\"content\\\":\\\"no\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"manifest\\\",\\\"href\\\":\\\"/manifest.json\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"icon\\\",\\\"type\\\":\\\"image/png\\\",\\\"sizes\\\":\\\"32x32\\\",\\\"href\\\":\\\"/icons/icon-32x32.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"icon\\\",\\\"type\\\":\\\"image/png\\\",\\\"sizes\\\":\\\"16x16\\\",\\\"href\\\":\\\"/icons/icon-16x16.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"href\\\":\\\"/icons/icon-180x180.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"152x152\\\",\\\"href\\\":\\\"/icons/icon-152x152.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"144x144\\\",\\\"href\\\":\\\"/icons/icon-144x144.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"120x120\\\",\\\"href\\\":\\\"/icons/icon-120x120.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"114x114\\\",\\\"href\\\":\\\"/icons/icon-114x114.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"76x76\\\",\\\"href\\\":\\\"/icons/icon-76x76.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"72x72\\\",\\\"href\\\":\\\"/icons/icon-72x72.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"60x60\\\",\\\"href\\\":\\\"/icons/icon-60x60.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-icon\\\",\\\"sizes\\\":\\\"57x57\\\",\\\"href\\\":\\\"/icons/icon-57x57.png\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphone5_splash.png\\\",\\\"media\\\":\\\"(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphone6_splash.png\\\",\\\"media\\\":\\\"(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphoneplus_splash.png\\\",\\\"media\\\":\\\"(device-width: 621px) and (device-height: 1104px) and (-webkit-device-pixel-ratio: 3)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphonex_splash.png\\\",\\\"media\\\":\\\"(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphonexr_splash.png\\\",\\\"media\\\":\\\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/iphonexsmax_splash.png\\\",\\\"media\\\":\\\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/ipad_splash.png\\\",\\\"media\\\":\\\"(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/ipadpro1_splash.png\\\",\\\"media\\\":\\\"(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/ipadpro3_splash.png\\\",\\\"media\\\":\\\"(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)\\\"}],[\\\"$\\\",\\\"link\\\",null,{\\\"rel\\\":\\\"apple-touch-startup-image\\\",\\\"href\\\":\\\"/splash/ipadpro2_splash.png\\\",\\\"media\\\":\\\"(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)\\\"}]]}],[\\\"$\\\",\\\"body\\\",null,{\\\"className\\\":\\\"antialiased bg-white font-sans\\\",\\\"children\\\":[\\\"$\\\",\\\"$L2\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"$L3\\\",null,{}],[\\\"$\\\",\\\"$L4\\\",null,{}],[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L6\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":[[[\\\"$\\\",\\\"title\\\",null,{\\\"children\\\":\\\"404: This page could not be found.\\\"}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"fontFamily\\\":\\\"system-ui,\\\\\\\"Segoe UI\\\\\\\",Roboto,Helvetica,Arial,sans-serif,\\\\\\\"Apple Color Emoji\\\\\\\",\\\\\\\"Segoe UI Emoji\\\\\\\"\\\",\\\"height\\\":\\\"100vh\\\",\\\"textAlign\\\":\\\"center\\\",\\\"display\\\":\\\"flex\\\",\\\"flexDirection\\\":\\\"column\\\",\\\"alignItems\\\":\\\"center\\\",\\\"justifyContent\\\":\\\"center\\\"},\\\"children\\\":[\\\"$\\\",\\\"div\\\",null,{\\\"children\\\":[[\\\"$\\\",\\\"style\\\",null,{\\\"dangerouslySetInnerHTML\\\":{\\\"__html\\\":\\\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\\\"}}],[\\\"$\\\",\\\"h1\\\",null,{\\\"className\\\":\\\"next-error-h1\\\",\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\",\\\"margin\\\":\\\"0 20px 0 0\\\",\\\"padding\\\":\\\"0 23px 0 0\\\",\\\"fontSize\\\":24,\\\"fontWeight\\\":500,\\\"verticalAlign\\\":\\\"top\\\",\\\"lineHeight\\\":\\\"49px\\\"},\\\"children\\\":404}],[\\\"$\\\",\\\"div\\\",null,{\\\"style\\\":{\\\"display\\\":\\\"inline-block\\\"},\\\"children\\\":[\\\"$\\\",\\\"h2\\\",null,{\\\"style\\\":{\\\"fontSize\\\":14,\\\"fontWeight\\\":400,\\\"lineHeight\\\":\\\"49px\\\",\\\"margin\\\":0},\\\"children\\\":\\\"This page could not be found.\\\"}]}]]}]}]],[]],\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"}],[\\\"$\\\",\\\"$L7\\\",null,{}]]}]}]]}]]}],{\\\"children\\\":[\\\"mobile-test\\\",[\\\"$\\\",\\\"$1\\\",\\\"c\\\",{\\\"children\\\":[null,[\\\"$\\\",\\\"$L5\\\",null,{\\\"parallelRouterKey\\\":\\\"children\\\",\\\"error\\\":\\\"$undefined\\\",\\\"errorStyles\\\":\\\"$undefined\\\",\\\"errorScripts\\\":\\\"$undefined\\\",\\\"template\\\":[\\\"$\\\",\\\"$L6\\\",null,{}],\\\"templateStyles\\\":\\\"$undefined\\\",\\\"templateScripts\\\":\\\"$undefined\\\",\\\"notFound\\\":\\\"$undefined\\\",\\\"forbidden\\\":\\\"$undefined\\\",\\\"unauthorized\\\":\\\"$undefined\\\"}]]}],{\\\"children\\\":[\\\"__PAGE__\\\",[\\\"$\\\",\\\"$1\\\",\\\"c\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$L8\\\",null,{\\\"Component\\\":\\\"$9\\\",\\\"searchParams\\\":{},\\\"params\\\":{},\\\"promises\\\":[\\\"$@a\\\",\\\"$@b\\\"]}],null,[\\\"$\\\",\\\"$Lc\\\",null,{\\\"children\\\":[\\\"$Ld\\\",\\\"$Le\\\",[\\\"$\\\",\\\"$Lf\\\",null,{\\\"promise\\\":\\\"$@10\\\"}]]}]]}],{},null,false]},null,false]},null,false],[\\\"$\\\",\\\"$1\\\",\\\"h\\\",{\\\"children\\\":[null,[\\\"$\\\",\\\"$1\\\",\\\"F7IxpSKISIERhbjHGPi1Ov\\\",{\\\"children\\\":[[\\\"$\\\",\\\"$L11\\\",null,{\\\"children\\\":\\\"$L12\\\"}],null]}],[\\\"$\\\",\\\"$L13\\\",null,{\\\"children\\\":\\\"$L14\\\"}]]}],false]],\\\"m\\\":\\\"$undefined\\\",\\\"G\\\":[\\\"$15\\\",\\\"$undefined\\\"],\\\"s\\\":false,\\\"S\\\":true}\\n\"])</script><script>self.__next_f.push([1,\"16:\\\"$Sreact.suspense\\\"\\n17:I[20751,[],\\\"AsyncMetadata\\\"]\\na:{}\\nb:{}\\n14:[\\\"$\\\",\\\"div\\\",null,{\\\"hidden\\\":true,\\\"children\\\":[\\\"$\\\",\\\"$16\\\",null,{\\\"fallback\\\":null,\\\"children\\\":[\\\"$\\\",\\\"$L17\\\",null,{\\\"promise\\\":\\\"$@18\\\"}]}]}]\\n\"])</script><script>self.__next_f.push([1,\"e:null\\n\"])</script><script>self.__next_f.push([1,\"12:[[\\\"$\\\",\\\"meta\\\",\\\"0\\\",{\\\"charSet\\\":\\\"utf-8\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"viewport\\\",\\\"content\\\":\\\"width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no\\\"}]]\\nd:null\\n\"])</script><script>self.__next_f.push([1,\"10:{\\\"metadata\\\":[[\\\"$\\\",\\\"title\\\",\\\"0\\\",{\\\"children\\\":\\\"TRON钱包 - 安全便捷的数字资产管理\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"1\\\",{\\\"name\\\":\\\"description\\\",\\\"content\\\":\\\"专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款。安全可靠，操作简单。\\\"}],[\\\"$\\\",\\\"meta\\\",\\\"2\\\",{\\\"name\\\":\\\"keywords\\\",\\\"content\\\":\\\"TRON钱包,TRX,USDT,区块链钱包,数字资产,转账,收款\\\"}],[\\\"$\\\",\\\"link\\\",\\\"3\\\",{\\\"rel\\\":\\\"icon\\\",\\\"href\\\":\\\"/favicon.ico\\\",\\\"type\\\":\\\"image/x-icon\\\",\\\"sizes\\\":\\\"16x16\\\"}]],\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n18:{\\\"metadata\\\":\\\"$10:metadata\\\",\\\"error\\\":null,\\\"digest\\\":\\\"$undefined\\\"}\\n\"])</script></body></html>", "rsc": "1:\"$Sreact.fragment\"\n2:I[14384,[\"8031\",\"static/chunks/8031-01f7a000112f070b.js\",\"4110\",\"static/chunks/4110-dd4c5a38b4e9039b.js\",\"7177\",\"static/chunks/app/layout-9da448754823ad56.js\"],\"default\"]\n3:I[44132,[\"8031\",\"static/chunks/8031-01f7a000112f070b.js\",\"4110\",\"static/chunks/4110-dd4c5a38b4e9039b.js\",\"7177\",\"static/chunks/app/layout-9da448754823ad56.js\"],\"default\"]\n4:I[77081,[\"8031\",\"static/chunks/8031-01f7a000112f070b.js\",\"4110\",\"static/chunks/4110-dd4c5a38b4e9039b.js\",\"7177\",\"static/chunks/app/layout-9da448754823ad56.js\"],\"default\"]\n5:I[58387,[],\"\"]\n6:I[1007,[],\"\"]\n7:I[85599,[\"8031\",\"static/chunks/8031-01f7a000112f070b.js\",\"4110\",\"static/chunks/4110-dd4c5a38b4e9039b.js\",\"7177\",\"static/chunks/app/layout-9da448754823ad56.js\"],\"default\"]\n8:I[33790,[],\"ClientPageRoot\"]\n9:I[49038,[\"8031\",\"static/chunks/8031-01f7a000112f070b.js\",\"4110\",\"static/chunks/4110-dd4c5a38b4e9039b.js\",\"7443\",\"static/chunks/7443-ce068aed18e0bfae.js\",\"4273\",\"static/chunks/4273-5091bf51000c6d49.js\",\"8688\",\"static/chunks/8688-71a419a1805f60df.js\",\"201\",\"static/chunks/201-dc948d51f380cef1.js\",\"6029\",\"static/chunks/6029-868259c6a7ab3447.js\",\"7740\",\"static/chunks/7740-b58251a3f97c8f6a.js\",\"4500\",\"static/chunks/app/mobile-test/page-05e800dca676f147.js\"],\"default\"]\nc:I[83809,[],\"OutletBoundary\"]\nf:I[20751,[],\"AsyncMetadataOutlet\"]\n11:I[83809,[],\"ViewportBoundary\"]\n13:I[83809,[],\"MetadataBoundary\"]\n15:I[61254,[],\"\"]\n:HL[\"/_next/static/css/f698dd6eafc1bbea.css\",\"style\"]\n0:{\"P\":null,\"b\":\"I3dRfcQDjXwxot1pWOofB\",\"p\":\"\",\"c\":[\"\",\"mobile-test\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"mobile-test\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/f698dd6eafc1bbea.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"zh-CN\",\"className\":\"light\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#3b82f6\"}],[\"$\",\"meta\",null,{\"name\":\"format-detection\",\"content\":\"telephone=no\"}],[\"$\",\"meta\",null,{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",null,{\"name\":\"apple-mobile-web-app-title\",\"content\":\"TRON钱包\"}],[\"$\",\"meta\",null,{\"name\":\"apple-touch-fullscreen\",\"content\":\"yes\"}],[\"$\",\"meta\",null,{\"name\":\"msapplication-TileColor\",\"content\":\"#3b82f6\"}],[\"$\",\"meta\",null,{\"name\":\"msapplication-tap-highlight\",\"content\":\"no\"}],[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/manifest.json\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"type\":\"image/png\",\"sizes\":\"32x32\",\"href\":\"/icons/icon-32x32.png\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"type\":\"image/png\",\"sizes\":\"16x16\",\"href\":\"/icons/icon-16x16.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"href\":\"/icons/icon-180x180.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"152x152\",\"href\":\"/icons/icon-152x152.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"144x144\",\"href\":\"/icons/icon-144x144.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"120x120\",\"href\":\"/icons/icon-120x120.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"114x114\",\"href\":\"/icons/icon-114x114.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"76x76\",\"href\":\"/icons/icon-76x76.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"72x72\",\"href\":\"/icons/icon-72x72.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"60x60\",\"href\":\"/icons/icon-60x60.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-icon\",\"sizes\":\"57x57\",\"href\":\"/icons/icon-57x57.png\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphone5_splash.png\",\"media\":\"(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphone6_splash.png\",\"media\":\"(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphoneplus_splash.png\",\"media\":\"(device-width: 621px) and (device-height: 1104px) and (-webkit-device-pixel-ratio: 3)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphonex_splash.png\",\"media\":\"(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphonexr_splash.png\",\"media\":\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/iphonexsmax_splash.png\",\"media\":\"(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/ipad_splash.png\",\"media\":\"(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/ipadpro1_splash.png\",\"media\":\"(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/ipadpro3_splash.png\",\"media\":\"(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)\"}],[\"$\",\"link\",null,{\"rel\":\"apple-touch-startup-image\",\"href\":\"/splash/ipadpro2_splash.png\",\"media\":\"(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)\"}]]}],[\"$\",\"body\",null,{\"className\":\"antialiased bg-white font-sans\",\"children\":[\"$\",\"$L2\",null,{\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"$L4\",null,{}],[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L7\",null,{}]]}]}]]}]]}],{\"children\":[\"mobile-test\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L8\",null,{\"Component\":\"$9\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@a\",\"$@b\"]}],null,[\"$\",\"$Lc\",null,{\"children\":[\"$Ld\",\"$Le\",[\"$\",\"$Lf\",null,{\"promise\":\"$@10\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"F7IxpSKISIERhbjHGPi1Ov\",{\"children\":[[\"$\",\"$L11\",null,{\"children\":\"$L12\"}],null]}],[\"$\",\"$L13\",null,{\"children\":\"$L14\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$15\",\"$undefined\"],\"s\":false,\"S\":true}\n16:\"$Sreact.suspense\"\n17:I[20751,[],\"AsyncMetadata\"]\na:{}\nb:{}\n14:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$16\",null,{\"fallback\":null,\"children\":[\"$\",\"$L17\",null,{\"promise\":\"$@18\"}]}]}]\ne:null\n12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no\"}]]\nd:null\n10:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"TRON钱包 - 安全便捷的数字资产管理\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款。安全可靠，操作简单。\"}],[\"$\",\"meta\",\"2\",{\"name\":\"keywords\",\"content\":\"TRON钱包,TRX,USDT,区块链钱包,数字资产,转账,收款\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n18:{\"metadata\":\"$10:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"}