{"name": "tron-wallet-app", "version": "1.0.0", "private": true, "description": "Professional TRON wallet application with admin management", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "init-db": "node init-local-db.js", "db:reset": "node init-local-db.js", "generate-icons": "node scripts/generate-icons.js"}, "dependencies": {"@heroui/react": "^2.7.11", "@heroui/theme": "^2.4.17", "@opennextjs/cloudflare": "^1.5.0", "bcryptjs": "^2.4.3", "cloudflare": "^4.5.0", "framer-motion": "^12.23.3", "jsonwebtoken": "^9.0.2", "next": "15.3.5", "otplib": "^12.0.1", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tronweb": "^6.0.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.7", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5", "wrangler": "^4.24.3"}}