"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4273],{3351:(e,t,n)=>{n.d(t,{C:()=>m,Y:()=>v});let r=new Set(["Arab","<PERSON>yrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),o=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function i(e){if(Intl.Locale){let t=new Intl.Locale(e).maximize(),n="function"==typeof t.getTextInfo?t.getTextInfo():t.textInfo;if(n)return"rtl"===n.direction;if(t.script)return r.has(t.script)}let t=e.split("-")[0];return o.has(t)}var l=n(9585),s=n(37285);let a=Symbol.for("react-aria.i18n.locale");function u(){let e="undefined"!=typeof window&&window[a]||"undefined"!=typeof navigator&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:i(e)?"rtl":"ltr"}}let c=u(),d=new Set;function f(){for(let e of(c=u(),d))e(c)}function h(){let e=(0,s.wR)(),[t,n]=(0,l.useState)(c);return((0,l.useEffect)(()=>(0===d.size&&window.addEventListener("languagechange",f),d.add(n),()=>{d.delete(n),0===d.size&&window.removeEventListener("languagechange",f)}),[]),e)?{locale:"en-US",direction:"ltr"}:t}let p=l.createContext(null);function m(e){let{locale:t,children:n}=e,r=h(),o=l.useMemo(()=>t?{locale:t,direction:i(t)?"rtl":"ltr"}:r,[r,t]);return l.createElement(p.Provider,{value:o},n)}function v(){let e=h();return(0,l.useContext)(p)||e}},28617:(e,t,n)=>{let r;n.d(t,{o:()=>h});var o=n(3351);let i=Symbol.for("react-aria.i18n.locale"),l=Symbol.for("react-aria.i18n.strings");class s{getStringForLocale(e,t){let n=this.getStringsForLocale(t)[e];if(!n)throw Error(`Could not find intl message ${e} in ${t} locale`);return n}getStringsForLocale(e){let t=this.strings[e];return t||(t=function(e,t,n="en-US"){var r;if(t[e])return t[e];let o=(r=e,Intl.Locale?new Intl.Locale(r).language:r.split("-")[0]);if(t[o])return t[o];for(let e in t)if(e.startsWith(o+"-"))return t[e];return t[n]}(e,this.strings,this.defaultLocale),this.strings[e]=t),t}static getGlobalDictionaryForPackage(e){if("undefined"==typeof window)return null;let t=window[i];if(void 0===r){let e=window[l];if(!e)return null;for(let n in r={},e)r[n]=new s({[t]:e[n]},t)}let n=null==r?void 0:r[e];if(!n)throw Error(`Strings for package "${e}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return n}constructor(e,t="en-US"){this.strings=Object.fromEntries(Object.entries(e).filter(([,e])=>e)),this.defaultLocale=t}}let a=new Map,u=new Map;class c{format(e,t){let n=this.strings.getStringForLocale(e,this.locale);return"function"==typeof n?n(t,this):n}plural(e,t,n="cardinal"){let r=t["="+e];if(r)return"function"==typeof r?r():r;let o=this.locale+":"+n,i=a.get(o);return i||(i=new Intl.PluralRules(this.locale,{type:n}),a.set(o,i)),"function"==typeof(r=t[i.select(e)]||t.other)?r():r}number(e){let t=u.get(this.locale);return t||(t=new Intl.NumberFormat(this.locale),u.set(this.locale,t)),t.format(e)}select(e,t){let n=e[t]||e.other;return"function"==typeof n?n():n}constructor(e,t){this.locale=e,this.strings=t}}var d=n(9585);let f=new WeakMap;function h(e,t){let n,{locale:r}=(0,o.Y)(),i=t&&s.getGlobalDictionaryForPackage(t)||((n=f.get(e))||(n=new s(e),f.set(e,n)),n);return(0,d.useMemo)(()=>new c(r,i),[r,i])}},32065:(e,t,n)=>{n.d(t,{o:()=>r});function r(e,t){if(!e)return!1;let n=window.getComputedStyle(e),r=/(auto|scroll)/.test(n.overflow+n.overflowX+n.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}},36458:(e,t,n)=>{n.d(t,{h:()=>i});let r=new WeakMap,o=[];function i(e,t=document.body){let n=new Set(e),l=new Set,s=e=>{for(let t of e.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))n.add(t);let t=e=>{if(n.has(e)||e.parentElement&&l.has(e.parentElement)&&"row"!==e.parentElement.getAttribute("role"))return NodeFilter.FILTER_REJECT;for(let t of n)if(e.contains(t))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:t}),o=t(e);if(o===NodeFilter.FILTER_ACCEPT&&a(e),o!==NodeFilter.FILTER_REJECT){let e=r.nextNode();for(;null!=e;)a(e),e=r.nextNode()}},a=e=>{var t;let n=null!=(t=r.get(e))?t:0;("true"!==e.getAttribute("aria-hidden")||0!==n)&&(0===n&&e.setAttribute("aria-hidden","true"),l.add(e),r.set(e,n+1))};o.length&&o[o.length-1].disconnect(),s(t);let u=new MutationObserver(e=>{for(let t of e)if("childList"===t.type&&0!==t.addedNodes.length&&![...n,...l].some(e=>e.contains(t.target))){for(let e of t.removedNodes)e instanceof Element&&(n.delete(e),l.delete(e));for(let e of t.addedNodes)(e instanceof HTMLElement||e instanceof SVGElement)&&("true"===e.dataset.liveAnnouncer||"true"===e.dataset.reactAriaTopLayer)?n.add(e):e instanceof Element&&s(e)}});u.observe(t,{childList:!0,subtree:!0});let c={visibleNodes:n,hiddenNodes:l,observe(){u.observe(t,{childList:!0,subtree:!0})},disconnect(){u.disconnect()}};return o.push(c),()=>{for(let e of(u.disconnect(),l)){let t=r.get(e);null!=t&&(1===t?(e.removeAttribute("aria-hidden"),r.delete(e)):r.set(e,t-1))}c===o[o.length-1]?(o.pop(),o.length&&o[o.length-1].observe()):o.splice(o.indexOf(c),1)}}},37136:(e,t,n)=>{n.d(t,{R:()=>q});var r={},o={},i={},l={},s={},a={},u={},c={},d={},f={},h={},p={},m={},v={},g={},E={},N={},b={},w={},T={},y={},R={},S={},C={},L={},k={},F={},x={},_={},D={},P={},I={},M={},O={},A={};A={"ar-AE":{dismiss:`\u{62A}\u{62C}\u{627}\u{647}\u{644}`},"bg-BG":{dismiss:`\u{41E}\u{442}\u{445}\u{432}\u{44A}\u{440}\u{43B}\u{44F}\u{43D}\u{435}`},"cs-CZ":{dismiss:"Odstranit"},"da-DK":{dismiss:"Luk"},"de-DE":{dismiss:`Schlie\xdfen`},"el-GR":{dismiss:`\u{391}\u{3C0}\u{3CC}\u{3C1}\u{3C1}\u{3B9}\u{3C8}\u{3B7}`},"en-US":{dismiss:"Dismiss"},"es-ES":{dismiss:"Descartar"},"et-EE":{dismiss:`L\xf5peta`},"fi-FI":{dismiss:`Hylk\xe4\xe4`},"fr-FR":{dismiss:"Rejeter"},"he-IL":{dismiss:`\u{5D4}\u{5EA}\u{5E2}\u{5DC}\u{5DD}`},"hr-HR":{dismiss:"Odbaci"},"hu-HU":{dismiss:`Elutas\xedt\xe1s`},"it-IT":{dismiss:"Ignora"},"ja-JP":{dismiss:`\u{9589}\u{3058}\u{308B}`},"ko-KR":{dismiss:`\u{BB34}\u{C2DC}`},"lt-LT":{dismiss:"Atmesti"},"lv-LV":{dismiss:`Ner\u{101}d\u{12B}t`},"nb-NO":{dismiss:"Lukk"},"nl-NL":{dismiss:"Negeren"},"pl-PL":{dismiss:"Zignoruj"},"pt-BR":{dismiss:"Descartar"},"pt-PT":{dismiss:"Dispensar"},"ro-RO":{dismiss:"Revocare"},"ru-RU":{dismiss:`\u{41F}\u{440}\u{43E}\u{43F}\u{443}\u{441}\u{442}\u{438}\u{442}\u{44C}`},"sk-SK":{dismiss:`Zru\u{161}i\u{165}`},"sl-SI":{dismiss:"Opusti"},"sr-SP":{dismiss:"Odbaci"},"sv-SE":{dismiss:"Avvisa"},"tr-TR":{dismiss:"Kapat"},"uk-UA":{dismiss:`\u{421}\u{43A}\u{430}\u{441}\u{443}\u{432}\u{430}\u{442}\u{438}`},"zh-CN":{dismiss:`\u{53D6}\u{6D88}`},"zh-TW":{dismiss:`\u{95DC}\u{9589}`}};var W=n(9585),H=n(74116),K=n(28617),B=n(56140);function q(e){var t;let{onDismiss:n,...r}=e,o=(0,K.o)((t=A)&&t.__esModule?t.default:t,"@react-aria/overlays"),i=(0,H.b)(r,o.format("dismiss"));return W.createElement(B.s,null,W.createElement("button",{...i,tabIndex:-1,onClick:()=>{n&&n()},style:{width:1,height:1}}))}},37240:(e,t,n)=>{n.d(t,{e:()=>c});var r=n(72625),o=n(9585),i=n(7672),l=n(20965);function s(e,t){if(e.button>0)return!1;if(e.target){let t=e.target.ownerDocument;if(!t||!t.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return!!t.current&&!e.composedPath().includes(t.current)}var a=n(74476);let u=[];function c(e,t){let{onClose:n,shouldCloseOnBlur:c,isOpen:d,isDismissable:f=!1,isKeyboardDismissDisabled:h=!1,shouldCloseOnInteractOutside:p}=e;(0,o.useEffect)(()=>{if(d&&!u.includes(t))return u.push(t),()=>{let e=u.indexOf(t);e>=0&&u.splice(e,1)}},[d,t]);let m=()=>{u[u.length-1]===t&&n&&n()};!function(e){let{ref:t,onInteractOutside:n,isDisabled:r,onInteractOutsideStart:a}=e,u=(0,o.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),c=(0,i.J)(e=>{n&&s(e,t)&&(a&&a(e),u.current.isPointerDown=!0)}),d=(0,i.J)(e=>{n&&n(e)});(0,o.useEffect)(()=>{let e=u.current;if(r)return;let n=t.current,o=(0,l.TW)(n);if("undefined"!=typeof PointerEvent){let n=n=>{e.isPointerDown&&s(n,t)&&d(n),e.isPointerDown=!1};return o.addEventListener("pointerdown",c,!0),o.addEventListener("click",n,!0),()=>{o.removeEventListener("pointerdown",c,!0),o.removeEventListener("click",n,!0)}}},[t,r,c,d])}({ref:t,onInteractOutside:f&&d?e=>{(!p||p(e.target))&&(u[u.length-1]===t&&(e.stopPropagation(),e.preventDefault()),m())}:void 0,onInteractOutsideStart:e=>{(!p||p(e.target))&&u[u.length-1]===t&&(e.stopPropagation(),e.preventDefault())}});let{focusWithinProps:v}=(0,a.R)({isDisabled:!c,onBlurWithin:e=>{!(!e.relatedTarget||(0,r.Pu)(e.relatedTarget))&&(!p||p(e.relatedTarget))&&(null==n||n())}});return{overlayProps:{onKeyDown:e=>{"Escape"!==e.key||h||e.nativeEvent.isComposing||(e.stopPropagation(),e.preventDefault(),m())},...v},underlayProps:{onPointerDown:e=>{e.target===e.currentTarget&&e.preventDefault()}}}}},56140:(e,t,n)=>{n.d(t,{B:()=>s,s:()=>a});var r=n(96539),o=n(9585),i=n(74476);let l={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function s(e={}){let{style:t,isFocusable:n}=e,[r,a]=(0,o.useState)(!1),{focusWithinProps:u}=(0,i.R)({isDisabled:!n,onFocusWithinChange:e=>a(e)}),c=(0,o.useMemo)(()=>r?t:t?{...l,...t}:l,[r]);return{visuallyHiddenProps:{...u,style:c}}}function a(e){let{children:t,elementType:n="div",isFocusable:i,style:l,...a}=e,{visuallyHiddenProps:u}=s(e);return o.createElement(n,(0,r.v)(a,u),t)}},58831:(e,t,n)=>{n.d(t,{xf:()=>r,zF:()=>o});var r={ease:[.36,.66,.4,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1],easeInOut:[.4,0,.2,1],spring:[.155,1.105,.295,1.12],springOut:[.57,-.15,.62,.07],softSpring:[.16,1.11,.3,1.02]};r.easeOut,r.easeIn;var o={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}},scaleSpringOpacity:{initial:{opacity:0,transform:"scale(0.8)"},enter:{opacity:1,transform:"scale(1)",transition:{type:"spring",bounce:0,duration:.3}},exit:{opacity:0,transform:"scale(0.96)",transition:{type:"easeOut",bounce:0,duration:.15}}},scale:{enter:{scale:1},exit:{scale:.95}},scaleFadeIn:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.25,ease:r.easeIn}},exit:{transform:"scale(0.95)",opacity:0,transition:{duration:.2,ease:r.easeOut}}},scaleInOut:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.4,ease:r.ease}},exit:{transform:"scale(1.03)",opacity:0,transition:{duration:.3,ease:r.ease}}},fade:{enter:{opacity:1,transition:{duration:.4,ease:r.ease}},exit:{opacity:0,transition:{duration:.3,ease:r.ease}}},collapse:{enter:{opacity:1,height:"auto",transition:{height:{type:"spring",bounce:0,duration:.3},opacity:{easings:"ease",duration:.4}}},exit:{opacity:0,height:0,transition:{easings:"ease",duration:.3}}}}},65481:(e,t,n)=>{n.d(t,{gX:()=>i});var r=n(9585);let o=(0,r.createContext)({});function i(){var e;return null!=(e=(0,r.useContext)(o))?e:{}}},66902:(e,t,n)=>{n.d(t,{hJ:()=>f,Se:()=>h});var r=n(65481),o=n(80029),i=n(9585);function l({children:e}){let t=(0,i.useMemo)(()=>({register:()=>{}}),[]);return i.createElement(o.F.Provider,{value:t},e)}var s=n(72625),a=n(23220),u=n(37285),c=n(73360);let d=i.createContext(null);function f(e){let t=(0,u.wR)(),{portalContainer:n=t?null:document.body,isExiting:o}=e,[c,f]=(0,i.useState)(!1),h=(0,i.useMemo)(()=>({contain:c,setContain:f}),[c,f]),{getContainer:p}=(0,r.gX)();if(!e.portalContainer&&p&&(n=p()),!n)return null;let m=e.children;return e.disableFocusManagement||(m=i.createElement(s.n1,{restoreFocus:!0,contain:(e.shouldContainFocus||c)&&!o},m)),m=i.createElement(d.Provider,{value:h},i.createElement(l,null,m)),a.createPortal(m,n)}function h(){let e=(0,i.useContext)(d),t=null==e?void 0:e.setContain;(0,c.N)(()=>{null==t||t(!0)},[t])}},67266:(e,t,n)=>{function r(...e){return 1===e.length&&e[0]?e[0]:t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||(n="function"==typeof r),r});if(n)return()=>{r.forEach((t,n)=>{"function"==typeof t?t():o(e[n],null)})}}}function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}n.d(t,{P:()=>r})},72067:(e,t,n)=>{n.d(t,{m:()=>o});var r=n(32065);function o(e,t){let n=e;for((0,r.o)(n,t)&&(n=n.parentElement);n&&!(0,r.o)(n,t);)n=n.parentElement;return n||document.scrollingElement||document.documentElement}},72625:(e,t,n)=>{n.d(t,{n1:()=>v,N$:()=>k,Pu:()=>w});var r=n(20965),o=n(73360),i=n(50689),l=n(60699),s=n(37260),a=n(36847);class u{get currentNode(){return this._currentNode}set currentNode(e){if(!(0,i.sD)(this.root,e))throw Error("Cannot set currentNode to a node that is not contained by the root node.");let t=[],n=e,r=e;for(this._currentNode=e;n&&n!==this.root;)if(n.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let e=n,o=this._doc.createTreeWalker(e,this.whatToShow,{acceptNode:this._acceptNode});t.push(o),o.currentNode=r,this._currentSetFor.add(o),n=r=e.host}else n=n.parentNode;let o=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});t.push(o),o.currentNode=r,this._currentSetFor.add(o),this._walkerStack=t}get doc(){return this._doc}firstChild(){let e=this.currentNode,t=this.nextNode();return(0,i.sD)(e,t)?(t&&(this.currentNode=t),t):(this.currentNode=e,null)}lastChild(){let e=this._walkerStack[0].lastChild();return e&&(this.currentNode=e),e}nextNode(){let e=this._walkerStack[0].nextNode();if(e){if(e.shadowRoot){var t;let n;if("function"==typeof this.filter?n=this.filter(e):(null==(t=this.filter)?void 0:t.acceptNode)&&(n=this.filter.acceptNode(e)),n===NodeFilter.FILTER_ACCEPT)return this.currentNode=e,e;let r=this.nextNode();return r&&(this.currentNode=r),r}return e&&(this.currentNode=e),e}if(!(this._walkerStack.length>1))return null;{this._walkerStack.shift();let e=this.nextNode();return e&&(this.currentNode=e),e}}previousNode(){let e=this._walkerStack[0];if(e.currentNode===e.root){if(this._currentSetFor.has(e)&&(this._currentSetFor.delete(e),this._walkerStack.length>1)){this._walkerStack.shift();let e=this.previousNode();return e&&(this.currentNode=e),e}return null}let t=e.previousNode();if(t){if(t.shadowRoot){var n;let e;if("function"==typeof this.filter?e=this.filter(t):(null==(n=this.filter)?void 0:n.acceptNode)&&(e=this.filter.acceptNode(t)),e===NodeFilter.FILTER_ACCEPT)return t&&(this.currentNode=t),t;let r=this.lastChild();return r&&(this.currentNode=r),r}return t&&(this.currentNode=t),t}if(!(this._walkerStack.length>1))return null;{this._walkerStack.shift();let e=this.previousNode();return e&&(this.currentNode=e),e}}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(e,t,n,r){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=e=>{if(e.nodeType===Node.ELEMENT_NODE){var t;let n=e.shadowRoot;if(n){let e=this._doc.createTreeWalker(n,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(e),NodeFilter.FILTER_ACCEPT}if("function"==typeof this.filter)return this.filter(e);if(null==(t=this.filter)?void 0:t.acceptNode)return this.filter.acceptNode(e);if(null===this.filter)return NodeFilter.FILTER_ACCEPT}return NodeFilter.FILTER_SKIP},this._doc=e,this.root=t,this.filter=null!=r?r:null,this.whatToShow=null!=n?n:NodeFilter.SHOW_ALL,this._currentNode=t,this._walkerStack.unshift(e.createTreeWalker(t,n,this._acceptNode));let o=t.shadowRoot;if(o){let e=this._doc.createTreeWalker(o,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(e)}}}var c=n(99275),d=n(84702),f=n(9585);let h=f.createContext(null),p="react-aria-focus-scope-restore",m=null;function v(e){var t,n,s,a,u;let d,v,{children:w,contain:F,restoreFocus:D,autoFocus:P}=e,I=(0,f.useRef)(null),M=(0,f.useRef)(null),O=(0,f.useRef)([]),{parentNode:A}=(0,f.useContext)(h)||{},W=(0,f.useMemo)(()=>new x({scopeRef:O}),[O]);(0,o.N)(()=>{let e=A||_.root;if(_.getTreeNode(e.scopeRef)&&m&&!T(m,e.scopeRef)){let t=_.getTreeNode(m);t&&(e=t)}e.addChild(W),_.addNode(W)},[W,A]),(0,o.N)(()=>{let e=_.getTreeNode(O);e&&(e.contain=!!F)},[F]),(0,o.N)(()=>{var e;let t=null==(e=I.current)?void 0:e.nextSibling,n=[],r=e=>e.stopPropagation();for(;t&&t!==M.current;)n.push(t),t.addEventListener(p,r),t=t.nextSibling;return O.current=n,()=>{for(let e of n)e.removeEventListener(p,r)}},[w]),t=O,n=D,s=F,(0,o.N)(()=>{if(n||s)return;let e=t.current,o=(0,r.TW)(e?e[0]:void 0),l=e=>{let n=(0,i.wt)(e);N(n,t.current)?m=t:b(n)||(m=null)};return o.addEventListener("focusin",l,!1),null==e||e.forEach(e=>e.addEventListener("focusin",l,!1)),()=>{o.removeEventListener("focusin",l,!1),null==e||e.forEach(e=>e.removeEventListener("focusin",l,!1))}},[t,n,s]),a=O,u=F,d=(0,f.useRef)(void 0),v=(0,f.useRef)(void 0),(0,o.N)(()=>{let e=a.current;if(!u){v.current&&(cancelAnimationFrame(v.current),v.current=void 0);return}let t=(0,r.TW)(e?e[0]:void 0),n=e=>{if("Tab"!==e.key||e.altKey||e.ctrlKey||e.metaKey||!E(a)||e.isComposing)return;let n=(0,i.bq)(t),r=a.current;if(!r||!N(n,r))return;let o=k(g(r),{tabbable:!0},r);if(!n)return;o.currentNode=n;let l=e.shiftKey?o.previousNode():o.nextNode();l||(o.currentNode=e.shiftKey?r[r.length-1].nextElementSibling:r[0].previousElementSibling,l=e.shiftKey?o.previousNode():o.nextNode()),e.preventDefault(),l&&y(l,!0)},o=e=>{(!m||T(m,a))&&N((0,i.wt)(e),a.current)?(m=a,d.current=(0,i.wt)(e)):E(a)&&!b((0,i.wt)(e),a)?d.current?d.current.focus():m&&m.current&&S(m.current):E(a)&&(d.current=(0,i.wt)(e))},s=e=>{v.current&&cancelAnimationFrame(v.current),v.current=requestAnimationFrame(()=>{let n=(0,c.ME)(),r=("virtual"===n||null===n)&&(0,l.m0)()&&(0,l.H8)(),o=(0,i.bq)(t);if(!r&&o&&E(a)&&!b(o,a)){m=a;let t=(0,i.wt)(e);if(t&&t.isConnected){var s;d.current=t,null==(s=d.current)||s.focus()}else m.current&&S(m.current)}})};return t.addEventListener("keydown",n,!1),t.addEventListener("focusin",o,!1),null==e||e.forEach(e=>e.addEventListener("focusin",o,!1)),null==e||e.forEach(e=>e.addEventListener("focusout",s,!1)),()=>{t.removeEventListener("keydown",n,!1),t.removeEventListener("focusin",o,!1),null==e||e.forEach(e=>e.removeEventListener("focusin",o,!1)),null==e||e.forEach(e=>e.removeEventListener("focusout",s,!1))}},[a,u]),(0,o.N)(()=>()=>{v.current&&cancelAnimationFrame(v.current)},[v]),function(e,t,n){let l=(0,f.useRef)("undefined"!=typeof document?(0,i.bq)((0,r.TW)(e.current?e.current[0]:void 0)):null);(0,o.N)(()=>{let o=e.current,l=(0,r.TW)(o?o[0]:void 0);if(!t||n)return;let s=()=>{(!m||T(m,e))&&N((0,i.bq)(l),e.current)&&(m=e)};return l.addEventListener("focusin",s,!1),null==o||o.forEach(e=>e.addEventListener("focusin",s,!1)),()=>{l.removeEventListener("focusin",s,!1),null==o||o.forEach(e=>e.removeEventListener("focusin",s,!1))}},[e,n]),(0,o.N)(()=>{let o=(0,r.TW)(e.current?e.current[0]:void 0);if(!t)return;let i=t=>{if("Tab"!==t.key||t.altKey||t.ctrlKey||t.metaKey||!E(e)||t.isComposing)return;let n=o.activeElement;if(!b(n,e)||!C(e))return;let r=_.getTreeNode(e);if(!r)return;let i=r.nodeToRestore,l=k(o.body,{tabbable:!0});l.currentNode=n;let s=t.shiftKey?l.previousNode():l.nextNode();if(i&&i.isConnected&&i!==o.body||(i=void 0,r.nodeToRestore=void 0),(!s||!b(s,e))&&i){l.currentNode=i;do s=t.shiftKey?l.previousNode():l.nextNode();while(b(s,e));(t.preventDefault(),t.stopPropagation(),s)?y(s,!0):b(i)?y(i,!0):n.blur()}};return n||o.addEventListener("keydown",i,!0),()=>{n||o.removeEventListener("keydown",i,!0)}},[e,t,n]),(0,o.N)(()=>{var n;let o=(0,r.TW)(e.current?e.current[0]:void 0);if(!t)return;let s=_.getTreeNode(e);if(s)return s.nodeToRestore=null!=(n=l.current)?n:void 0,()=>{let n=_.getTreeNode(e);if(!n)return;let r=n.nodeToRestore,l=(0,i.bq)(o);if(t&&r&&(l&&b(l,e)||l===o.body&&C(e))){let t=_.clone();requestAnimationFrame(()=>{if(o.activeElement===o.body){let n=t.getTreeNode(e);for(;n;){if(n.nodeToRestore&&n.nodeToRestore.isConnected)return void L(n.nodeToRestore);n=n.parent}for(n=t.getTreeNode(e);n;){if(n.scopeRef&&n.scopeRef.current&&_.getTreeNode(n.scopeRef))return void L(R(n.scopeRef.current,!0));n=n.parent}}})}}},[e,t])}(O,D,F),function(e,t){let n=f.useRef(t);(0,f.useEffect)(()=>{if(n.current){m=e;let t=(0,r.TW)(e.current?e.current[0]:void 0);!N((0,i.bq)(t),m.current)&&e.current&&S(e.current)}n.current=!1},[e])}(O,P),(0,f.useEffect)(()=>{let e=(0,i.bq)((0,r.TW)(O.current?O.current[0]:void 0)),t=null;if(N(e,O.current)){for(let n of _.traverse())n.scopeRef&&N(e,n.scopeRef.current)&&(t=n);t===_.getTreeNode(O)&&(m=t.scopeRef)}},[O]),(0,o.N)(()=>()=>{var e,t,n;let r=null!=(n=null==(t=_.getTreeNode(O))||null==(e=t.parent)?void 0:e.scopeRef)?n:null;(O===m||T(O,m))&&(!r||_.getTreeNode(r))&&(m=r),_.removeTreeNode(O)},[O]);let H=(0,f.useMemo)(()=>{var e;return e=O,{focusNext(t={}){var n;let o=e.current,{from:l,tabbable:s,wrap:a,accept:u}=t,c=l||(0,i.bq)((0,r.TW)(null!=(n=o[0])?n:void 0)),d=o[0].previousElementSibling,f=k(g(o),{tabbable:s,accept:u},o);f.currentNode=N(c,o)?c:d;let h=f.nextNode();return!h&&a&&(f.currentNode=d,h=f.nextNode()),h&&y(h,!0),h},focusPrevious(t={}){var n;let o=e.current,{from:l,tabbable:s,wrap:a,accept:u}=t,c=l||(0,i.bq)((0,r.TW)(null!=(n=o[0])?n:void 0)),d=o[o.length-1].nextElementSibling,f=k(g(o),{tabbable:s,accept:u},o);f.currentNode=N(c,o)?c:d;let h=f.previousNode();return!h&&a&&(f.currentNode=d,h=f.previousNode()),h&&y(h,!0),h},focusFirst(t={}){let n=e.current,{tabbable:r,accept:o}=t,i=k(g(n),{tabbable:r,accept:o},n);i.currentNode=n[0].previousElementSibling;let l=i.nextNode();return l&&y(l,!0),l},focusLast(t={}){let n=e.current,{tabbable:r,accept:o}=t,i=k(g(n),{tabbable:r,accept:o},n);i.currentNode=n[n.length-1].nextElementSibling;let l=i.previousNode();return l&&y(l,!0),l}}},[]),K=(0,f.useMemo)(()=>({focusManager:H,parentNode:W}),[W,H]);return f.createElement(h.Provider,{value:K},f.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:I}),w,f.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:M}))}function g(e){return e[0].parentElement}function E(e){let t=_.getTreeNode(m);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function N(e,t){return!!e&&!!t&&t.some(t=>t.contains(e))}function b(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:n}of _.traverse(_.getTreeNode(t)))if(n&&N(e,n.current))return!0;return!1}function w(e){return b(e,m)}function T(e,t){var n;let r=null==(n=_.getTreeNode(t))?void 0:n.parent;for(;r;){if(r.scopeRef===e)return!0;r=r.parent}return!1}function y(e,t=!1){if(null==e||t){if(null!=e)try{e.focus()}catch{}}else try{(0,d.l)(e)}catch{}}function R(e,t=!0){let n=e[0].previousElementSibling,r=g(e),o=k(r,{tabbable:t},e);o.currentNode=n;let i=o.nextNode();return t&&!i&&((o=k(r=g(e),{tabbable:!1},e)).currentNode=n,i=o.nextNode()),i}function S(e,t=!0){y(R(e,t))}function C(e){let t=_.getTreeNode(m);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return(null==t?void 0:t.scopeRef)===e}function L(e){e.dispatchEvent(new CustomEvent(p,{bubbles:!0,cancelable:!0}))&&y(e)}function k(e,t,n){var o,i,l;let c=(null==t?void 0:t.tabbable)?s.A:s.t,d=(null==e?void 0:e.nodeType)===Node.ELEMENT_NODE?e:null,f=(0,r.TW)(d),h=(o=e||f,i=NodeFilter.SHOW_ELEMENT,l={acceptNode(e){var o;return(null==t||null==(o=t.from)?void 0:o.contains(e))?NodeFilter.FILTER_REJECT:c(e)&&function e(t,n){return"#comment"!==t.nodeName&&function(e){let t=(0,r.mD)(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:n,visibility:o}=e.style,i="none"!==n&&"hidden"!==o&&"collapse"!==o;if(i){let{getComputedStyle:t}=e.ownerDocument.defaultView,{display:n,visibility:r}=t(e);i="none"!==n&&"hidden"!==r&&"collapse"!==r}return i}(t)&&!t.hasAttribute("hidden")&&!t.hasAttribute("data-react-aria-prevent-focus")&&("DETAILS"!==t.nodeName||!n||"SUMMARY"===n.nodeName||t.hasAttribute("open"))&&(!t.parentElement||e(t.parentElement,t))}(e)&&(!n||N(e,n))&&(!(null==t?void 0:t.accept)||t.accept(e))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}},(0,a.Nf)()?new u(f,o,i,l):f.createTreeWalker(o,i,l));return(null==t?void 0:t.from)&&(h.currentNode=t.from),h}class F{get size(){return this.fastMap.size}getTreeNode(e){return this.fastMap.get(e)}addTreeNode(e,t,n){let r=this.fastMap.get(null!=t?t:null);if(!r)return;let o=new x({scopeRef:e});r.addChild(o),o.parent=r,this.fastMap.set(e,o),n&&(o.nodeToRestore=n)}addNode(e){this.fastMap.set(e.scopeRef,e)}removeTreeNode(e){if(null===e)return;let t=this.fastMap.get(e);if(!t)return;let n=t.parent;for(let e of this.traverse())e!==t&&t.nodeToRestore&&e.nodeToRestore&&t.scopeRef&&t.scopeRef.current&&N(e.nodeToRestore,t.scopeRef.current)&&(e.nodeToRestore=t.nodeToRestore);let r=t.children;n&&(n.removeChild(t),r.size>0&&r.forEach(e=>n&&n.addChild(e))),this.fastMap.delete(t.scopeRef)}*traverse(e=this.root){if(null!=e.scopeRef&&(yield e),e.children.size>0)for(let t of e.children)yield*this.traverse(t)}clone(){var e,t;let n=new F;for(let r of this.traverse())n.addTreeNode(r.scopeRef,null!=(t=null==(e=r.parent)?void 0:e.scopeRef)?t:null,r.nodeToRestore);return n}constructor(){this.fastMap=new Map,this.root=new x({scopeRef:null}),this.fastMap.set(null,this.root)}}class x{addChild(e){this.children.add(e),e.parent=this}removeChild(e){this.children.delete(e),e.parent=void 0}constructor(e){this.children=new Set,this.contain=!1,this.scopeRef=e.scopeRef}}let _=new F},81077:(e,t,n)=>{n.d(t,{s:()=>a});var r=n(24215),o=n(89826),i=n(84702),l=n(9585),s=n(66902);function a(e,t){let{role:n="dialog"}=e,a=(0,r.X1)();a=e["aria-label"]?void 0:a;let u=(0,l.useRef)(!1);return(0,l.useEffect)(()=>{if(t.current&&!t.current.contains(document.activeElement)){(0,i.l)(t.current);let e=setTimeout(()=>{document.activeElement===t.current&&(u.current=!0,t.current&&(t.current.blur(),(0,i.l)(t.current)),u.current=!1)},500);return()=>{clearTimeout(e)}}},[t]),(0,s.Se)(),{dialogProps:{...(0,o.$)(e,{labelable:!0}),role:n,tabIndex:-1,"aria-labelledby":e["aria-labelledby"]||a,onBlur:e=>{u.current&&e.stopPropagation()}},titleProps:{id:a}}}},90322:(e,t,n)=>{n.d(t,{T:()=>i});var r=n(9585),o=n(47314);function i(e){let[t,n]=(0,o.P)(e.isOpen,e.defaultOpen||!1,e.onOpenChange),i=(0,r.useCallback)(()=>{n(!0)},[n]),l=(0,r.useCallback)(()=>{n(!1)},[n]),s=(0,r.useCallback)(()=>{n(!t)},[n,t]);return{isOpen:t,setOpen:n,open:i,close:l,toggle:s}}},93482:(e,t,n)=>{let r;n.d(t,{H:()=>d});var o=n(73360),i=n(60699),l=n(26423),s=n(72067);let a="undefined"!=typeof document&&window.visualViewport,u=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),c=0;function d(e={}){let{isDisabled:t}=e;(0,o.N)(()=>{if(!t){let e,t,n,o,d,m;return 1==++c&&(r=(0,i.un)()?(n=null,o=()=>{if(n)return;let e=window.pageXOffset,t=window.pageYOffset;n=(0,l.c)(h(window,"scroll",()=>{window.scrollTo(0,0)}),f(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),f(document.documentElement,"overflow","hidden"),f(document.body,"marginTop",`-${t}px`),()=>{window.scrollTo(e,t)}),window.scrollTo(0,0)},d=(0,l.c)(h(document,"touchstart",n=>{((e=(0,s.m)(n.target,!0))!==document.documentElement||e!==document.body)&&e instanceof HTMLElement&&"auto"===window.getComputedStyle(e).overscrollBehavior&&(t=f(e,"overscrollBehavior","contain"))},{passive:!1,capture:!0}),h(document,"touchmove",t=>{if(!e||e===document.documentElement||e===document.body)return void t.preventDefault();e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&t.preventDefault()},{passive:!1,capture:!0}),h(document,"touchend",()=>{t&&t()},{passive:!1,capture:!0}),h(document,"focus",e=>{var t;let n=e.target;((t=n)instanceof HTMLInputElement&&!u.has(t.type)||t instanceof HTMLTextAreaElement||t instanceof HTMLElement&&t.isContentEditable)&&(o(),n.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{n.style.transform="",a&&(a.height<window.innerHeight?requestAnimationFrame(()=>{p(n)}):a.addEventListener("resize",()=>p(n),{once:!0}))}))},!0)),()=>{null==t||t(),null==n||n(),d()}):(m=window.innerWidth-document.documentElement.clientWidth,(0,l.c)(m>0&&("scrollbarGutter"in document.documentElement.style?f(document.documentElement,"scrollbarGutter","stable"):f(document.documentElement,"paddingRight",`${m}px`)),f(document.documentElement,"overflow","hidden")))),()=>{0==--c&&r()}}},[t])}function f(e,t,n){let r=e.style[t];return e.style[t]=n,()=>{e.style[t]=r}}function h(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function p(e){let t=document.scrollingElement||document.documentElement,n=e;for(;n&&n!==t;){let e=(0,s.m)(n);if(e!==document.documentElement&&e!==document.body&&e!==n){let t=e.getBoundingClientRect().top,r=n.getBoundingClientRect().top;r>t+n.clientHeight&&(e.scrollTop+=r-t)}n=e.parentElement}}}}]);