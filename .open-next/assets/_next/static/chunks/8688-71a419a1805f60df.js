"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8688],{13451:(e,t,l)=>{l.d(t,{y:()=>b});var n=l(64636),r=l(81298),i=l(56542),s=l(71201),o=l(61133),u=l(7672),c=l(9585);function a(e,t,l,n){let r=(0,u.J)(l),i=null==l;(0,c.useEffect)(()=>{if(i||!e.current)return;let l=e.current;return l.addEventListener(t,r,n),()=>{l.removeEventListener(t,r,n)}},[e,t,n,i,r])}var d=l(33998),y=l(73360);function f(e,t){let l=(0,c.useRef)(!0),n=(0,c.useRef)(null);(0,y.N)(()=>(l.current=!0,()=>{l.current=!1}),[]),(0,y.N)(()=>{l.current?l.current=!1:(!n.current||t.some((e,t)=>!Object.is(e,n[t])))&&e(),n.current=t},t)}var h=l(96539),p=l(23220),g=l(99275),v=l(84702),K=l(72625),m=l(72254),S=l(3351);function b(e){let t,{selectionManager:l,keyboardDelegate:y,ref:b,autoFocus:k=!1,shouldFocusWrap:w=!1,disallowEmptySelection:F=!1,disallowSelectAll:E=!1,escapeKeyBehavior:T="clearSelection",selectOnFocus:I="replace"===l.selectionBehavior,disallowTypeAhead:M=!1,shouldUseVirtualFocus:P,allowsTabNavigation:D=!1,isVirtualized:x,scrollRef:C=b,linkBehavior:L="action"}=e,{direction:B}=(0,S.Y)(),N=(0,i.rd)(),R=(0,c.useRef)({top:0,left:0});a(C,"scroll",x?void 0:()=>{var e,t,l,n;R.current={top:null!=(l=null==(e=C.current)?void 0:e.scrollTop)?l:0,left:null!=(n=null==(t=C.current)?void 0:t.scrollLeft)?n:0}});let A=(0,c.useRef)(!1);a(b,"react-aria-focus",P?e=>{let{detail:t}=e;e.stopPropagation(),l.setFocused(!0),(null==t?void 0:t.focusStrategy)==="first"&&(A.current=!0)}:void 0);let $=(0,u.J)(()=>{var e,t;let n=null!=(t=null==(e=y.getFirstKey)?void 0:e.call(y))?t:null;null==n?((0,m.vX)(b.current),l.collection.size>0&&(A.current=!1)):(l.setFocusedKey(n),A.current=!1)});f(()=>{A.current&&$()},[l.collection,$]);let O=(0,u.J)(()=>{l.collection.size>0&&(A.current=!1)});f(()=>{O()},[l.focusedKey,O]),a(b,"react-aria-clear-focus",P?e=>{var t;e.stopPropagation(),l.setFocused(!1),(null==(t=e.detail)?void 0:t.clearFocusKey)&&l.setFocusedKey(null)}:void 0);let z=(0,c.useRef)(k),V=(0,c.useRef)(!1);(0,c.useEffect)(()=>{if(z.current){var e,t,n,r;let i=null;"first"===k&&(i=null!=(n=null==(e=y.getFirstKey)?void 0:e.call(y))?n:null),"last"===k&&(i=null!=(r=null==(t=y.getLastKey)?void 0:t.call(y))?r:null);let s=l.selectedKeys;if(s.size){for(let e of s)if(l.canSelectItem(e)){i=e;break}}l.setFocused(!0),l.setFocusedKey(i),null==i&&!P&&b.current&&(0,v.l)(b.current),l.collection.size>0&&(z.current=!1,V.current=!0)}});let W=(0,c.useRef)(l.focusedKey),U=(0,c.useRef)(null);(0,c.useEffect)(()=>{if(l.isFocused&&null!=l.focusedKey&&(l.focusedKey!==W.current||V.current)&&C.current&&b.current){let e=(0,g.ME)(),t=(0,n.au)(b,l.focusedKey);if(!(t instanceof HTMLElement))return;("keyboard"===e||V.current)&&(U.current&&cancelAnimationFrame(U.current),U.current=requestAnimationFrame(()=>{C.current&&((0,d.R)(C.current,t),"virtual"!==e&&(0,d.o)(t,{containingElement:b.current}))}))}!P&&l.isFocused&&null==l.focusedKey&&null!=W.current&&b.current&&(0,v.l)(b.current),W.current=l.focusedKey,V.current=!1}),(0,c.useEffect)(()=>()=>{U.current&&cancelAnimationFrame(U.current)},[]),a(b,"react-aria-focus-scope-restore",e=>{e.preventDefault(),l.setFocused(!0)});let _={onKeyDown:e=>{var t,r,i,u,c,a,d,f,h,g,v,m,S;if(e.altKey&&"Tab"===e.key&&e.preventDefault(),!(null==(t=b.current)?void 0:t.contains(e.target)))return;let k=(t,r)=>{if(null!=t){if(l.isLink(t)&&"selection"===L&&I&&!(0,n.N9)(e)){(0,p.flushSync)(()=>{l.setFocusedKey(t,r)});let i=(0,n.au)(b,t),s=l.getItemProps(t);i&&N.open(i,e,s.href,s.routerOptions);return}l.setFocusedKey(t,r),l.isLink(t)&&"override"===L||(e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):I&&!(0,n.N9)(e)&&l.replaceSelection(t))}};switch(e.key){case"ArrowDown":if(y.getKeyBelow){let t=null!=l.focusedKey?null==(r=y.getKeyBelow)?void 0:r.call(y,l.focusedKey):null==(i=y.getFirstKey)?void 0:i.call(y);null==t&&w&&(t=null==(u=y.getFirstKey)?void 0:u.call(y,l.focusedKey)),null!=t&&(e.preventDefault(),k(t))}break;case"ArrowUp":if(y.getKeyAbove){let t=null!=l.focusedKey?null==(c=y.getKeyAbove)?void 0:c.call(y,l.focusedKey):null==(a=y.getLastKey)?void 0:a.call(y);null==t&&w&&(t=null==(d=y.getLastKey)?void 0:d.call(y,l.focusedKey)),null!=t&&(e.preventDefault(),k(t))}break;case"ArrowLeft":if(y.getKeyLeftOf){let t=null!=l.focusedKey?null==(f=y.getKeyLeftOf)?void 0:f.call(y,l.focusedKey):null;null==t&&w&&(t="rtl"===B?null==(h=y.getFirstKey)?void 0:h.call(y,l.focusedKey):null==(g=y.getLastKey)?void 0:g.call(y,l.focusedKey)),null!=t&&(e.preventDefault(),k(t,"rtl"===B?"first":"last"))}break;case"ArrowRight":if(y.getKeyRightOf){let t=null!=l.focusedKey?null==(v=y.getKeyRightOf)?void 0:v.call(y,l.focusedKey):null;null==t&&w&&(t="rtl"===B?null==(m=y.getLastKey)?void 0:m.call(y,l.focusedKey):null==(S=y.getFirstKey)?void 0:S.call(y,l.focusedKey)),null!=t&&(e.preventDefault(),k(t,"rtl"===B?"last":"first"))}break;case"Home":if(y.getFirstKey){if(null===l.focusedKey&&e.shiftKey)return;e.preventDefault();let t=y.getFirstKey(l.focusedKey,(0,s.B)(e));l.setFocusedKey(t),null!=t&&((0,s.B)(e)&&e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):I&&l.replaceSelection(t))}break;case"End":if(y.getLastKey){if(null===l.focusedKey&&e.shiftKey)return;e.preventDefault();let t=y.getLastKey(l.focusedKey,(0,s.B)(e));l.setFocusedKey(t),null!=t&&((0,s.B)(e)&&e.shiftKey&&"multiple"===l.selectionMode?l.extendSelection(t):I&&l.replaceSelection(t))}break;case"PageDown":if(y.getKeyPageBelow&&null!=l.focusedKey){let t=y.getKeyPageBelow(l.focusedKey);null!=t&&(e.preventDefault(),k(t))}break;case"PageUp":if(y.getKeyPageAbove&&null!=l.focusedKey){let t=y.getKeyPageAbove(l.focusedKey);null!=t&&(e.preventDefault(),k(t))}break;case"a":(0,s.B)(e)&&"multiple"===l.selectionMode&&!0!==E&&(e.preventDefault(),l.selectAll());break;case"Escape":"clearSelection"!==T||F||0===l.selectedKeys.size||(e.stopPropagation(),e.preventDefault(),l.clearSelection());break;case"Tab":if(!D)if(e.shiftKey)b.current.focus();else{let e,t,l=(0,K.N$)(b.current,{tabbable:!0});do(t=l.lastChild())&&(e=t);while(t);e&&!e.contains(document.activeElement)&&(0,o.e)(e)}}},onFocus:e=>{if(l.isFocused){e.currentTarget.contains(e.target)||l.setFocused(!1);return}if(e.currentTarget.contains(e.target)){if(l.setFocused(!0),null==l.focusedKey){var t,r,i,s;let n=e=>{null!=e&&(l.setFocusedKey(e),I&&!l.isSelected(e)&&l.replaceSelection(e))},o=e.relatedTarget;o&&e.currentTarget.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_FOLLOWING?n(null!=(i=l.lastSelectedKey)?i:null==(t=y.getLastKey)?void 0:t.call(y)):n(null!=(s=l.firstSelectedKey)?s:null==(r=y.getFirstKey)?void 0:r.call(y))}else!x&&C.current&&(C.current.scrollTop=R.current.top,C.current.scrollLeft=R.current.left);if(null!=l.focusedKey&&C.current){let e=(0,n.au)(b,l.focusedKey);e instanceof HTMLElement&&(e.contains(document.activeElement)||P||(0,o.e)(e),"keyboard"===(0,g.ME)()&&(0,d.o)(e,{containingElement:b.current}))}}},onBlur:e=>{e.currentTarget.contains(e.relatedTarget)||l.setFocused(!1)},onMouseDown(e){C.current===e.target&&e.preventDefault()}},{typeSelectProps:H}=(0,r.I)({keyboardDelegate:y,selectionManager:l});M||(_=(0,h.v)(H,_)),P||(t=null==l.focusedKey?0:-1);let G=(0,n.j5)(l.collection);return{collectionProps:(0,h.v)(_,{tabIndex:t,"data-collection":G})}}},33998:(e,t,l)=>{l.d(t,{R:()=>r,o:()=>s});var n=l(32065);function r(e,t){let l=i(e,t,"left"),n=i(e,t,"top"),r=t.offsetWidth,s=t.offsetHeight,o=e.scrollLeft,u=e.scrollTop,{borderTopWidth:c,borderLeftWidth:a,scrollPaddingTop:d,scrollPaddingRight:y,scrollPaddingBottom:f,scrollPaddingLeft:h}=getComputedStyle(e),p=o+parseInt(a,10),g=u+parseInt(c,10),v=p+e.clientWidth,K=g+e.clientHeight,m=parseInt(d,10)||0,S=parseInt(f,10)||0,b=parseInt(y,10)||0,k=parseInt(h,10)||0;l<=o+k?o=l-parseInt(a,10)-k:l+r>v-b&&(o+=l+r-v+b),n<=g+m?u=n-parseInt(c,10)-m:n+s>K-S&&(u+=n+s-K+S),e.scrollLeft=o,e.scrollTop=u}function i(e,t,l){let n="left"===l?"offsetLeft":"offsetTop",r=0;for(;t.offsetParent&&(r+=t[n],t.offsetParent!==e);){if(t.offsetParent.contains(e)){r-=e[n];break}t=t.offsetParent}return r}function s(e,t){if(e&&document.contains(e)){let u=document.scrollingElement||document.documentElement;if("hidden"===window.getComputedStyle(u).overflow)for(let t of function(e,t){let l=[];for(;e&&e!==document.documentElement;)(0,n.o)(e,void 0)&&l.push(e),e=e.parentElement;return l}(e))r(t,e);else{var l,i,s,o;let{left:n,top:r}=e.getBoundingClientRect();null==e||null==(l=e.scrollIntoView)||l.call(e,{block:"nearest"});let{left:u,top:c}=e.getBoundingClientRect();(Math.abs(n-u)>1||Math.abs(r-c)>1)&&(null==t||null==(s=t.containingElement)||null==(i=s.scrollIntoView)||i.call(s,{block:"center",inline:"center"}),null==(o=e.scrollIntoView)||o.call(e,{block:"nearest"}))}}}},36123:(e,t,l)=>{l.d(t,{p:()=>f});var n=l(64636),r=l(84702),i=l(60144),s=l(38746),o=l(56542),u=l(24215),c=l(71201),a=l(96539),d=l(72254),y=l(9585);function f(e){let{id:t,selectionManager:l,key:f,ref:g,shouldSelectOnPressUp:v,shouldUseVirtualFocus:K,focus:m,isDisabled:S,onAction:b,allowsDifferentPressOrigin:k,linkBehavior:w="action"}=e,F=(0,o.rd)();t=(0,u.Bi)(t);let E=e=>{if("keyboard"===e.pointerType&&(0,n.N9)(e))l.toggleSelection(f);else{if("none"===l.selectionMode)return;if(l.isLink(f)){if("selection"===w&&g.current){let t=l.getItemProps(f);F.open(g.current,e,t.href,t.routerOptions),l.setSelectedKeys(l.selectedKeys);return}else if("override"===w||"none"===w)return}"single"===l.selectionMode?l.isSelected(f)&&!l.disallowEmptySelection?l.toggleSelection(f):l.replaceSelection(f):e&&e.shiftKey?l.extendSelection(f):"toggle"===l.selectionBehavior||e&&((0,c.B)(e)||"touch"===e.pointerType||"virtual"===e.pointerType)?l.toggleSelection(f):l.replaceSelection(f)}};(0,y.useEffect)(()=>{f===l.focusedKey&&l.isFocused&&(K?(0,d.vX)(g.current):m?m():document.activeElement!==g.current&&g.current&&(0,r.l)(g.current))},[g,f,l.focusedKey,l.childFocusStrategy,l.isFocused,K]),S=S||l.isDisabled(f);let T={};K||S?S&&(T.onMouseDown=e=>{e.preventDefault()}):T={tabIndex:f===l.focusedKey?0:-1,onFocus(e){e.target===g.current&&l.setFocusedKey(f)}};let I=l.isLink(f)&&"override"===w,M=l.isLink(f)&&"selection"!==w&&"none"!==w,P=!S&&l.canSelectItem(f)&&!I,D=(b||M)&&!S,x=D&&("replace"===l.selectionBehavior?!P:!P||l.isEmpty),C=D&&P&&"replace"===l.selectionBehavior,L=x||C,B=(0,y.useRef)(null),N=L&&P,R=(0,y.useRef)(!1),A=(0,y.useRef)(!1),$=e=>{if(b&&b(),M&&g.current){let t=l.getItemProps(f);F.open(g.current,e,t.href,t.routerOptions)}},O={ref:g};v?(O.onPressStart=e=>{B.current=e.pointerType,R.current=N,"keyboard"===e.pointerType&&(!L||p())&&E(e)},k?(O.onPressUp=x?void 0:e=>{"mouse"===e.pointerType&&P&&E(e)},O.onPress=x?$:e=>{"keyboard"!==e.pointerType&&"mouse"!==e.pointerType&&P&&E(e)}):O.onPress=e=>{x||C&&"mouse"!==e.pointerType?("keyboard"!==e.pointerType||h())&&$(e):"keyboard"!==e.pointerType&&P&&E(e)}):(O.onPressStart=e=>{B.current=e.pointerType,R.current=N,A.current=x,P&&("mouse"===e.pointerType&&!x||"keyboard"===e.pointerType&&(!D||p()))&&E(e)},O.onPress=e=>{("touch"===e.pointerType||"pen"===e.pointerType||"virtual"===e.pointerType||"keyboard"===e.pointerType&&L&&h()||"mouse"===e.pointerType&&A.current)&&(L?$(e):P&&E(e))}),T["data-collection"]=(0,n.EG)(l.collection),T["data-key"]=f,O.preventFocusOnPress=K,K&&(O=(0,a.v)(O,{onPressStart(e){"touch"!==e.pointerType&&(l.setFocused(!0),l.setFocusedKey(f))},onPress(e){"touch"===e.pointerType&&(l.setFocused(!0),l.setFocusedKey(f))}}));let{pressProps:z,isPressed:V}=(0,i.d)(O),W=C?e=>{"mouse"===B.current&&(e.stopPropagation(),e.preventDefault(),$(e))}:void 0,{longPressProps:U}=(0,s.H)({isDisabled:!N,onLongPress(e){"touch"===e.pointerType&&(E(e),l.setSelectionBehavior("toggle"))}}),_=l.isLink(f)?e=>{o.Fe.isOpening||e.preventDefault()}:void 0;return{itemProps:(0,a.v)(T,P||x||K&&!S?z:{},N?U:{},{onDoubleClick:W,onDragStartCapture:e=>{"touch"===B.current&&R.current&&e.preventDefault()},onClick:_,id:t},K?{onMouseDown:e=>e.preventDefault()}:void 0),isPressed:V,isSelected:l.isSelected(f),isFocused:l.isFocused&&l.focusedKey===f,isDisabled:S,allowsSelection:P,hasAction:L}}function h(){let e=window.event;return(null==e?void 0:e.key)==="Enter"}function p(){let e=window.event;return(null==e?void 0:e.key)===" "||(null==e?void 0:e.code)==="Space"}},38746:(e,t,l)=>{l.d(t,{H:()=>a});var n=l(60144),r=l(60077),i=l(20965),s=l(61133),o=l(56947),u=l(96539),c=l(9585);function a(e){let{isDisabled:t,onLongPressStart:l,onLongPressEnd:a,onLongPress:d,threshold:y=500,accessibilityDescription:f}=e,h=(0,c.useRef)(void 0),{addGlobalListener:p,removeGlobalListener:g}=(0,r.A)(),{pressProps:v}=(0,n.d)({isDisabled:t,onPressStart(e){if(e.continuePropagation(),("mouse"===e.pointerType||"touch"===e.pointerType)&&(l&&l({...e,type:"longpressstart"}),h.current=setTimeout(()=>{e.target.dispatchEvent(new PointerEvent("pointercancel",{bubbles:!0})),(0,i.TW)(e.target).activeElement!==e.target&&(0,s.e)(e.target),d&&d({...e,type:"longpress"}),h.current=void 0},y),"touch"===e.pointerType)){let t=e=>{e.preventDefault()};p(e.target,"contextmenu",t,{once:!0}),p(window,"pointerup",()=>{setTimeout(()=>{g(e.target,"contextmenu",t)},30)},{once:!0})}},onPressEnd(e){h.current&&clearTimeout(h.current),a&&("mouse"===e.pointerType||"touch"===e.pointerType)&&a({...e,type:"longpressend"})}}),K=(0,o.I)(d&&!t?f:void 0);return{longPressProps:(0,u.v)(v,K)}}},40111:(e,t,l)=>{l.d(t,{R:()=>s});var n=l(71953),r=l(47314),i=l(9585);function s(e){let{selectionMode:t="none",disallowEmptySelection:l=!1,allowDuplicateSelectionEvents:s,selectionBehavior:u="toggle",disabledBehavior:c="all"}=e,a=(0,i.useRef)(!1),[,d]=(0,i.useState)(!1),y=(0,i.useRef)(null),f=(0,i.useRef)(null),[,h]=(0,i.useState)(null),p=(0,i.useMemo)(()=>o(e.selectedKeys),[e.selectedKeys]),g=(0,i.useMemo)(()=>o(e.defaultSelectedKeys,new(0,n.L)),[e.defaultSelectedKeys]),[v,K]=(0,r.P)(p,g,e.onSelectionChange),m=(0,i.useMemo)(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),[S,b]=(0,i.useState)(u);"replace"===u&&"toggle"===S&&"object"==typeof v&&0===v.size&&b("replace");let k=(0,i.useRef)(u);return(0,i.useEffect)(()=>{u!==k.current&&(b(u),k.current=u)},[u]),{selectionMode:t,disallowEmptySelection:l,selectionBehavior:S,setSelectionBehavior:b,get isFocused(){return a.current},setFocused(e){a.current=e,d(e)},get focusedKey(){return y.current},get childFocusStrategy(){return f.current},setFocusedKey(e,t="first"){y.current=e,f.current=t,h(e)},selectedKeys:v,setSelectedKeys(e){(s||!function(e,t){if(e.size!==t.size)return!1;for(let l of e)if(!t.has(l))return!1;return!0}(e,v))&&K(e)},disabledKeys:m,disabledBehavior:c}}function o(e,t){return e?"all"===e?"all":new(0,n.L)(e):t}},53837:(e,t,l)=>{l.d(t,{q:()=>i});var n=l(9585);function r(e){return null}r.getCollectionNode=function*(e,t){var l;let{childItems:r,title:i,children:s}=e,o=e.title||e.children,u=e.textValue||("string"==typeof o?o:"")||e["aria-label"]||"";u||null==t||t.suppressTextValueWarning,yield{type:"item",props:e,rendered:o,textValue:u,"aria-label":e["aria-label"],hasChildNodes:null!=(l=e).hasChildItems?l.hasChildItems:!!(l.childItems||l.title&&n.Children.count(l.children)>0),*childNodes(){if(r)for(let e of r)yield{type:"item",value:e};else if(i){let e=[];n.Children.forEach(s,t=>{e.push({type:"item",element:t})}),yield*e}}}};let i=r},56947:(e,t,l)=>{l.d(t,{I:()=>o});var n=l(73360),r=l(9585);let i=0,s=new Map;function o(e){let[t,l]=(0,r.useState)();return(0,n.N)(()=>{if(!e)return;let t=s.get(e);if(t)l(t.element.id);else{let n=`react-aria-description-${i++}`;l(n);let r=document.createElement("div");r.id=n,r.style.display="none",r.textContent=e,document.body.appendChild(r),t={refCount:0,element:r},s.set(e,t)}return t.refCount++,()=>{t&&0==--t.refCount&&(t.element.remove(),s.delete(e))}},[e]),{"aria-describedby":e?t:void 0}}},63051:(e,t,l)=>{function n(e,t){return"function"==typeof t.getChildren?t.getChildren(e.key):e.childNodes}function r(e){return i(e,0)}function i(e,t){if(t<0)return;let l=0;for(let n of e){if(l===t)return n;l++}}function s(e){let t;for(let l of e)t=l;return t}function o(e,t,l){if(t.parentKey===l.parentKey)return t.index-l.index;let n=[...u(e,t),t],r=[...u(e,l),l],i=n.slice(0,r.length).findIndex((e,t)=>e!==r[t]);return -1!==i?(t=n[i],l=r[i],t.index-l.index):n.findIndex(e=>e===l)>=0?1:(r.findIndex(e=>e===t),-1)}function u(e,t){let l=[],n=t;for(;(null==n?void 0:n.parentKey)!=null;)(n=e.getItem(n.parentKey))&&l.unshift(n);return l}l.d(t,{W:()=>s,cj:()=>i,iQ:()=>n,o3:()=>o,ue:()=>r})},64636:(e,t,l)=>{l.d(t,{EG:()=>c,N9:()=>i,au:()=>s,j5:()=>u});var n=l(60699),r=l(24215);function i(e){return(0,n.lg)()?e.altKey:e.ctrlKey}function s(e,t){var l,n;let r=`[data-key="${CSS.escape(String(t))}"]`,i=null==(l=e.current)?void 0:l.dataset.collection;return i&&(r=`[data-collection="${CSS.escape(i)}"]${r}`),null==(n=e.current)?void 0:n.querySelector(r)}let o=new WeakMap;function u(e){let t=(0,r.Bi)();return o.set(e,t),t}function c(e){return o.get(e)}},69036:(e,t,l)=>{l.d(t,{p:()=>u});class n{*[Symbol.iterator](){yield*this.iterable}get size(){return this.keyMap.size}getKeys(){return this.keyMap.keys()}getKeyBefore(e){var t;let l=this.keyMap.get(e);return l&&null!=(t=l.prevKey)?t:null}getKeyAfter(e){var t;let l=this.keyMap.get(e);return l&&null!=(t=l.nextKey)?t:null}getFirstKey(){return this.firstKey}getLastKey(){return this.lastKey}getItem(e){var t;return null!=(t=this.keyMap.get(e))?t:null}at(e){let t=[...this.getKeys()];return this.getItem(t[e])}getChildren(e){let t=this.keyMap.get(e);return(null==t?void 0:t.childNodes)||[]}constructor(e){var t;this.keyMap=new Map,this.firstKey=null,this.lastKey=null,this.iterable=e;let l=e=>{if(this.keyMap.set(e.key,e),e.childNodes&&"section"===e.type)for(let t of e.childNodes)l(t)};for(let t of e)l(t);let n=null,r=0;for(let[e,t]of this.keyMap)n?(n.nextKey=e,t.prevKey=n.key):(this.firstKey=e,t.prevKey=void 0),"item"===t.type&&(t.index=r++),(n=t).nextKey=void 0;this.lastKey=null!=(t=null==n?void 0:n.key)?t:null}}var r=l(40111),i=l(97620),s=l(9585),o=l(97653);function u(e){let{filter:t,layoutDelegate:l}=e,u=(0,r.R)(e),c=(0,s.useMemo)(()=>e.disabledKeys?new Set(e.disabledKeys):new Set,[e.disabledKeys]),a=(0,s.useCallback)(e=>new n(t?t(e):e),[t]),d=(0,s.useMemo)(()=>({suppressTextValueWarning:e.suppressTextValueWarning}),[e.suppressTextValueWarning]),y=(0,o.G)(e,a,d),f=(0,s.useMemo)(()=>new(0,i.Y)(y,u,{layoutDelegate:l}),[y,u,l]);return function(e,t){let l=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(null!=t.focusedKey&&!e.getItem(t.focusedKey)&&l.current){var n,r,i,s,o,u,c;let a=l.current.getItem(t.focusedKey),d=[...l.current.getKeys()].map(e=>{let t=l.current.getItem(e);return(null==t?void 0:t.type)==="item"?t:null}).filter(e=>null!==e),y=[...e.getKeys()].map(t=>{let l=e.getItem(t);return(null==l?void 0:l.type)==="item"?l:null}).filter(e=>null!==e),f=(null!=(n=null==d?void 0:d.length)?n:0)-(null!=(r=null==y?void 0:y.length)?r:0),h=Math.min(f>1?Math.max((null!=(i=null==a?void 0:a.index)?i:0)-f+1,0):null!=(s=null==a?void 0:a.index)?s:0,(null!=(o=null==y?void 0:y.length)?o:0)-1),p=null,g=!1;for(;h>=0;){if(!t.isDisabled(y[h].key)){p=y[h];break}h<y.length-1&&!g?h++:(g=!0,h>(null!=(u=null==a?void 0:a.index)?u:0)&&(h=null!=(c=null==a?void 0:a.index)?c:0),h--)}t.setFocusedKey(p?p.key:null)}l.current=e},[e,t])}(y,f),{collection:y,disabledKeys:c,selectionManager:f}}},71201:(e,t,l)=>{l.d(t,{B:()=>r});var n=l(60699);function r(e){return(0,n.cX)()?e.metaKey:e.ctrlKey}},71953:(e,t,l)=>{l.d(t,{L:()=>n});class n extends Set{constructor(e,t,l){super(e),e instanceof n?(this.anchorKey=null!=t?t:e.anchorKey,this.currentKey=null!=l?l:e.currentKey):(this.anchorKey=null!=t?t:null,this.currentKey=null!=l?l:null)}}},72254:(e,t,l)=>{l.d(t,{vX:()=>i});var n=l(20965),r=l(50689);function i(e){var t,l,i,s,o;let u,c,a=(t=(0,n.TW)(e),(c=null==(u=(0,r.bq)(t))?void 0:u.getAttribute("aria-activedescendant"))&&t.getElementById(c)||u);a!==e&&(a&&(l=a,i=e,l.dispatchEvent(new FocusEvent("blur",{relatedTarget:i})),l.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:i}))),e&&(s=e,o=a,s.dispatchEvent(new FocusEvent("focus",{relatedTarget:o})),s.dispatchEvent(new FocusEvent("focusin",{bubbles:!0,relatedTarget:o}))))}},81298:(e,t,l)=>{l.d(t,{I:()=>r});var n=l(9585);function r(e){let{keyboardDelegate:t,selectionManager:l,onTypeSelect:r}=e,i=(0,n.useRef)({search:"",timeout:void 0}).current;return{typeSelectProps:{onKeyDownCapture:t.getKeyForSearch?e=>{var n;let s=1!==(n=e.key).length&&/^[A-Z]/i.test(n)?"":n;if(s&&!e.ctrlKey&&!e.metaKey&&e.currentTarget.contains(e.target)){if(" "===s&&i.search.trim().length>0&&(e.preventDefault(),"continuePropagation"in e||e.stopPropagation()),i.search+=s,null!=t.getKeyForSearch){let e=t.getKeyForSearch(i.search,l.focusedKey);null==e&&(e=t.getKeyForSearch(i.search)),null!=e&&(l.setFocusedKey(e),r&&r(e))}clearTimeout(i.timeout),i.timeout=setTimeout(()=>{i.search=""},1e3)}}:void 0}}}},89213:(e,t,l)=>{l.d(t,{y:()=>c});var n=l(9733),r=(0,l(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),i=l(9585),s=l(3208),o=l(19605),u=(0,s.Rf)((e,t)=>{let{Component:l,getDividerProps:s}=function(e){var t;let l,s,{as:o,className:u,orientation:c,...a}=e,d=o||"hr";"hr"===d&&"vertical"===c&&(d="div");let{separatorProps:y}=(t={elementType:"string"==typeof d?d:"hr",orientation:c},s=(0,n.$)(t,{enabled:"string"==typeof t.elementType}),("vertical"===t.orientation&&(l="vertical"),"hr"!==t.elementType)?{separatorProps:{...s,role:"separator","aria-orientation":l}}:{separatorProps:s}),f=(0,i.useMemo)(()=>r({orientation:c,className:u}),[c,u]);return{Component:d,getDividerProps:(0,i.useCallback)((e={})=>({className:f,role:"separator","data-orientation":c,...y,...a,...e}),[f,c,y,a])}}({...e});return(0,o.jsx)(l,{ref:t,...s()})});u.displayName="HeroUI.Divider";var c=u},97620:(e,t,l)=>{l.d(t,{Y:()=>i});var n=l(71953),r=l(63051);class i{get selectionMode(){return this.state.selectionMode}get disallowEmptySelection(){return this.state.disallowEmptySelection}get selectionBehavior(){return this.state.selectionBehavior}setSelectionBehavior(e){this.state.setSelectionBehavior(e)}get isFocused(){return this.state.isFocused}setFocused(e){this.state.setFocused(e)}get focusedKey(){return this.state.focusedKey}get childFocusStrategy(){return this.state.childFocusStrategy}setFocusedKey(e,t){(null==e||this.collection.getItem(e))&&this.state.setFocusedKey(e,t)}get selectedKeys(){return"all"===this.state.selectedKeys?new Set(this.getSelectAllKeys()):this.state.selectedKeys}get rawSelection(){return this.state.selectedKeys}isSelected(e){if("none"===this.state.selectionMode)return!1;let t=this.getKey(e);return null!=t&&("all"===this.state.selectedKeys?this.canSelectItem(t):this.state.selectedKeys.has(t))}get isEmpty(){return"all"!==this.state.selectedKeys&&0===this.state.selectedKeys.size}get isSelectAll(){if(this.isEmpty)return!1;if("all"===this.state.selectedKeys)return!0;if(null!=this._isSelectAll)return this._isSelectAll;let e=this.getSelectAllKeys(),t=this.state.selectedKeys;return this._isSelectAll=e.every(e=>t.has(e)),this._isSelectAll}get firstSelectedKey(){var e;let t=null;for(let e of this.state.selectedKeys){let l=this.collection.getItem(e);(!t||l&&0>(0,r.o3)(this.collection,l,t))&&(t=l)}return null!=(e=null==t?void 0:t.key)?e:null}get lastSelectedKey(){var e;let t=null;for(let e of this.state.selectedKeys){let l=this.collection.getItem(e);(!t||l&&(0,r.o3)(this.collection,l,t)>0)&&(t=l)}return null!=(e=null==t?void 0:t.key)?e:null}get disabledKeys(){return this.state.disabledKeys}get disabledBehavior(){return this.state.disabledBehavior}extendSelection(e){let t;if("none"===this.selectionMode)return;if("single"===this.selectionMode)return void this.replaceSelection(e);let l=this.getKey(e);if(null!=l){if("all"===this.state.selectedKeys)t=new(0,n.L)([l],l,l);else{var r,i;let e=this.state.selectedKeys,s=null!=(r=e.anchorKey)?r:l;for(let r of(t=new(0,n.L)(e,s,l),this.getKeyRange(s,null!=(i=e.currentKey)?i:l)))t.delete(r);for(let e of this.getKeyRange(l,s))this.canSelectItem(e)&&t.add(e)}this.state.setSelectedKeys(t)}}getKeyRange(e,t){let l=this.collection.getItem(e),n=this.collection.getItem(t);return l&&n?0>=(0,r.o3)(this.collection,l,n)?this.getKeyRangeInternal(e,t):this.getKeyRangeInternal(t,e):[]}getKeyRangeInternal(e,t){var l;if(null==(l=this.layoutDelegate)?void 0:l.getKeyRange)return this.layoutDelegate.getKeyRange(e,t);let n=[],r=e;for(;null!=r;){let e=this.collection.getItem(r);if(e&&("item"===e.type||"cell"===e.type&&this.allowsCellSelection)&&n.push(r),r===t)return n;r=this.collection.getKeyAfter(r)}return[]}getKey(e){let t=this.collection.getItem(e);if(!t||"cell"===t.type&&this.allowsCellSelection)return e;for(;t&&"item"!==t.type&&null!=t.parentKey;)t=this.collection.getItem(t.parentKey);return t&&"item"===t.type?t.key:null}toggleSelection(e){if("none"===this.selectionMode)return;if("single"===this.selectionMode&&!this.isSelected(e))return void this.replaceSelection(e);let t=this.getKey(e);if(null==t)return;let l=new(0,n.L)("all"===this.state.selectedKeys?this.getSelectAllKeys():this.state.selectedKeys);l.has(t)?l.delete(t):this.canSelectItem(t)&&(l.add(t),l.anchorKey=t,l.currentKey=t),this.disallowEmptySelection&&0===l.size||this.state.setSelectedKeys(l)}replaceSelection(e){if("none"===this.selectionMode)return;let t=this.getKey(e);if(null==t)return;let l=this.canSelectItem(t)?new(0,n.L)([t],t,t):new(0,n.L);this.state.setSelectedKeys(l)}setSelectedKeys(e){if("none"===this.selectionMode)return;let t=new(0,n.L);for(let l of e){let e=this.getKey(l);if(null!=e&&(t.add(e),"single"===this.selectionMode))break}this.state.setSelectedKeys(t)}getSelectAllKeys(){let e=[],t=l=>{for(;null!=l;){if(this.canSelectItem(l)){var n,i;let s=this.collection.getItem(l);(null==s?void 0:s.type)==="item"&&e.push(l),(null==s?void 0:s.hasChildNodes)&&(this.allowsCellSelection||"item"!==s.type)&&t(null!=(i=null==(n=(0,r.ue)((0,r.iQ)(s,this.collection)))?void 0:n.key)?i:null)}l=this.collection.getKeyAfter(l)}};return t(this.collection.getFirstKey()),e}selectAll(){this.isSelectAll||"multiple"!==this.selectionMode||this.state.setSelectedKeys("all")}clearSelection(){!this.disallowEmptySelection&&("all"===this.state.selectedKeys||this.state.selectedKeys.size>0)&&this.state.setSelectedKeys(new(0,n.L))}toggleSelectAll(){this.isSelectAll?this.clearSelection():this.selectAll()}select(e,t){"none"!==this.selectionMode&&("single"===this.selectionMode?this.isSelected(e)&&!this.disallowEmptySelection?this.toggleSelection(e):this.replaceSelection(e):"toggle"===this.selectionBehavior||t&&("touch"===t.pointerType||"virtual"===t.pointerType)?this.toggleSelection(e):this.replaceSelection(e))}isSelectionEqual(e){if(e===this.state.selectedKeys)return!0;let t=this.selectedKeys;if(e.size!==t.size)return!1;for(let l of e)if(!t.has(l))return!1;for(let l of t)if(!e.has(l))return!1;return!0}canSelectItem(e){var t;if("none"===this.state.selectionMode||this.state.disabledKeys.has(e))return!1;let l=this.collection.getItem(e);return!!l&&(null==l||null==(t=l.props)||!t.isDisabled)&&("cell"!==l.type||!!this.allowsCellSelection)}isDisabled(e){var t,l;return"all"===this.state.disabledBehavior&&(this.state.disabledKeys.has(e)||!!(null==(l=this.collection.getItem(e))||null==(t=l.props)?void 0:t.isDisabled))}isLink(e){var t,l;return!!(null==(l=this.collection.getItem(e))||null==(t=l.props)?void 0:t.href)}getItemProps(e){var t;return null==(t=this.collection.getItem(e))?void 0:t.props}withCollection(e){return new i(e,this.state,{allowsCellSelection:this.allowsCellSelection,layoutDelegate:this.layoutDelegate||void 0})}constructor(e,t,l){var n;this.collection=e,this.state=t,this.allowsCellSelection=null!=(n=null==l?void 0:l.allowsCellSelection)&&n,this._isSelectAll=null,this.layoutDelegate=(null==l?void 0:l.layoutDelegate)||null}}},97653:(e,t,l)=>{l.d(t,{G:()=>o});var n=l(9585);class r{build(e,t){return this.context=t,i(()=>this.iterateCollection(e))}*iterateCollection(e){let{children:t,items:l}=e;if(n.isValidElement(t)&&t.type===n.Fragment)yield*this.iterateCollection({children:t.props.children,items:l});else if("function"==typeof t){if(!l)throw Error("props.children was a function but props.items is missing");let e=0;for(let n of l)yield*this.getFullNode({value:n,index:e},{renderer:t}),e++}else{let e=[];n.Children.forEach(t,t=>{t&&e.push(t)});let l=0;for(let t of e)for(let e of this.getFullNode({element:t,index:l},{}))l++,yield e}}getKey(e,t,l,n){if(null!=e.key)return e.key;if("cell"===t.type&&null!=t.key)return`${n}${t.key}`;let r=t.value;if(null!=r){var i;let e=null!=(i=r.key)?i:r.id;if(null==e)throw Error("No key found for item");return e}return n?`${n}.${t.index}`:`$.${t.index}`}getChildState(e,t){return{renderer:t.renderer||e.renderer}}*getFullNode(e,t,l,r){var o,u,c,a,d,y,f,h;if(n.isValidElement(e.element)&&e.element.type===n.Fragment){let i=[];n.Children.forEach(e.element.props.children,e=>{i.push(e)});let s=null!=(o=e.index)?o:0;for(let e of i)yield*this.getFullNode({element:e,index:s++},t,l,r);return}let p=e.element;if(!p&&e.value&&t&&t.renderer){let l=this.cache.get(e.value);if(l&&(!l.shouldInvalidate||!l.shouldInvalidate(this.context))){l.index=e.index,l.parentKey=r?r.key:null,yield l;return}p=t.renderer(e.value)}if(n.isValidElement(p)){let n=p.type;if("function"!=typeof n&&"function"!=typeof n.getCollectionNode){let e=p.type;throw Error(`Unknown element <${e}> in collection.`)}let i=n.getCollectionNode(p.props,this.context),o=null!=(u=e.index)?u:0,f=i.next();for(;!f.done&&f.value;){let n=f.value;e.index=o;let u=null!=(c=n.key)?c:null;null==u&&(u=n.element?null:this.getKey(p,e,t,l));let h=[...this.getFullNode({...n,key:u,index:o,wrapper:function(e,t){return e&&t?l=>e(t(l)):e||t||void 0}(e.wrapper,n.wrapper)},this.getChildState(t,n),l?`${l}${p.key}`:p.key,r)];for(let t of h){if(t.value=null!=(d=null!=(a=n.value)?a:e.value)?d:null,t.value&&this.cache.set(t.value,t),e.type&&t.type!==e.type)throw Error(`Unsupported type <${s(t.type)}> in <${s(null!=(y=null==r?void 0:r.type)?y:"unknown parent type")}>. Only <${s(e.type)}> is supported.`);o++,yield t}f=i.next(h)}return}if(null==e.key||null==e.type)return;let g=this,v={type:e.type,props:e.props,key:e.key,parentKey:r?r.key:null,value:null!=(f=e.value)?f:null,level:r?r.level+1:0,index:e.index,rendered:e.rendered,textValue:null!=(h=e.textValue)?h:"","aria-label":e["aria-label"],wrapper:e.wrapper,shouldInvalidate:e.shouldInvalidate,hasChildNodes:e.hasChildNodes||!1,childNodes:i(function*(){if(!e.hasChildNodes||!e.childNodes)return;let l=0;for(let n of e.childNodes())for(let e of(null!=n.key&&(n.key=`${v.key}${n.key}`),g.getFullNode({...n,index:l},g.getChildState(t,n),v.key,v)))l++,yield e})};yield v}constructor(){this.cache=new WeakMap}}function i(e){let t=[],l=null;return{*[Symbol.iterator](){for(let e of t)yield e;for(let n of(l||(l=e()),l))t.push(n),yield n}}}function s(e){return e[0].toUpperCase()+e.slice(1)}function o(e,t,l){let i=(0,n.useMemo)(()=>new r,[]),{children:s,items:o,collection:u}=e;return(0,n.useMemo)(()=>u||t(i.build({children:s,items:o},l)),[i,s,o,u,l,t])}}}]);