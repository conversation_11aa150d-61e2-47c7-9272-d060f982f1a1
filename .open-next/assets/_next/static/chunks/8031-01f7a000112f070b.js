"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8031],{651:(e,t,r)=>{r.d(t,{O:()=>u});var n=r(17199),o=r(1343);let i=(e,t)=>t&&"number"==typeof e?t.transform(e):e;var a=r(15919);let l={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s=n.U.length;function u(e,t,r){let{style:u,vars:d,transformOrigin:c}=e,f=!1,p=!1;for(let e in t){let r=t[e];if(n.f.has(e)){f=!0;continue}if((0,o.j)(e)){d[e]=r;continue}{let t=i(r,a.W[e]);e.startsWith("origin")?(p=!0,c[e]=t):u[e]=t}}if(!t.transform&&(f||r?u.transform=function(e,t,r){let o="",u=!0;for(let d=0;d<s;d++){let s=n.U[d],c=e[s];if(void 0===c)continue;let f=!0;if(!(f="number"==typeof c?c===+!!s.startsWith("scale"):0===parseFloat(c))||r){let e=i(c,a.W[s]);if(!f){u=!1;let t=l[s]||s;o+=`${t}(${e}) `}r&&(t[s]=e)}}return o=o.trim(),r?o=r(t,u?"":o):u&&(o="none"),o}(t,e.transform,r):u.transform&&(u.transform="none")),p){let{originX:e="50%",originY:t="50%",originZ:r=0}=c;u.transformOrigin=`${e} ${t} ${r}`}}},1343:(e,t,r)=>{r.d(t,{j:()=>o,p:()=>a});let n=e=>t=>"string"==typeof t&&t.startsWith(e),o=n("--"),i=n("var(--"),a=e=>!!i(e)&&l.test(e.split("/*")[0].trim()),l=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},2215:(e,t,r)=>{r.d(t,{LE:()=>p,eg:()=>u,lR:()=>f,o1:()=>d,yB:()=>c});var n=r(73360),o=r(7672),i=r(37260),a=r(20965),l=r(61133),s=r(9585);function u(e){return e.nativeEvent=e,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function d(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function c(e){let t=(0,s.useRef)({isFocused:!1,observer:null});(0,n.N)(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let r=(0,o.J)(t=>{null==e||e(t)});return(0,s.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=e.target;n.addEventListener("focusout",e=>{t.current.isFocused=!1,n.disabled&&r(u(e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var e;null==(e=t.current.observer)||e.disconnect();let r=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:r})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:r}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[r])}let f=!1;function p(e){for(;e&&!(0,i.t)(e);)e=e.parentElement;let t=(0,a.mD)(e),r=t.document.activeElement;if(!r||r===e)return;f=!0;let n=!1,o=e=>{(e.target===r||n)&&e.stopImmediatePropagation()},s=t=>{(t.target===r||n)&&(t.stopImmediatePropagation(),e||n||(n=!0,(0,l.e)(r),c()))},u=t=>{(t.target===e||n)&&t.stopImmediatePropagation()},d=t=>{(t.target===e||n)&&(t.stopImmediatePropagation(),n||(n=!0,(0,l.e)(r),c()))};t.addEventListener("blur",o,!0),t.addEventListener("focusout",s,!0),t.addEventListener("focusin",d,!0),t.addEventListener("focus",u,!0);let c=()=>{cancelAnimationFrame(p),t.removeEventListener("blur",o,!0),t.removeEventListener("focusout",s,!0),t.removeEventListener("focusin",d,!0),t.removeEventListener("focus",u,!0),f=!1,n=!1},p=requestAnimationFrame(c);return c}},3208:(e,t,r)=>{r.d(t,{Rf:()=>o,rE:()=>i});var n=r(9585);function o(e){return(0,n.forwardRef)(e)}var i=(e,t,r=!0)=>{if(!t)return[e,{}];let n=t.reduce((t,r)=>r in e?{...t,[r]:e[r]}:t,{});return r?[Object.keys(e).filter(e=>!t.includes(e)).reduce((t,r)=>({...t,[r]:e[r]}),{}),n]:[e,n]}},5773:(e,t,r)=>{r.d(t,{n:()=>n});let n=e=>"string"==typeof e&&"svg"===e.toLowerCase()},7484:(e,t,r)=>{r.d(t,{o:()=>l});var n=r(99275),o=r(80481),i=r(74476),a=r(9585);function l(e={}){let{autoFocus:t=!1,isTextInput:r,within:s}=e,u=(0,a.useRef)({isFocused:!1,isFocusVisible:t||(0,n.pP)()}),[d,c]=(0,a.useState)(!1),[f,p]=(0,a.useState)(()=>u.current.isFocused&&u.current.isFocusVisible),g=(0,a.useCallback)(()=>p(u.current.isFocused&&u.current.isFocusVisible),[]),m=(0,a.useCallback)(e=>{u.current.isFocused=e,c(e),g()},[g]);(0,n.K7)(e=>{u.current.isFocusVisible=e,g()},[],{isTextInput:r});let{focusProps:v}=(0,o.i)({isDisabled:s,onFocusChange:m}),{focusWithinProps:b}=(0,i.R)({isDisabled:!s,onFocusWithinChange:m});return{isFocused:d,isFocusVisible:f,focusProps:s?b:v}}},7672:(e,t,r)=>{r.d(t,{J:()=>i});var n=r(73360),o=r(9585);function i(e){let t=(0,o.useRef)(null);return(0,n.N)(()=>{t.current=e},[e]),(0,o.useCallback)((...e)=>{let r=t.current;return null==r?void 0:r(...e)},[])}},9733:(e,t,r)=>{r.d(t,{$:()=>s});var n=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),o=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),i=/^(data-.*)$/,a=/^(aria-.*)$/,l=/^(on[A-Z].*)$/;function s(e,t={}){let{labelable:r=!0,enabled:u=!0,propNames:d,omitPropNames:c,omitEventNames:f,omitDataProps:p,omitEventProps:g}=t,m={};if(!u)return e;for(let t in e)!((null==c?void 0:c.has(t))||(null==f?void 0:f.has(t))&&l.test(t)||l.test(t)&&!o.has(t)||p&&i.test(t))&&!(g&&l.test(t))&&(Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&a.test(t)||(null==d?void 0:d.has(t))||i.test(t))||l.test(t))&&(m[t]=e[t]);return m}},12877:(e,t,r)=>{r.d(t,{P:()=>i,Y:()=>o});var n=r(60699);function o(e){return 0===e.mozInputSource&&!!e.isTrusted||((0,n.m0)()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function i(e){return!(0,n.m0)()&&0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType}},13600:(e,t,r)=>{r.d(t,{v:()=>a});let n=new Map,o=new Set;function i(){if("undefined"==typeof window)return;function e(e){return"propertyName"in e}let t=r=>{if(!e(r)||!r.target)return;let i=n.get(r.target);if(i&&(i.delete(r.propertyName),0===i.size&&(r.target.removeEventListener("transitioncancel",t),n.delete(r.target)),0===n.size)){for(let e of o)e();o.clear()}};document.body.addEventListener("transitionrun",r=>{if(!e(r)||!r.target)return;let o=n.get(r.target);o||(o=new Set,n.set(r.target,o),r.target.addEventListener("transitioncancel",t,{once:!0})),o.add(r.propertyName)}),document.body.addEventListener("transitionend",t)}function a(e){requestAnimationFrame(()=>{for(let[e]of n)"isConnected"in e&&!e.isConnected&&n.delete(e);0===n.size?e():o.add(e)})}"undefined"!=typeof document&&("loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i))},13683:(e,t,r)=>{r.d(t,{KN:()=>i,gQ:()=>u,px:()=>a,uj:()=>o,vh:()=>l,vw:()=>s});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),o=n("deg"),i=n("%"),a=n("px"),l=n("vh"),s=n("vw"),u={...i,parse:e=>i.parse(e)/100,transform:e=>i.transform(100*e)}},14171:(e,t,r)=>{r.d(t,{j:()=>d});var n=r(80897),o=r(27494),i=r(42620),a=r(18884),l=r(19605),s=()=>Promise.all([r.e(9586),r.e(822)]).then(r.bind(r,80822)).then(e=>e.default),u=e=>{let{ripples:t=[],motionProps:r,color:u="currentColor",style:d,onClear:c}=e;return(0,l.jsx)(l.Fragment,{children:t.map(e=>{let t=(0,a.qE)(.01*e.size,.2,e.size>100?.75:.5);return(0,l.jsx)(n.F,{features:s,children:(0,l.jsx)(o.N,{mode:"popLayout",children:(0,l.jsx)(i.m.span,{animate:{transform:"scale(2)",opacity:0},className:"heroui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:u,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:e.y,left:e.x,width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),...d},transition:{duration:t},onAnimationComplete:()=>{c(e.key)},...r})})},e.key)})})};u.displayName="HeroUI.Ripple";var d=u},14784:(e,t,r)=>{r.d(t,{B:()=>l});var n=r(651),o=r(13683);let i={offset:"stroke-dashoffset",array:"stroke-dasharray"},a={offset:"strokeDashoffset",array:"strokeDasharray"};function l(e,{attrX:t,attrY:r,attrScale:l,pathLength:s,pathSpacing:u=1,pathOffset:d=0,...c},f,p,g){if((0,n.O)(e,c,p),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:m,style:v}=e;m.transform&&(v.transform=m.transform,delete m.transform),(v.transform||m.transformOrigin)&&(v.transformOrigin=m.transformOrigin??"50% 50%",delete m.transformOrigin),v.transform&&(v.transformBox=g?.transformBox??"fill-box",delete m.transformBox),void 0!==t&&(m.x=t),void 0!==r&&(m.y=r),void 0!==l&&(m.scale=l),void 0!==s&&function(e,t,r=1,n=0,l=!0){e.pathLength=1;let s=l?i:a;e[s.offset]=o.px.transform(-n);let u=o.px.transform(t),d=o.px.transform(r);e[s.array]=`${u} ${d}`}(m,s,u,d,!1)}},15919:(e,t,r)=>{r.d(t,{W:()=>l});var n=r(48782);let o={...n.ai,transform:Math.round};var i=r(13683);let a={rotate:i.uj,rotateX:i.uj,rotateY:i.uj,rotateZ:i.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:i.uj,skewX:i.uj,skewY:i.uj,distance:i.px,translateX:i.px,translateY:i.px,translateZ:i.px,x:i.px,y:i.px,z:i.px,perspective:i.px,transformPerspective:i.px,opacity:n.X4,originX:i.gQ,originY:i.gQ,originZ:i.px},l={borderWidth:i.px,borderTopWidth:i.px,borderRightWidth:i.px,borderBottomWidth:i.px,borderLeftWidth:i.px,borderRadius:i.px,radius:i.px,borderTopLeftRadius:i.px,borderTopRightRadius:i.px,borderBottomRightRadius:i.px,borderBottomLeftRadius:i.px,width:i.px,maxWidth:i.px,height:i.px,maxHeight:i.px,top:i.px,right:i.px,bottom:i.px,left:i.px,padding:i.px,paddingTop:i.px,paddingRight:i.px,paddingBottom:i.px,paddingLeft:i.px,margin:i.px,marginTop:i.px,marginRight:i.px,marginBottom:i.px,marginLeft:i.px,backgroundPositionX:i.px,backgroundPositionY:i.px,...a,zIndex:o,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:o}},16007:(e,t,r)=>{r.d(t,{L:()=>n});let n=(0,r(9585).createContext)({})},16383:(e,t,r)=>{r.d(t,{$:()=>i,H:()=>o});var n=r(1343);let o={};function i(e){for(let t in e)o[t]=e[t],(0,n.j)(t)&&(o[t].isCSSVariable=!0)}},16506:(e,t,r)=>{r.d(t,{Y:()=>o});var n=r(21320);function o(e){for(let t in e)n.B[t]={...n.B[t],...e[t]}}},17199:(e,t,r)=>{r.d(t,{U:()=>n,f:()=>o});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(n)},18884:(e,t,r)=>{r.d(t,{$z:()=>d,GU:()=>g,Im:()=>l,Lz:()=>p,QA:()=>n,QN:()=>h,R8:()=>w,Tn:()=>s,ZH:()=>f,gt:()=>o,j1:()=>c,qE:()=>b,sE:()=>u,t6:()=>m,y1:()=>v});var n=e=>e,o=!1;function i(e){return Array.isArray(e)}function a(e){let t=typeof e;return null!=e&&("object"===t||"function"===t)&&!i(e)}function l(e){return i(e)?i(e)&&0===e.length:a(e)?a(e)&&0===Object.keys(e).length:null==e||""===e}function s(e){return"function"==typeof e}var u=e=>e?"true":void 0;function d(...e){for(var t,r,n=0,o="";n<e.length;)(t=e[n++])&&(r=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n);else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(t))&&(o&&(o+=" "),o+=r);return o}var c=(...e)=>{let t=" ";for(let r of e)if("string"==typeof r&&r.length>0){t=r;break}return t},f=e=>e?e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"";function p(e){return`${e}-${Math.floor(1e6*Math.random())}`}function g(e){for(let t in e)t.startsWith("on")&&delete e[t];return e}function m(e){if(!e||"object"!=typeof e)return"";try{return JSON.stringify(e)}catch(e){return""}}function v(e,t){return Array.from({length:t-e+1},(t,r)=>r+e)}function b(e,t,r){return Math.min(Math.max(e,t),r)}function h(e,t=100){return Math.min(Math.max(e,0),t)}var y={};function w(e,t,...r){let n=t?` [${t}]`:" ",o=`[Hero UI]${n}: ${e}`;"undefined"!=typeof console&&(y[o]||(y[o]=!0))}},20460:(e,t,r)=>{r.d(t,{I:()=>n});let n=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},20714:(e,t,r)=>{r.d(t,{s:()=>o});var n=r(68969);function o(e){return(0,n.G)(e)&&"offsetHeight"in e}},20965:(e,t,r)=>{r.d(t,{Ng:()=>i,TW:()=>n,mD:()=>o});let n=e=>{var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document},o=e=>e&&"window"in e&&e.window===e?e:n(e).defaultView||window;function i(e){return null!==e&&"object"==typeof e&&"nodeType"in e&&"number"==typeof e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}},21320:(e,t,r)=>{r.d(t,{B:()=>o});let n={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},o={};for(let e in n)o[e]={isEnabled:t=>n[e].some(e=>!!t[e])}},23883:(e,t,r)=>{r.d(t,{mK:()=>o,zD:()=>i});var n=r(9585);function o(e){return{UNSAFE_getDOMNode:()=>e.current}}function i(e){let t=(0,n.useRef)(null);return(0,n.useImperativeHandle)(e,()=>t.current),t}"undefined"!=typeof window&&window.document&&window.document.createElement},24215:(e,t,r)=>{let n;r.d(t,{Tw:()=>c,Bi:()=>d,X1:()=>f});var o=r(73360),i=r(7672),a=r(9585),l=r(37285);let s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),u=new Map;function d(e){let[t,r]=(0,a.useState)(e),i=(0,a.useRef)(null),d=(0,l.Cc)(t),c=(0,a.useRef)(null);if(n&&n.register(c,d),s){let e=u.get(d);e&&!e.includes(i)?e.push(i):u.set(d,[i])}return(0,o.N)(()=>()=>{n&&n.unregister(c),u.delete(d)},[d]),(0,a.useEffect)(()=>{let e=i.current;return e&&r(e),()=>{e&&(i.current=null)}}),d}function c(e,t){if(e===t)return e;let r=u.get(e);if(r)return r.forEach(e=>e.current=t),t;let n=u.get(t);return n?(n.forEach(t=>t.current=e),e):t}function f(e=[]){let t=d(),[r,n]=function(e){let[t,r]=(0,a.useState)(e),n=(0,a.useRef)(null),l=(0,i.J)(()=>{if(!n.current)return;let e=n.current.next();if(e.done){n.current=null;return}t===e.value?l():r(e.value)});(0,o.N)(()=>{n.current&&l()});let s=(0,i.J)(e=>{n.current=e(t),l()});return[t,s]}(t),l=(0,a.useCallback)(()=>{n(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,n]);return(0,o.N)(l,[t,l,...e]),r}"undefined"!=typeof FinalizationRegistry&&(n=new FinalizationRegistry(e=>{u.delete(e)}))},26251:(e,t,r)=>{r.d(t,{N:()=>n});let n=(0,r(9585).createContext)({})},26423:(e,t,r)=>{r.d(t,{c:()=>n});function n(...e){return(...t)=>{for(let r of e)"function"==typeof r&&r(...t)}}},27494:(e,t,r)=>{r.d(t,{N:()=>h});var n=r(19605),o=r(9585),i=r(16007),a=r(56411),l=r(36196),s=r(98307),u=r(20714),d=r(39098);class c extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:r,anchorX:i,root:a}=e,l=(0,o.useId)(),s=(0,o.useRef)(null),u=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,o.useContext)(d.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:d}=u.current;if(r||!s.current||!e||!t)return;s.current.dataset.motionPopId=l;let c=document.createElement("style");f&&(c.nonce=f);let p=null!=a?a:document.head;return p.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(o):"right: ".concat(d),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{p.removeChild(c),p.contains(c)&&p.removeChild(c)}},[r]),(0,n.jsx)(c,{isPresent:r,childRef:s,sizeRef:u,children:o.cloneElement(t,{ref:s})})}let p=e=>{let{children:t,initial:r,isPresent:i,onExitComplete:l,custom:u,presenceAffectsLayout:d,mode:c,anchorX:p,root:m}=e,v=(0,a.M)(g),b=(0,o.useId)(),h=!0,y=(0,o.useMemo)(()=>(h=!1,{id:b,initial:r,isPresent:i,custom:u,onExitComplete:e=>{for(let t of(v.set(e,!0),v.values()))if(!t)return;l&&l()},register:e=>(v.set(e,!1),()=>v.delete(e))}),[i,v,l]);return d&&h&&(y={...y}),(0,o.useMemo)(()=>{v.forEach((e,t)=>v.set(t,!1))},[i]),o.useEffect(()=>{i||v.size||!l||l()},[i]),"popLayout"===c&&(t=(0,n.jsx)(f,{isPresent:i,anchorX:p,root:m,children:t})),(0,n.jsx)(s.t.Provider,{value:y,children:t})};function g(){return new Map}var m=r(30100);let v=e=>e.key||"";function b(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let h=e=>{let{children:t,custom:r,initial:s=!0,onExitComplete:u,presenceAffectsLayout:d=!0,mode:c="sync",propagate:f=!1,anchorX:g="left",root:h}=e,[y,w]=(0,m.xQ)(f),x=(0,o.useMemo)(()=>b(t),[t]),E=f&&!y?[]:x.map(v),T=(0,o.useRef)(!0),k=(0,o.useRef)(x),P=(0,a.M)(()=>new Map),[C,S]=(0,o.useState)(x),[M,L]=(0,o.useState)(x);(0,l.E)(()=>{T.current=!1,k.current=x;for(let e=0;e<M.length;e++){let t=v(M[e]);E.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[M,E.length,E.join("-")]);let A=[];if(x!==C){let e=[...x];for(let t=0;t<M.length;t++){let r=M[t],n=v(r);E.includes(n)||(e.splice(t,0,r),A.push(r))}return"wait"===c&&A.length&&(e=A),L(b(e)),S(x),null}let{forceRender:j}=(0,o.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:M.map(e=>{let t=v(e),o=(!f||!!y)&&(x===M||E.includes(t));return(0,n.jsx)(p,{isPresent:o,initial:(!T.current||!!s)&&void 0,custom:r,presenceAffectsLayout:d,mode:c,root:h,onExitComplete:o?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==j||j(),L(k.current),f&&(null==w||w()),u&&u())},anchorX:g,children:e},t)})})}},28829:(e,t,r)=>{r.d(t,{E:()=>d,M:()=>u});var n=r(60699),o=r(20965),i=r(13600);let a="default",l="",s=new WeakMap;function u(e){if((0,n.un)()){if("default"===a){let t=(0,o.TW)(e);l=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}a="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";s.set(e,e.style[t]),e.style[t]="none"}}function d(e){if((0,n.un)())"disabled"===a&&(a="restoring",setTimeout(()=>{(0,i.v)(()=>{if("restoring"===a){let t=(0,o.TW)(e);"none"===t.documentElement.style.webkitUserSelect&&(t.documentElement.style.webkitUserSelect=l||""),l="",a="default"}})},300));else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&s.has(e)){let t=s.get(e),r="userSelect"in e.style?"userSelect":"webkitUserSelect";"none"===e.style[r]&&(e.style[r]=t),""===e.getAttribute("style")&&e.removeAttribute("style"),s.delete(e)}}},30100:(e,t,r)=>{r.d(t,{xQ:()=>i});var n=r(9585),o=r(98307);function i(e=!0){let t=(0,n.useContext)(o.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:a,register:l}=t,s=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return l(s)},[e]);let u=(0,n.useCallback)(()=>e&&a&&a(s),[s,a,e]);return!r&&a?[!1,u]:[!0]}},31081:(e,t,r)=>{r.d(t,{n:()=>n,o:()=>o});var[n,o]=(0,r(71242).q)({name:"ProviderContext",strict:!1})},32965:(e,t,r)=>{r.d(t,{k:()=>i});var n=r(18884),o=r(9585);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,o.useState)([]),i=(0,o.useCallback)(e=>{let t=e.target,o=Math.max(t.clientWidth,t.clientHeight);r(t=>[...t,{key:(0,n.Lz)(t.length.toString()),size:o,x:e.x-o/2,y:e.y-o/2}])},[]);return{ripples:t,onClear:(0,o.useCallback)(e=>{r(t=>t.filter(t=>t.key!==e))},[]),onPress:i,...e}}},33798:(e,t,r)=>{r.d(t,{B:()=>n});let n="undefined"!=typeof window},35808:(e,t,r)=>{r.d(t,{x:()=>i});var n=r(55150),o=r(89877);function i(e,t,r){let{style:i}=e,a={};for(let l in i)((0,n.S)(i[l])||t.style&&(0,n.S)(t.style[l])||(0,o.z)(l,e)||r?.getValue(l)?.liveStyle!==void 0)&&(a[l]=i[l]);return a}},36196:(e,t,r)=>{r.d(t,{E:()=>o});var n=r(9585);let o=r(33798).B?n.useLayoutEffect:n.useEffect},36584:(e,t,r)=>{r.d(t,{Y:()=>n});let n=(0,r(9585).createContext)({strict:!1})},36847:(e,t,r)=>{r.d(t,{D5:()=>n,Nf:()=>o});function n(){return!1}function o(){return!1}},37260:(e,t,r)=>{r.d(t,{A:()=>l,t:()=>a});let n=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],o=n.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";n.push('[tabindex]:not([tabindex="-1"]):not([disabled])');let i=n.join(':not([hidden]):not([tabindex="-1"]),');function a(e){return e.matches(o)}function l(e){return e.matches(i)}},37285:(e,t,r)=>{r.d(t,{Cc:()=>u,wR:()=>p});var n=r(9585);let o={prefix:String(Math.round(1e10*Math.random())),current:0},i=n.createContext(o),a=n.createContext(!1);"undefined"!=typeof window&&window.document&&window.document.createElement;let l=new WeakMap;function s(e=!1){let t=(0,n.useContext)(i),r=(0,n.useRef)(null);if(null===r.current&&!e){var o,a;let e=null==(a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||null==(o=a.ReactCurrentOwner)?void 0:o.current;if(e){let r=l.get(e);null==r?l.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==r.state&&(t.current=r.id,l.delete(e))}r.current=++t.current}return r.current}let u="function"==typeof n.useId?function(e){let t=n.useId(),[r]=(0,n.useState)(p()),i=r?"react-aria":`react-aria${o.prefix}`;return e||`${i}-${t}`}:function(e){let t=(0,n.useContext)(i),r=s(!!e),o=`react-aria${t.prefix}`;return e||`${o}-${r}`};function d(){return!1}function c(){return!0}function f(e){return()=>{}}function p(){return"function"==typeof n.useSyncExternalStore?n.useSyncExternalStore(f,d,c):(0,n.useContext)(a)}},38267:(e,t,r)=>{r.d(t,{O:()=>l,e:()=>a});var n=r(84046),o=r(93095),i=r(64558);function a(e){return(0,n.N)(e.animate)||i._.some(t=>(0,o.w)(e[t]))}function l(e){return!!(a(e)||e.variants)}},39098:(e,t,r)=>{r.d(t,{Q:()=>n});let n=(0,r(9585).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},42620:(e,t,r)=>{r.d(t,{m:()=>I});var n=r(19605),o=r(9585),i=r(16007),a=r(36584),l=r(39098);let s=(0,o.createContext)({});var u=r(38267),d=r(93095);function c(e){return Array.isArray(e)?e.join(" "):e}var f=r(33798),p=r(21320),g=r(16506);let m=Symbol.for("motionComponentSymbol");var v=r(97133),b=r(44294),h=r(98466),y=r(98307),w=r(26251),x=r(36196),E=r(55150),T=r(89877),k=r(651);let P=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function C(e,t,r){for(let n in t)(0,E.S)(t[n])||(0,T.z)(n,r)||(e[n]=t[n])}var S=r(14784);let M=()=>({...P(),attrs:{}});var L=r(5773),A=r(47535),j=r(81956),D=r(84046),O=r(68849),z=r(56411),W=r(83452);let F=e=>(t,r)=>{let n=(0,o.useContext)(s),i=(0,o.useContext)(y.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,n,o){return{latestValues:function(e,t,r,n){let o={},i=n(e,{});for(let e in i)o[e]=(0,W.u)(i[e]);let{initial:a,animate:l}=e,s=(0,u.e)(e),d=(0,u.O)(e);t&&d&&!s&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===l&&(l=t.animate));let c=!!r&&!1===r.initial,f=(c=c||!1===a)?l:a;if(f&&"boolean"!=typeof f&&!(0,D.N)(f)){let t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){let n=(0,O.a)(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(r,n,o,e),renderState:t()}})(e,t,n,i);return r?a():(0,z.M)(a)},R={useVisualState:F({scrapeMotionValuesFromProps:r(35808).x,createRenderState:P})},N={useVisualState:F({scrapeMotionValuesFromProps:r(70081).x,createRenderState:M})},I=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}(function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,r;let{preloadedFeatures:E,createVisualElement:T,useRender:k,useVisualState:P,Component:C}=e;function S(e,t){var r,g,m;let E,S={...(0,o.useContext)(l.Q),...e,layoutId:function(e){let{layoutId:t}=e,r=(0,o.useContext)(i.L).id;return r&&void 0!==t?r+"-"+t:t}(e)},{isStatic:M}=S,L=function(e){let{initial:t,animate:r}=function(e,t){if((0,u.e)(e)){let{initial:t,animate:r}=e;return{initial:!1===t||(0,d.w)(t)?t:void 0,animate:(0,d.w)(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(s));return(0,o.useMemo)(()=>({initial:t,animate:r}),[c(t),c(r)])}(e),A=P(e,M);if(!M&&f.B){g=0,m=0,(0,o.useContext)(a.Y).strict;let e=function(e){let{drag:t,layout:r}=p.B;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(S);E=e.MeasureLayout,L.visualElement=function(e,t,r,n,i){let{visualElement:u}=(0,o.useContext)(s),d=(0,o.useContext)(a.Y),c=(0,o.useContext)(y.t),f=(0,o.useContext)(l.Q).reducedMotion,p=(0,o.useRef)(null);n=n||d.renderer,!p.current&&n&&(p.current=n(e,{visualState:t,parent:u,props:r,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:f}));let g=p.current,m=(0,o.useContext)(w.N);g&&!g.projection&&i&&("html"===g.type||"svg"===g.type)&&function(e,t,r,n){let{layoutId:o,layout:i,drag:a,dragConstraints:l,layoutScroll:s,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:o,layout:i,alwaysMeasureLayout:!!a||l&&(0,v.X)(l),visualElement:e,animationType:"string"==typeof i?i:"both",initialPromotionConfig:n,crossfade:d,layoutScroll:s,layoutRoot:u})}(p.current,r,i,m);let E=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{g&&E.current&&g.update(r,c)});let T=r[h.n],k=(0,o.useRef)(!!T&&!window.MotionHandoffIsComplete?.(T)&&window.MotionHasOptimisedAnimation?.(T));return(0,x.E)(()=>{g&&(E.current=!0,window.MotionIsMounted=!0,g.updateFeatures(),b.k.render(g.render),k.current&&g.animationState&&g.animationState.animateChanges())}),(0,o.useEffect)(()=>{g&&(!k.current&&g.animationState&&g.animationState.animateChanges(),k.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(T)}),k.current=!1))}),g}(C,A,S,T,e.ProjectionNode)}return(0,n.jsxs)(s.Provider,{value:L,children:[E&&L.visualElement?(0,n.jsx)(E,{visualElement:L.visualElement,...S}):null,k(C,e,(r=L.visualElement,(0,o.useCallback)(e=>{e&&A.onMount&&A.onMount(e),r&&(e?r.mount(e):r.unmount()),t&&("function"==typeof t?t(e):(0,v.X)(t)&&(t.current=e))},[r])),A,M,L.visualElement)]})}E&&(0,g.Y)(E),S.displayName="motion.".concat("string"==typeof C?C:"create(".concat(null!=(r=null!=(t=C.displayName)?t:C.name)?r:"",")"));let M=(0,o.forwardRef)(S);return M[m]=C,M}({...(0,j.Q)(e)?N:R,preloadedFeatures:void 0,useRender:function(e=!1){return(t,r,n,{latestValues:i},a)=>{let l=((0,j.Q)(t)?function(e,t,r,n){let i=(0,o.useMemo)(()=>{let r=M();return(0,S.B)(r,t,(0,L.n)(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};C(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return C(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let r=P();return(0,k.O)(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,a,t),s=(0,A.J)(r,"string"==typeof t,e),u=t!==o.Fragment?{...s,...l,ref:n}:{},{children:d}=r,c=(0,o.useMemo)(()=>(0,E.S)(d)?d.get():d,[d]);return(0,o.createElement)(t,{...u,children:c})}}(t),createVisualElement:void 0,Component:e})})},44294:(e,t,r)=>{r.d(t,{k:()=>n});let{schedule:n}=(0,r(74908).I)(queueMicrotask,!1)},47535:(e,t,r)=>{r.d(t,{J:()=>l,D:()=>a});let n=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function o(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||n.has(e)}let i=e=>!o(e);function a(e){"function"==typeof e&&(i=t=>t.startsWith("on")?!o(t):e(t))}try{a(require("@emotion/is-prop-valid").default)}catch{}function l(e,t,r){let n={};for(let a in e)("values"!==a||"object"!=typeof e.values)&&(i(a)||!0===r&&o(a)||!t&&!o(a)||e.draggable&&a.startsWith("onDrag"))&&(n[a]=e[a]);return n}},48782:(e,t,r)=>{r.d(t,{X4:()=>i,ai:()=>o,hs:()=>a});var n=r(57272);let o={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},i={...o,transform:e=>(0,n.q)(0,1,e)},a={...o,default:1}},50689:(e,t,r)=>{r.d(t,{bq:()=>a,sD:()=>i,wt:()=>l});var n=r(20965),o=r(36847);function i(e,t){if(!(0,o.Nf)())return!!t&&!!e&&e.contains(t);if(!e||!t)return!1;let r=t;for(;null!==r;){if(r===e)return!0;r="SLOT"===r.tagName&&r.assignedSlot?r.assignedSlot.parentNode:(0,n.Ng)(r)?r.host:r.parentNode}return!1}let a=(e=document)=>{var t;if(!(0,o.Nf)())return e.activeElement;let r=e.activeElement;for(;r&&"shadowRoot"in r&&(null==(t=r.shadowRoot)?void 0:t.activeElement);)r=r.shadowRoot.activeElement;return r};function l(e){return(0,o.Nf)()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}},55150:(e,t,r)=>{r.d(t,{S:()=>n});let n=e=>!!(e&&e.getVelocity)},56411:(e,t,r)=>{r.d(t,{M:()=>o});var n=r(9585);function o(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},56457:(e,t,r)=>{r.d(t,{$1:()=>i,n3:()=>l,oT:()=>a,wA:()=>o,zb:()=>n});var n=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],o=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],i=["outline-none","ring-2","ring-focus","ring-offset-2","ring-offset-background"],a={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]},l=["font-inherit","text-[100%]","leading-[1.15]","m-0","p-0","overflow-visible","box-border","absolute","top-0","w-full","h-full","opacity-[0.0001]","z-[1]","cursor-pointer","disabled:cursor-default"]},56542:(e,t,r)=>{r.d(t,{Fe:()=>d,HI:()=>f,_h:()=>p,pg:()=>l,rd:()=>s,sU:()=>u});var n=r(61133),o=r(60699),i=r(9585);let a=(0,i.createContext)({isNative:!0,open:function(e,t){c(e,e=>d(e,t))},useHref:e=>e});function l(e){let{children:t,navigate:r,useHref:n}=e,o=(0,i.useMemo)(()=>({isNative:!1,open:(e,t,n,o)=>{c(e,e=>{u(e,t)?r(n,o):d(e,t)})},useHref:n||(e=>e)}),[r,n]);return i.createElement(a.Provider,{value:o},t)}function s(){return(0,i.useContext)(a)}function u(e,t){let r=e.getAttribute("target");return(!r||"_self"===r)&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function d(e,t,r=!0){var i,a;let{metaKey:l,ctrlKey:s,altKey:u,shiftKey:c}=t;(0,o.gm)()&&(null==(a=window.event)||null==(i=a.type)?void 0:i.startsWith("key"))&&"_blank"===e.target&&((0,o.cX)()?l=!0:s=!0);let f=(0,o.Tc)()&&(0,o.cX)()&&!(0,o.bh)()&&1?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:l,ctrlKey:s,altKey:u,shiftKey:c}):new MouseEvent("click",{metaKey:l,ctrlKey:s,altKey:u,shiftKey:c,bubbles:!0,cancelable:!0});d.isOpening=r,(0,n.e)(e),e.dispatchEvent(f),d.isOpening=!1}function c(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let r=document.createElement("a");r.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(r.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(r.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(r.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(r.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(r.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(r),t(r),e.removeChild(r)}}function f(e){var t;let r=s().useHref(null!=(t=e.href)?t:"");return{"data-href":e.href?r:void 0,"data-target":e.target,"data-rel":e.rel,"data-download":e.download,"data-ping":e.ping,"data-referrer-policy":e.referrerPolicy}}function p(e){var t;let r=s().useHref(null!=(t=null==e?void 0:e.href)?t:"");return{href:(null==e?void 0:e.href)?r:void 0,target:null==e?void 0:e.target,rel:null==e?void 0:e.rel,download:null==e?void 0:e.download,ping:null==e?void 0:e.ping,referrerPolicy:null==e?void 0:e.referrerPolicy}}d.isOpening=!1},57272:(e,t,r)=>{r.d(t,{q:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},58091:(e,t,r)=>{r.d(t,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},60077:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(9585);function o(){let e=(0,n.useRef)(new Map),t=(0,n.useCallback)((t,r,n,o)=>{let i=(null==o?void 0:o.once)?(...t)=>{e.current.delete(n),n(...t)}:n;e.current.set(n,{type:r,eventTarget:t,fn:i,options:o}),t.addEventListener(r,i,o)},[]),r=(0,n.useCallback)((t,r,n,o)=>{var i;let a=(null==(i=e.current.get(n))?void 0:i.fn)||n;t.removeEventListener(r,a,o),e.current.delete(n)},[]),o=(0,n.useCallback)(()=>{e.current.forEach((e,t)=>{r(e.eventTarget,e.type,t,e.options)})},[r]);return(0,n.useEffect)(()=>o,[o]),{addGlobalListener:t,removeGlobalListener:r,removeAllGlobalListeners:o}}},60144:(e,t,r)=>{r.d(t,{d:()=>P});var n=r(2215),o=r(28829),i=r(80029);function a(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function l(e,t,r){var n=a(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}var s=r(96539),u=r(70525),d=r(60077),c=r(7672),f=r(50689),p=r(20965),g=r(26423),m=r(60699),v=r(56542),b=r(12877),h=r(61133);r(23220);var y=r(9585),w=new WeakMap;class x{continuePropagation(){l(this,w,!1)}get shouldStopPropagation(){var e;return e=a(this,w,"get"),e.get?e.get.call(this):e.value}constructor(e,t,r,n){var o;!function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,w,{writable:!0,value:void 0}),l(this,w,!0);let i=null!=(o=null==n?void 0:n.target)?o:r.currentTarget,a=null==i?void 0:i.getBoundingClientRect(),s,u=0,d,c=null;null!=r.clientX&&null!=r.clientY&&(d=r.clientX,c=r.clientY),a&&(null!=d&&null!=c?(s=d-a.left,u=c-a.top):(s=a.width/2,u=a.height/2)),this.type=e,this.pointerType=t,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=u}}let E=Symbol("linkClicked"),T="react-aria-pressable-style",k="data-react-aria-pressable";function P(e){let{onPress:t,onPressChange:r,onPressStart:a,onPressEnd:l,onPressUp:w,onClick:P,isDisabled:A,isPressed:j,preventFocusOnPress:D,shouldCancelOnPointerExit:O,allowTextSelectionOnPress:z,ref:W,...F}=function(e){let t=(0,y.useContext)(i.F);if(t){let{register:r,...n}=t;e=(0,s.v)(n,e),r()}return(0,u.w)(t,e.ref),e}(e),[R,N]=(0,y.useState)(!1),I=(0,y.useRef)({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:H,removeAllGlobalListeners:K}=(0,d.A)(),$=(0,c.J)((e,t)=>{let n=I.current;if(A||n.didFirePressStart)return!1;let o=!0;if(n.isTriggeringEvent=!0,a){let r=new x("pressstart",t,e);a(r),o=r.shouldStopPropagation}return r&&r(!0),n.isTriggeringEvent=!1,n.didFirePressStart=!0,N(!0),o}),V=(0,c.J)((e,n,o=!0)=>{let i=I.current;if(!i.didFirePressStart)return!1;i.didFirePressStart=!1,i.isTriggeringEvent=!0;let a=!0;if(l){let t=new x("pressend",n,e);l(t),a=t.shouldStopPropagation}if(r&&r(!1),N(!1),t&&o&&!A){let r=new x("press",n,e);t(r),a&&(a=r.shouldStopPropagation)}return i.isTriggeringEvent=!1,a}),B=(0,c.J)((e,t)=>{let r=I.current;if(A)return!1;if(w){r.isTriggeringEvent=!0;let n=new x("pressup",t,e);return w(n),r.isTriggeringEvent=!1,n.shouldStopPropagation}return!0}),G=(0,c.J)(e=>{let t=I.current;if(t.isPressed&&t.target){for(let r of(t.didFirePressStart&&null!=t.pointerType&&V(M(t.target,e),t.pointerType,!1),t.isPressed=!1,t.isOverTarget=!1,t.activePointerId=null,t.pointerType=null,K(),z||(0,o.E)(t.target),t.disposables))r();t.disposables=[]}}),U=(0,c.J)(e=>{O&&G(e)}),X=(0,c.J)(e=>{null==P||P(e)}),Y=(0,c.J)((e,t)=>{if(P){let r=new MouseEvent("click",e);(0,n.o1)(r,t),P((0,n.eg)(r))}}),_=(0,y.useMemo)(()=>{let e=I.current,t={onKeyDown(t){if(S(t.nativeEvent,t.currentTarget)&&(0,f.sD)(t.currentTarget,(0,f.wt)(t.nativeEvent))){var n;L((0,f.wt)(t.nativeEvent),t.key)&&t.preventDefault();let o=!0;if(!e.isPressed&&!t.repeat){e.target=t.currentTarget,e.isPressed=!0,e.pointerType="keyboard",o=$(t,"keyboard");let n=t.currentTarget;H((0,p.TW)(t.currentTarget),"keyup",(0,g.c)(t=>{S(t,n)&&!t.repeat&&(0,f.sD)(n,(0,f.wt)(t))&&e.target&&B(M(e.target,t),"keyboard")},r),!0)}o&&t.stopPropagation(),t.metaKey&&(0,m.cX)()&&(null==(n=e.metaKeyEvents)||n.set(t.key,t.nativeEvent))}else"Meta"===t.key&&(e.metaKeyEvents=new Map)},onClick(t){if((!t||(0,f.sD)(t.currentTarget,(0,f.wt)(t.nativeEvent)))&&t&&0===t.button&&!e.isTriggeringEvent&&!v.Fe.isOpening){let r=!0;if(A&&t.preventDefault(),!e.ignoreEmulatedMouseEvents&&!e.isPressed&&("virtual"===e.pointerType||(0,b.Y)(t.nativeEvent))){let e=$(t,"virtual"),n=B(t,"virtual"),o=V(t,"virtual");X(t),r=e&&n&&o}else if(e.isPressed&&"keyboard"!==e.pointerType){let n=e.pointerType||t.nativeEvent.pointerType||"virtual",o=B(M(t.currentTarget,t),n),i=V(M(t.currentTarget,t),n,!0);r=o&&i,e.isOverTarget=!1,X(t),G(t)}e.ignoreEmulatedMouseEvents=!1,r&&t.stopPropagation()}}},r=t=>{var r,n,o;if(e.isPressed&&e.target&&S(t,e.target)){L((0,f.wt)(t),t.key)&&t.preventDefault();let r=(0,f.wt)(t),o=(0,f.sD)(e.target,(0,f.wt)(t));V(M(e.target,t),"keyboard",o),o&&Y(t,e.target),K(),"Enter"!==t.key&&C(e.target)&&(0,f.sD)(e.target,r)&&!t[E]&&(t[E]=!0,(0,v.Fe)(e.target,t,!1)),e.isPressed=!1,null==(n=e.metaKeyEvents)||n.delete(t.key)}else if("Meta"===t.key&&(null==(r=e.metaKeyEvents)?void 0:r.size)){let t=e.metaKeyEvents;for(let r of(e.metaKeyEvents=void 0,t.values()))null==(o=e.target)||o.dispatchEvent(new KeyboardEvent("keyup",r))}};if("undefined"!=typeof PointerEvent){t.onPointerDown=t=>{if(0!==t.button||!(0,f.sD)(t.currentTarget,(0,f.wt)(t.nativeEvent)))return;if((0,b.P)(t.nativeEvent)){e.pointerType="virtual";return}e.pointerType=t.pointerType;let n=!0;if(!e.isPressed){e.isPressed=!0,e.isOverTarget=!0,e.activePointerId=t.pointerId,e.target=t.currentTarget,z||(0,o.M)(e.target),n=$(t,e.pointerType);let a=(0,f.wt)(t.nativeEvent);"releasePointerCapture"in a&&a.releasePointerCapture(t.pointerId),H((0,p.TW)(t.currentTarget),"pointerup",r,!1),H((0,p.TW)(t.currentTarget),"pointercancel",i,!1)}n&&t.stopPropagation()},t.onMouseDown=t=>{if((0,f.sD)(t.currentTarget,(0,f.wt)(t.nativeEvent))&&0===t.button){if(D){let r=(0,n.LE)(t.target);r&&e.disposables.push(r)}t.stopPropagation()}},t.onPointerUp=t=>{(0,f.sD)(t.currentTarget,(0,f.wt)(t.nativeEvent))&&"virtual"!==e.pointerType&&(0!==t.button||e.isPressed||B(t,e.pointerType||t.pointerType))},t.onPointerEnter=t=>{t.pointerId===e.activePointerId&&e.target&&!e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!0,$(M(e.target,t),e.pointerType))},t.onPointerLeave=t=>{t.pointerId===e.activePointerId&&e.target&&e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,V(M(e.target,t),e.pointerType,!1),U(t))};let r=t=>{if(t.pointerId===e.activePointerId&&e.isPressed&&0===t.button&&e.target){if((0,f.sD)(e.target,(0,f.wt)(t))&&null!=e.pointerType){let r=!1,n=setTimeout(()=>{e.isPressed&&e.target instanceof HTMLElement&&(r?G(t):((0,h.e)(e.target),e.target.click()))},80);H(t.currentTarget,"click",()=>r=!0,!0),e.disposables.push(()=>clearTimeout(n))}else G(t);e.isOverTarget=!1}},i=e=>{G(e)};t.onDragStart=e=>{(0,f.sD)(e.currentTarget,(0,f.wt)(e.nativeEvent))&&G(e)}}return t},[H,A,D,K,z,G,U,V,$,B,X,Y]);return(0,y.useEffect)(()=>{if(!W)return;let e=(0,p.TW)(W.current);if(!e||!e.head||e.getElementById(T))return;let t=e.createElement("style");t.id=T,t.textContent=`
@layer {
  [${k}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),e.head.prepend(t)},[W]),(0,y.useEffect)(()=>{let e=I.current;return()=>{var t;for(let r of(z||(0,o.E)(null!=(t=e.target)?t:void 0),e.disposables))r();e.disposables=[]}},[z]),{isPressed:j||R,pressProps:(0,s.v)(F,_,{[k]:!0})}}function C(e){return"A"===e.tagName&&e.hasAttribute("href")}function S(e,t){let{key:r,code:n}=e,o=t.getAttribute("role");return("Enter"===r||" "===r||"Spacebar"===r||"Space"===n)&&!(t instanceof(0,p.mD)(t).HTMLInputElement&&!j(t,r)||t instanceof(0,p.mD)(t).HTMLTextAreaElement||t.isContentEditable)&&!(("link"===o||!o&&C(t))&&"Enter"!==r)}function M(e,t){let r=t.clientX,n=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function L(e,t){return e instanceof HTMLInputElement?!j(e,t):!(e instanceof HTMLInputElement)&&(e instanceof HTMLButtonElement?"submit"!==e.type&&"reset"!==e.type:!C(e))}let A=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function j(e,t){return"checkbox"===e.type||"radio"===e.type?" "===t:A.has(e.type)}},60699:(e,t,r)=>{function n(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null==(t=window.navigator.userAgentData)?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function o(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null==(t=window.navigator.userAgentData)?void 0:t.platform)||window.navigator.platform)}function i(e){let t=null;return()=>(null==t&&(t=e()),t)}r.d(t,{H8:()=>f,Tc:()=>c,bh:()=>s,cX:()=>a,gm:()=>g,lg:()=>d,m0:()=>p,un:()=>u});let a=i(function(){return o(/^Mac/i)}),l=i(function(){return o(/^iPhone/i)}),s=i(function(){return o(/^iPad/i)||a()&&navigator.maxTouchPoints>1}),u=i(function(){return l()||s()}),d=i(function(){return a()||u()}),c=i(function(){return n(/AppleWebKit/i)&&!f()}),f=i(function(){return n(/Chrome/i)}),p=i(function(){return n(/Android/i)}),g=i(function(){return n(/Firefox/i)})},61133:(e,t,r)=>{function n(e){if(function(){if(null==o){o=!1;try{document.createElement("div").focus({get preventScroll(){return o=!0,!0}})}catch{}}return o}())e.focus({preventScroll:!0});else{let t=function(e){let t=e.parentNode,r=[],n=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==n;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&r.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return n instanceof HTMLElement&&r.push({element:n,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}),r}(e);e.focus(),function(e){for(let{element:t,scrollTop:r,scrollLeft:n}of e)t.scrollTop=r,t.scrollLeft=n}(t)}}r.d(t,{e:()=>n});let o=null},64558:(e,t,r)=>{r.d(t,{U:()=>n,_:()=>o});let n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...n]},68849:(e,t,r)=>{function n(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,o){if("function"==typeof t){let[i,a]=n(o);t=t(void 0!==r?r:e.custom,i,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,a]=n(o);t=t(void 0!==r?r:e.custom,i,a)}return t}r.d(t,{a:()=>o})},68969:(e,t,r)=>{r.d(t,{G:()=>n});function n(e){return"object"==typeof e&&null!==e}},70081:(e,t,r)=>{r.d(t,{x:()=>a});var n=r(55150),o=r(17199),i=r(35808);function a(e,t,r){let a=(0,i.x)(e,t,r);for(let r in e)((0,n.S)(e[r])||(0,n.S)(t[r]))&&(a[-1!==o.U.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return a}},70525:(e,t,r)=>{r.d(t,{w:()=>o});var n=r(73360);function o(e,t){(0,n.N)(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}},71242:(e,t,r)=>{r.d(t,{q:()=>o});var n=r(9585);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{strict:t=!0,errorMessage:r="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:o}=e,i=n.createContext(void 0);return i.displayName=o,[i.Provider,function e(){var o;let a=n.useContext(i);if(!a&&t){let t=Error(r);throw t.name="ContextError",null==(o=Error.captureStackTrace)||o.call(Error,t,e),t}return a},i]}},73360:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(9585);let o="undefined"!=typeof document?n.useLayoutEffect:()=>{}},73571:(e,t,r)=>{function n(e){if(!e)return;let t=!0;return r=>{e({...r,preventDefault(){r.preventDefault()},isDefaultPrevented:()=>r.isDefaultPrevented(),stopPropagation(){t=!0},continuePropagation(){t=!1},isPropagationStopped:()=>t}),t&&r.stopPropagation()}}function o(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:n(e.onKeyDown),onKeyUp:n(e.onKeyUp)}}}r.d(t,{d:()=>o})},74476:(e,t,r)=>{r.d(t,{R:()=>s});var n=r(2215),o=r(9585),i=r(60077),a=r(20965),l=r(50689);function s(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:s,onFocusWithinChange:u}=e,d=(0,o.useRef)({isFocusWithin:!1}),{addGlobalListener:c,removeAllGlobalListeners:f}=(0,i.A)(),p=(0,o.useCallback)(e=>{e.currentTarget.contains(e.target)&&d.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(d.current.isFocusWithin=!1,f(),r&&r(e),u&&u(!1))},[r,u,d,f]),g=(0,n.yB)(p),m=(0,o.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=(0,a.TW)(e.target),r=(0,l.bq)(t);if(!d.current.isFocusWithin&&r===(0,l.wt)(e.nativeEvent)){s&&s(e),u&&u(!0),d.current.isFocusWithin=!0,g(e);let r=e.currentTarget;c(t,"focus",e=>{if(d.current.isFocusWithin&&!(0,l.sD)(r,e.target)){let o=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});(0,n.o1)(o,r),p((0,n.eg)(o))}},{capture:!0})}},[s,u,g,c,p]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:m,onBlur:p}}}},74908:(e,t,r)=>{r.d(t,{I:()=>a});var n=r(84953);let o=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var i=r(58091);function a(e,t){let r=!1,a=!0,l={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,u=o.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,o=!1,a=!1,l=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},u=0;function d(t){l.has(t)&&(c.schedule(t),e()),u++,t(s)}let c={schedule:(e,t=!1,i=!1)=>{let a=i&&o?r:n;return t&&l.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),l.delete(e)},process:e=>{if(s=e,o){a=!0;return}o=!0,[r,n]=[n,r],r.forEach(d),t&&i.Q.value&&i.Q.value.frameloop[t].push(u),u=0,r.clear(),o=!1,a&&(a=!1,c.process(e))}};return c}(s,t?r:void 0),e),{}),{setup:d,read:c,resolveKeyframes:f,preUpdate:p,update:g,preRender:m,render:v,postRender:b}=u,h=()=>{let o=n.W.useManualTiming?l.timestamp:performance.now();r=!1,n.W.useManualTiming||(l.delta=a?1e3/60:Math.max(Math.min(o-l.timestamp,40),1)),l.timestamp=o,l.isProcessing=!0,d.process(l),c.process(l),f.process(l),p.process(l),g.process(l),m.process(l),v.process(l),b.process(l),l.isProcessing=!1,r&&t&&(a=!1,e(h))},y=()=>{r=!0,a=!0,l.isProcessing||e(h)};return{schedule:o.reduce((e,t)=>{let n=u[t];return e[t]=(e,t=!1,o=!1)=>(r||y(),n.schedule(e,t,o)),e},{}),cancel:e=>{for(let t=0;t<o.length;t++)u[o[t]].cancel(e)},state:l,steps:u}}},78749:(e,t,r)=>{r.d(t,{Wc:()=>d});var n=r(84702),o=r(80481),i=r(73571),a=r(70525),l=r(96539),s=r(9585);let u=s.createContext(null);function d(e,t){let{focusProps:r}=(0,o.i)(e),{keyboardProps:d}=(0,i.d)(e),c=(0,l.v)(r,d),f=function(e){let t=(0,s.useContext)(u)||{};(0,a.w)(t,e);let{ref:r,...n}=t;return n}(t),p=e.isDisabled?{}:f,g=(0,s.useRef)(e.autoFocus);(0,s.useEffect)(()=>{g.current&&t.current&&(0,n.l)(t.current),g.current=!1},[t]);let m=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(m=void 0),{focusableProps:(0,l.v)({...c,tabIndex:m},p)}}},80029:(e,t,r)=>{r.d(t,{F:()=>n});let n=r(9585).createContext({register:()=>{}});n.displayName="PressResponderContext"},80481:(e,t,r)=>{r.d(t,{i:()=>l});var n=r(2215),o=r(9585),i=r(20965),a=r(50689);function l(e){let{isDisabled:t,onFocus:r,onBlur:l,onFocusChange:s}=e,u=(0,o.useCallback)(e=>{if(e.target===e.currentTarget)return l&&l(e),s&&s(!1),!0},[l,s]),d=(0,n.yB)(u),c=(0,o.useCallback)(e=>{let t=(0,i.TW)(e.target),n=t?(0,a.bq)(t):(0,a.bq)();e.target===e.currentTarget&&n===(0,a.wt)(e.nativeEvent)&&(r&&r(e),s&&s(!0),d(e))},[s,r,d]);return{focusProps:{onFocus:!t&&(r||s||l)?c:void 0,onBlur:!t&&(l||s)?u:void 0}}}},80897:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(19605),o=r(9585),i=r(36584),a=r(16506);function l(e){let{children:t,features:r,strict:l=!1}=e,[,u]=(0,o.useState)(!s(r)),d=(0,o.useRef)(void 0);if(!s(r)){let{renderer:e,...t}=r;d.current=e,(0,a.Y)(t)}return(0,o.useEffect)(()=>{s(r)&&r().then(e=>{let{renderer:t,...r}=e;(0,a.Y)(r),d.current=t,u(!0)})},[]),(0,n.jsx)(i.Y.Provider,{value:{renderer:d.current,strict:l},children:t})}function s(e){return"function"==typeof e}},81956:(e,t,r)=>{r.d(t,{Q:()=>o});let n=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o(e){if("string"!=typeof e||e.includes("-"));else if(n.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}},83452:(e,t,r)=>{r.d(t,{u:()=>o});var n=r(55150);function o(e){return(0,n.S)(e)?e.get():e}},84046:(e,t,r)=>{r.d(t,{N:()=>n});function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}},84702:(e,t,r)=>{r.d(t,{l:()=>s});var n=r(99275),o=r(20965),i=r(50689),a=r(13600),l=r(61133);function s(e){let t=(0,o.TW)(e),r=(0,i.bq)(t);"virtual"===(0,n.ME)()?(0,a.v)(()=>{(0,i.bq)(t)===r&&e.isConnected&&(0,l.e)(e)}):(0,l.e)(e)}},84953:(e,t,r)=>{r.d(t,{W:()=>n});let n={}},85823:(e,t,r)=>{r.d(t,{M:()=>c});var n=r(60077),o=r(20965),i=r(50689),a=r(9585);let l=!1,s=0;function u(e){"touch"===e.pointerType&&(l=!0,setTimeout(()=>{l=!1},50))}function d(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent&&document.addEventListener("pointerup",u),s++,()=>{--s>0||"undefined"!=typeof PointerEvent&&document.removeEventListener("pointerup",u)}}function c(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:s,isDisabled:u}=e,[c,f]=(0,a.useState)(!1),p=(0,a.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,a.useEffect)(d,[]);let{addGlobalListener:g,removeAllGlobalListeners:m}=(0,n.A)(),{hoverProps:v,triggerHoverEnd:b}=(0,a.useMemo)(()=>{let e=(e,a)=>{if(p.pointerType=a,u||"touch"===a||p.isHovered||!e.currentTarget.contains(e.target))return;p.isHovered=!0;let l=e.currentTarget;p.target=l,g((0,o.TW)(e.target),"pointerover",e=>{p.isHovered&&p.target&&!(0,i.sD)(p.target,e.target)&&n(e,e.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:l,pointerType:a}),r&&r(!0),f(!0)},n=(e,t)=>{let n=p.target;p.pointerType="",p.target=null,"touch"!==t&&p.isHovered&&n&&(p.isHovered=!1,m(),s&&s({type:"hoverend",target:n,pointerType:t}),r&&r(!1),f(!1))},a={};return"undefined"!=typeof PointerEvent&&(a.onPointerEnter=t=>{l&&"mouse"===t.pointerType||e(t,t.pointerType)},a.onPointerLeave=e=>{!u&&e.currentTarget.contains(e.target)&&n(e,e.pointerType)}),{hoverProps:a,triggerHoverEnd:n}},[t,r,s,u,p,g,m]);return(0,a.useEffect)(()=>{u&&b({currentTarget:p.target},p.pointerType)},[u]),{hoverProps:v,isHovered:c}}},89826:(e,t,r)=>{r.d(t,{$:()=>l});let n=new Set(["id"]),o=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),i=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),a=/^(data-.*)$/;function l(e,t={}){let{labelable:r,isLink:s,propNames:u}=t,d={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&o.has(t)||s&&i.has(t)||(null==u?void 0:u.has(t))||a.test(t))&&(d[t]=e[t]);return d}},89877:(e,t,r)=>{r.d(t,{z:()=>i});var n=r(17199),o=r(16383);function i(e,{layout:t,layoutId:r}){return n.f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!o.H[e]||"opacity"===e)}},90890:(e,t,r)=>{r.d(t,{l:()=>l});var n=r(96539),o=r(89826),i=r(78749),a=r(60144);function l(e,t){let r,{elementType:l="button",isDisabled:s,onPress:u,onPressStart:d,onPressEnd:c,onPressChange:f,preventFocusOnPress:p,allowFocusWhenDisabled:g,onClick:m,href:v,target:b,rel:h,type:y="button",allowTextSelectionOnPress:w}=e;r="button"===l?{type:y,disabled:s}:{role:"button",href:"a"!==l||s?void 0:v,target:"a"===l?b:void 0,type:"input"===l?y:void 0,disabled:"input"===l?s:void 0,"aria-disabled":s&&"input"!==l?s:void 0,rel:"a"===l?h:void 0};let{pressProps:x,isPressed:E}=(0,a.d)({onClick:m,onPressStart:d,onPressEnd:c,onPressChange:f,onPress:u,isDisabled:s,preventFocusOnPress:p,allowTextSelectionOnPress:w,ref:t}),{focusableProps:T}=(0,i.Wc)(e,t);g&&(T.tabIndex=s?-1:T.tabIndex);let k=(0,n.v)(T,x,(0,o.$)(e,{labelable:!0}));return{isPressed:E,buttonProps:(0,n.v)(r,k,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}},92610:(e,t,r)=>{r.d(t,{tv:()=>ex});var n=["small","medium","large"],o={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:n,borderRadius:n},classGroups:{shadow:[{shadow:n}],"font-size":[{text:["tiny",...n]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}},i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=e=>!e||"object"!=typeof e||0===Object.keys(e).length,l=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function s(e){let t=[];return function e(t,r){t.forEach(function(t){Array.isArray(t)?e(t,r):r.push(t)})}(e,t),t}var u=(...e)=>s(e).filter(Boolean),d=(e,t)=>{let r={},n=Object.keys(e),o=Object.keys(t);for(let i of n)if(o.includes(i)){let n=e[i],o=t[i];Array.isArray(n)||Array.isArray(o)?r[i]=u(o,n):"object"==typeof n&&"object"==typeof o?r[i]=d(n,o):r[i]=o+" "+n}else r[i]=e[i];for(let e of o)n.includes(e)||(r[e]=t[e]);return r},c=e=>e&&"string"==typeof e?e.replace(/\s+/g," ").trim():e;let f=e=>{let t=v(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),p(r,t)||m(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},p=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?p(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},g=/^\[(.+)\]$/,m=e=>{if(g.test(e)){let t=g.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},v=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return w(Object.entries(e.classGroups),r).forEach(([e,r])=>{b(r,n,e,t)}),n},b=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:h(t,e)).classGroupId=r;return}if("function"==typeof e)return y(e)?void b(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{b(o,h(t,e),r,n)})})},h=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},y=e=>e.isThemeGetter,w=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,x=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},E=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],i=t.length,a=e=>{let r,a=[],l=0,s=0;for(let u=0;u<e.length;u++){let d=e[u];if(0===l){if(d===o&&(n||e.slice(u,u+i)===t)){a.push(e.slice(s,u)),s=u+i;continue}if("/"===d){r=u;continue}}"["===d?l++:"]"===d&&l--}let u=0===a.length?e:e.substring(s),d=u.startsWith("!"),c=d?u.substring(1):u;return{modifiers:a,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};return r?e=>r({className:e,parseClassName:a}):a},T=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},k=e=>({cache:x(e.cacheSize),parseClassName:E(e),...f(e)}),P=/\s+/,C=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,i=[],a=e.trim().split(P),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:s,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:c}=r(t),f=!!c,p=n(f?d.substring(0,c):d);if(!p){if(!f||!(p=n(d))){l=t+(l.length>0?" "+l:l);continue}f=!1}let g=T(s).join(":"),m=u?g+"!":g,v=m+p;if(i.includes(v))continue;i.push(v);let b=o(p,f);for(let e=0;e<b.length;++e){let t=b[e];i.push(m+t)}l=t+(l.length>0?" "+l:l)}return l};function S(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=M(e))&&(n&&(n+=" "),n+=t);return n}let M=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=M(e[n]))&&(r&&(r+=" "),r+=t);return r};function L(e,...t){let r,n,o,i=function(l){return n=(r=k(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=a,a(l)};function a(e){let t=n(e);if(t)return t;let i=C(e,r);return o(e,i),i}return function(){return i(S.apply(null,arguments))}}let A=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},j=/^\[(?:([a-z-]+):)?(.+)\]$/i,D=/^\d+\/\d+$/,O=new Set(["px","full","screen"]),z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,W=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,F=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,R=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,N=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=e=>K(e)||O.has(e)||D.test(e),H=e=>ee(e,"length",et),K=e=>!!e&&!Number.isNaN(Number(e)),$=e=>ee(e,"number",K),V=e=>!!e&&Number.isInteger(Number(e)),B=e=>e.endsWith("%")&&K(e.slice(0,-1)),G=e=>j.test(e),U=e=>z.test(e),X=new Set(["length","size","percentage"]),Y=e=>ee(e,X,er),_=e=>ee(e,"position",er),q=new Set(["image","url"]),Q=e=>ee(e,q,eo),J=e=>ee(e,"",en),Z=()=>!0,ee=(e,t,r)=>{let n=j.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},et=e=>W.test(e)&&!F.test(e),er=()=>!1,en=e=>R.test(e),eo=e=>N.test(e);Symbol.toStringTag;let ei=()=>{let e=A("colors"),t=A("spacing"),r=A("blur"),n=A("brightness"),o=A("borderColor"),i=A("borderRadius"),a=A("borderSpacing"),l=A("borderWidth"),s=A("contrast"),u=A("grayscale"),d=A("hueRotate"),c=A("invert"),f=A("gap"),p=A("gradientColorStops"),g=A("gradientColorStopPositions"),m=A("inset"),v=A("margin"),b=A("opacity"),h=A("padding"),y=A("saturate"),w=A("scale"),x=A("sepia"),E=A("skew"),T=A("space"),k=A("translate"),P=()=>["auto","contain","none"],C=()=>["auto","hidden","clip","visible","scroll"],S=()=>["auto",G,t],M=()=>[G,t],L=()=>["",I,H],j=()=>["auto",K,G],D=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],O=()=>["solid","dashed","dotted","double","none"],z=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>["start","end","center","between","around","evenly","stretch"],F=()=>["","0",G],R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],N=()=>[K,G];return{cacheSize:500,separator:":",theme:{colors:[Z],spacing:[I,H],blur:["none","",U,G],brightness:N(),borderColor:[e],borderRadius:["none","","full",U,G],borderSpacing:M(),borderWidth:L(),contrast:N(),grayscale:F(),hueRotate:N(),invert:F(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[B,H],inset:S(),margin:S(),opacity:N(),padding:M(),saturate:N(),scale:N(),sepia:F(),skew:N(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",G]}],container:["container"],columns:[{columns:[U]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...D(),G]}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",V,G]}],basis:[{basis:S()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",G]}],grow:[{grow:F()}],shrink:[{shrink:F()}],order:[{order:["first","last","none",V,G]}],"grid-cols":[{"grid-cols":[Z]}],"col-start-end":[{col:["auto",{span:["full",V,G]},G]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Z]}],"row-start-end":[{row:["auto",{span:[V,G]},G]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",G]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",G]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...W()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...W(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...W(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[T]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[T]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",G,t]}],"min-w":[{"min-w":[G,t,"min","max","fit"]}],"max-w":[{"max-w":[G,t,"none","full","min","max","fit","prose",{screen:[U]},U]}],h:[{h:[G,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[G,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[G,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[G,t,"auto","min","max","fit"]}],"font-size":[{text:["base",U,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",$]}],"font-family":[{font:[Z]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",G]}],"line-clamp":[{"line-clamp":["none",K,$]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",I,G]}],"list-image":[{"list-image":["none",G]}],"list-style-type":[{list:["none","disc","decimal",G]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...O(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",I,H]}],"underline-offset":[{"underline-offset":["auto",I,G]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...D(),_]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Y]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Q]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...O(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:O()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...O()]}],"outline-offset":[{"outline-offset":[I,G]}],"outline-w":[{outline:[I,H]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[I,H]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",U,J]}],"shadow-color":[{shadow:[Z]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...z(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":z()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",U,G]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",G]}],duration:[{duration:N()}],ease:[{ease:["linear","in","out","in-out",G]}],delay:[{delay:N()}],animate:[{animate:["none","spin","ping","pulse","bounce",G]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[V,G]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",G]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[I,H,$]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},ea=(e,{cacheSize:t,prefix:r,separator:n,experimentalParseClassName:o,extend:i={},override:a={}})=>{for(let i in el(e,"cacheSize",t),el(e,"prefix",r),el(e,"separator",n),el(e,"experimentalParseClassName",o),a)es(e[i],a[i]);for(let t in i)eu(e[t],i[t]);return e},el=(e,t,r)=>{void 0!==r&&(e[t]=r)},es=(e,t)=>{if(t)for(let r in t)el(e,r,t[r])},eu=(e,t)=>{if(t)for(let r in t){let n=t[r];void 0!==n&&(e[r]=(e[r]||[]).concat(n))}},ed=(e,...t)=>"function"==typeof e?L(ei,e,...t):L(()=>ea(ei(),e),...t),ec=L(ei);var ef={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},ep=e=>e||void 0,eg=(...e)=>ep(s(e).filter(Boolean).join(" ")),em=null,ev={},eb=!1,eh=(...e)=>t=>t.twMerge?((!em||eb)&&(eb=!1,em=a(ev)?ec:ed({...ev,extend:{theme:ev.theme,classGroups:ev.classGroups,conflictingClassGroupModifiers:ev.conflictingClassGroupModifiers,conflictingClassGroups:ev.conflictingClassGroups,...ev.extend}})),ep(em(eg(e)))):eg(e),ey=(e,t)=>{for(let r in t)e.hasOwnProperty(r)?e[r]=eg(e[r],t[r]):e[r]=t[r];return e},ew=(e,t)=>{let{extend:r=null,slots:n={},variants:o={},compoundVariants:s=[],compoundSlots:f=[],defaultVariants:p={}}=e,g={...ef,...t},m=null!=r&&r.base?eg(r.base,null==e?void 0:e.base):null==e?void 0:e.base,v=null!=r&&r.variants&&!a(r.variants)?d(o,r.variants):o,b=null!=r&&r.defaultVariants&&!a(r.defaultVariants)?{...r.defaultVariants,...p}:p;a(g.twMergeConfig)||l(g.twMergeConfig,ev)||(eb=!0,ev=g.twMergeConfig);let h=a(null==r?void 0:r.slots),y=a(n)?{}:{base:eg(null==e?void 0:e.base,h&&(null==r?void 0:r.base)),...n},w=h?y:ey({...null==r?void 0:r.slots},a(y)?{base:null==e?void 0:e.base}:y),x=a(null==r?void 0:r.compoundVariants)?s:u(null==r?void 0:r.compoundVariants,s),E=e=>{if(a(v)&&a(n)&&h)return eh(m,null==e?void 0:e.class,null==e?void 0:e.className)(g);if(x&&!Array.isArray(x))throw TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof x}`);if(f&&!Array.isArray(f))throw TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof f}`);let t=(e,t,r=[],n)=>{let o=r;if("string"==typeof t)o=o.concat(c(t).split(" ").map(t=>`${e}:${t}`));else if(Array.isArray(t))o=o.concat(t.reduce((t,r)=>t.concat(`${e}:${r}`),[]));else if("object"==typeof t&&"string"==typeof n){for(let r in t)if(t.hasOwnProperty(r)&&r===n){let i=t[r];if(i&&"string"==typeof i){let t=c(i);o[n]?o[n]=o[n].concat(t.split(" ").map(t=>`${e}:${t}`)):o[n]=t.split(" ").map(t=>`${e}:${t}`)}else Array.isArray(i)&&i.length>0&&(o[n]=i.reduce((t,r)=>t.concat(`${e}:${r}`),[]))}}return o},r=(r,n=v,o=null,l=null)=>{var s;let u=n[r];if(!u||a(u))return null;let d=null!=(s=null==l?void 0:l[r])?s:null==e?void 0:e[r];if(null===d)return null;let c=i(d),f=Array.isArray(g.responsiveVariants)&&g.responsiveVariants.length>0||!0===g.responsiveVariants,p=null==b?void 0:b[r],m=[];if("object"==typeof c&&f)for(let[e,r]of Object.entries(c)){let n=u[r];if("initial"===e){p=r;continue}Array.isArray(g.responsiveVariants)&&!g.responsiveVariants.includes(e)||(m=t(e,n,m,o))}let h=u[(null!=c&&"object"!=typeof c?c:i(p))||"false"];return"object"==typeof m&&"string"==typeof o&&m[o]?ey(m,h):m.length>0?(m.push(h),"base"===o?m.join(" "):m):h},o=(e,t)=>{if(!v||"object"!=typeof v)return null;let n=[];for(let o in v){let i=r(o,v,e,t),a="base"===e&&"string"==typeof i?i:i&&i[e];a&&(n[n.length]=a)}return n},l={};for(let t in e)void 0!==e[t]&&(l[t]=e[t]);let s=(t,r)=>{var n;let o="object"==typeof(null==e?void 0:e[t])?{[t]:null==(n=e[t])?void 0:n.initial}:{};return{...b,...l,...o,...r}},u=(e=[],t)=>{let r=[];for(let{class:n,className:o,...i}of e){let e=!0;for(let[r,n]of Object.entries(i)){let o=s(r,t)[r];if(Array.isArray(n)){if(!n.includes(o)){e=!1;break}}else{let t=e=>null==e||!1===e;if(t(n)&&t(o))continue;if(o!==n){e=!1;break}}}e&&(n&&r.push(n),o&&r.push(o))}return r},d=e=>{let t=u(x,e);if(!Array.isArray(t))return t;let r={};for(let e of t)if("string"==typeof e&&(r.base=eh(r.base,e)(g)),"object"==typeof e)for(let[t,n]of Object.entries(e))r[t]=eh(r[t],n)(g);return r},p=e=>{if(f.length<1)return null;let t={};for(let{slots:r=[],class:n,className:o,...i}of f){if(!a(i)){let t=!0;for(let r of Object.keys(i)){let n=s(r,e)[r];if(void 0===n||(Array.isArray(i[r])?!i[r].includes(n):i[r]!==n)){t=!1;break}}if(!t)continue}for(let e of r)t[e]=t[e]||[],t[e].push([n,o])}return t};if(!a(n)||!h){let e={};if("object"==typeof w&&!a(w))for(let t of Object.keys(w))e[t]=e=>{var r,n;return eh(w[t],o(t,e),(null!=(r=d(e))?r:[])[t],(null!=(n=p(e))?n:[])[t],null==e?void 0:e.class,null==e?void 0:e.className)(g)};return e}return eh(m,v?Object.keys(v).map(e=>r(e,v)):null,u(x),null==e?void 0:e.class,null==e?void 0:e.className)(g)};return E.variantKeys=(()=>{if(!(!v||"object"!=typeof v))return Object.keys(v)})(),E.extend=r,E.base=m,E.slots=w,E.variants=v,E.defaultVariants=b,E.compoundSlots=f,E.compoundVariants=x,E},ex=(e,t)=>{var r,n,i;return ew(e,{...t,twMerge:null==(r=null==t?void 0:t.twMerge)||r,twMergeConfig:{...null==t?void 0:t.twMergeConfig,theme:{...null==(n=null==t?void 0:t.twMergeConfig)?void 0:n.theme,...o.theme},classGroups:{...null==(i=null==t?void 0:t.twMergeConfig)?void 0:i.classGroups,...o.classGroups}}})}},93095:(e,t,r)=>{r.d(t,{w:()=>n});function n(e){return"string"==typeof e||Array.isArray(e)}},96539:(e,t,r)=>{r.d(t,{v:()=>a});var n=r(26423),o=r(24215);let i=function(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n};function a(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let a=e[r];for(let e in a){let r=t[e],l=a[e];"function"==typeof r&&"function"==typeof l&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=(0,n.c)(r,l):("className"===e||"UNSAFE_className"===e)&&"string"==typeof r&&"string"==typeof l?t[e]=i(r,l):"id"===e&&r&&l?t.id=(0,o.Tw)(r,l):t[e]=void 0!==l?l:r}}return t}},97133:(e,t,r)=>{r.d(t,{X:()=>n});function n(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}},98307:(e,t,r)=>{r.d(t,{t:()=>n});let n=(0,r(9585).createContext)(null)},98466:(e,t,r)=>{r.d(t,{n:()=>n});let n="data-"+(0,r(20460).I)("framerAppearId")},99275:(e,t,r)=>{r.d(t,{Cl:()=>P,K7:()=>M,ME:()=>k,lb:()=>C,pP:()=>T});var n=r(2215),o=r(60699),i=r(12877),a=r(20965),l=r(9585),s=r(37285);let u=null,d=new Set,c=new Map,f=!1,p=!1,g={Tab:!0,Escape:!0};function m(e,t){for(let r of d)r(e,t)}function v(e){f=!0,e.metaKey||!(0,o.cX)()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(u="keyboard",m("keyboard",e))}function b(e){u="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(f=!0,m("pointer",e))}function h(e){(0,i.Y)(e)&&(f=!0,u="virtual")}function y(e){e.target!==window&&e.target!==document&&!n.lR&&e.isTrusted&&(f||p||(u="virtual",m("virtual",e)),f=!1,p=!1)}function w(){n.lR||(f=!1,p=!0)}function x(e){if("undefined"==typeof window||"undefined"==typeof document||c.get((0,a.mD)(e)))return;let t=(0,a.mD)(e),r=(0,a.TW)(e),n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){f=!0,n.apply(this,arguments)},r.addEventListener("keydown",v,!0),r.addEventListener("keyup",v,!0),r.addEventListener("click",h,!0),t.addEventListener("focus",y,!0),t.addEventListener("blur",w,!1),"undefined"!=typeof PointerEvent&&(r.addEventListener("pointerdown",b,!0),r.addEventListener("pointermove",b,!0),r.addEventListener("pointerup",b,!0)),t.addEventListener("beforeunload",()=>{E(e)},{once:!0}),c.set(t,{focus:n})}let E=(e,t)=>{let r=(0,a.mD)(e),n=(0,a.TW)(e);t&&n.removeEventListener("DOMContentLoaded",t),c.has(r)&&(r.HTMLElement.prototype.focus=c.get(r).focus,n.removeEventListener("keydown",v,!0),n.removeEventListener("keyup",v,!0),n.removeEventListener("click",h,!0),r.removeEventListener("focus",y,!0),r.removeEventListener("blur",w,!1),"undefined"!=typeof PointerEvent&&(n.removeEventListener("pointerdown",b,!0),n.removeEventListener("pointermove",b,!0),n.removeEventListener("pointerup",b,!0)),c.delete(r))};function T(){return"pointer"!==u}function k(){return u}function P(e){u=e,m(e,null)}function C(){x();let[e,t]=(0,l.useState)(u);return(0,l.useEffect)(()=>{let e=()=>{t(u)};return d.add(e),()=>{d.delete(e)}},[]),(0,s.wR)()?null:e}"undefined"!=typeof document&&function(e){let t,r=(0,a.TW)(void 0);"loading"!==r.readyState?x(void 0):(t=()=>{x(e)},r.addEventListener("DOMContentLoaded",t)),()=>E(e,t)}();let S=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function M(e,t,r){x(),(0,l.useEffect)(()=>{let t=(t,n)=>{(function(e,t,r){let n=(0,a.TW)(null==r?void 0:r.target),o="undefined"!=typeof window?(0,a.mD)(null==r?void 0:r.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?(0,a.mD)(null==r?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,l="undefined"!=typeof window?(0,a.mD)(null==r?void 0:r.target).HTMLElement:HTMLElement,s="undefined"!=typeof window?(0,a.mD)(null==r?void 0:r.target).KeyboardEvent:KeyboardEvent;return!((e=e||n.activeElement instanceof o&&!S.has(n.activeElement.type)||n.activeElement instanceof i||n.activeElement instanceof l&&n.activeElement.isContentEditable)&&"keyboard"===t&&r instanceof s&&!g[r.key])})(!!(null==r?void 0:r.isTextInput),t,n)&&e(T())};return d.add(t),()=>{d.delete(t)}},t)}}}]);