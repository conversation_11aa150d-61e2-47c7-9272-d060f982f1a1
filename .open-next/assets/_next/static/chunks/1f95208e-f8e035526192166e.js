(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2883],{1326:(e,t,r)=>{var o=r(49393),n="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof window&&window||void 0!==n&&n||"undefined"!=typeof self&&self||(function(){return this}).call(null)||Function("return this")(),s=r(44061);o.object.extend(proto,s),o.exportSymbol("TronWebProto.Account",null,n),o.exportSymbol("TronWebProto.Account.AccountResource",null,n),o.exportSymbol("TronWebProto.Account.Frozen",null,n),o.exportSymbol("TronWebProto.AccountId",null,n),o.exportSymbol("TronWebProto.AccountType",null,n),o.exportSymbol("TronWebProto.DelegatedResource",null,n),o.exportSymbol("TronWebProto.Exchange",null,n),o.exportSymbol("TronWebProto.Key",null,n),o.exportSymbol("TronWebProto.MarketOrderDetail",null,n),o.exportSymbol("TronWebProto.Permission",null,n),o.exportSymbol("TronWebProto.Permission.PermissionType",null,n),o.exportSymbol("TronWebProto.Proposal",null,n),o.exportSymbol("TronWebProto.Proposal.State",null,n),o.exportSymbol("TronWebProto.Transaction",null,n),o.exportSymbol("TronWebProto.Transaction.Contract",null,n),o.exportSymbol("TronWebProto.Transaction.Contract.ContractType",null,n),o.exportSymbol("TronWebProto.Transaction.Result",null,n),o.exportSymbol("TronWebProto.Transaction.Result.code",null,n),o.exportSymbol("TronWebProto.Transaction.Result.contractResult",null,n),o.exportSymbol("TronWebProto.Transaction.raw",null,n),o.exportSymbol("TronWebProto.Vote",null,n),o.exportSymbol("TronWebProto.Votes",null,n),o.exportSymbol("TronWebProto.Witness",null,n),o.exportSymbol("TronWebProto.authority",null,n),TronWebProto.AccountId=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.AccountId,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.AccountId.displayName="TronWebProto.AccountId"),TronWebProto.Vote=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Vote,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Vote.displayName="TronWebProto.Vote"),TronWebProto.Proposal=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Proposal.repeatedFields_,null)},o.inherits(TronWebProto.Proposal,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Proposal.displayName="TronWebProto.Proposal"),TronWebProto.Exchange=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Exchange,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Exchange.displayName="TronWebProto.Exchange"),TronWebProto.Account=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Account.repeatedFields_,null)},o.inherits(TronWebProto.Account,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Account.displayName="TronWebProto.Account"),TronWebProto.Account.Frozen=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Account.Frozen,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Account.Frozen.displayName="TronWebProto.Account.Frozen"),TronWebProto.Account.AccountResource=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Account.AccountResource,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Account.AccountResource.displayName="TronWebProto.Account.AccountResource"),TronWebProto.Key=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Key,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Key.displayName="TronWebProto.Key"),TronWebProto.DelegatedResource=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.DelegatedResource,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.DelegatedResource.displayName="TronWebProto.DelegatedResource"),TronWebProto.authority=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.authority,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.authority.displayName="TronWebProto.authority"),TronWebProto.Permission=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Permission.repeatedFields_,null)},o.inherits(TronWebProto.Permission,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Permission.displayName="TronWebProto.Permission"),TronWebProto.Witness=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Witness,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Witness.displayName="TronWebProto.Witness"),TronWebProto.Votes=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Votes.repeatedFields_,null)},o.inherits(TronWebProto.Votes,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Votes.displayName="TronWebProto.Votes"),TronWebProto.MarketOrderDetail=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.MarketOrderDetail,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.MarketOrderDetail.displayName="TronWebProto.MarketOrderDetail"),TronWebProto.Transaction=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Transaction.repeatedFields_,null)},o.inherits(TronWebProto.Transaction,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Transaction.displayName="TronWebProto.Transaction"),TronWebProto.Transaction.Contract=function(e){o.Message.initialize(this,e,0,-1,null,null)},o.inherits(TronWebProto.Transaction.Contract,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Transaction.Contract.displayName="TronWebProto.Transaction.Contract"),TronWebProto.Transaction.Result=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Transaction.Result.repeatedFields_,null)},o.inherits(TronWebProto.Transaction.Result,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Transaction.Result.displayName="TronWebProto.Transaction.Result"),TronWebProto.Transaction.raw=function(e){o.Message.initialize(this,e,0,-1,TronWebProto.Transaction.raw.repeatedFields_,null)},o.inherits(TronWebProto.Transaction.raw,o.Message),o.DEBUG&&!COMPILED&&(TronWebProto.Transaction.raw.displayName="TronWebProto.Transaction.raw"),o.Message.GENERATE_TO_OBJECT&&(TronWebProto.AccountId.prototype.toObject=function(e){return TronWebProto.AccountId.toObject(e,this)},TronWebProto.AccountId.toObject=function(e,t){var r={name:t.getName_asB64(),address:t.getAddress_asB64()};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.AccountId.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.AccountId;return TronWebProto.AccountId.deserializeBinaryFromReader(r,t)},TronWebProto.AccountId.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setName(r);break;case 2:var r=t.readBytes();e.setAddress(r);break;default:t.skipField()}return e},TronWebProto.AccountId.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.AccountId.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.AccountId.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getName_asU8()).length>0&&t.writeBytes(1,r),(r=e.getAddress_asU8()).length>0&&t.writeBytes(2,r)},TronWebProto.AccountId.prototype.getName=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.AccountId.prototype.getName_asB64=function(){return o.Message.bytesAsB64(this.getName())},TronWebProto.AccountId.prototype.getName_asU8=function(){return o.Message.bytesAsU8(this.getName())},TronWebProto.AccountId.prototype.setName=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.AccountId.prototype.getAddress=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.AccountId.prototype.getAddress_asB64=function(){return o.Message.bytesAsB64(this.getAddress())},TronWebProto.AccountId.prototype.getAddress_asU8=function(){return o.Message.bytesAsU8(this.getAddress())},TronWebProto.AccountId.prototype.setAddress=function(e){return o.Message.setProto3BytesField(this,2,e)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Vote.prototype.toObject=function(e){return TronWebProto.Vote.toObject(e,this)},TronWebProto.Vote.toObject=function(e,t){var r={voteAddress:t.getVoteAddress_asB64(),voteCount:o.Message.getFieldWithDefault(t,2,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Vote.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Vote;return TronWebProto.Vote.deserializeBinaryFromReader(r,t)},TronWebProto.Vote.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setVoteAddress(r);break;case 2:var r=t.readInt64();e.setVoteCount(r);break;default:t.skipField()}return e},TronWebProto.Vote.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Vote.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Vote.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getVoteAddress_asU8()).length>0&&t.writeBytes(1,r),0!==(r=e.getVoteCount())&&t.writeInt64(2,r)},TronWebProto.Vote.prototype.getVoteAddress=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Vote.prototype.getVoteAddress_asB64=function(){return o.Message.bytesAsB64(this.getVoteAddress())},TronWebProto.Vote.prototype.getVoteAddress_asU8=function(){return o.Message.bytesAsU8(this.getVoteAddress())},TronWebProto.Vote.prototype.setVoteAddress=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Vote.prototype.getVoteCount=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Vote.prototype.setVoteCount=function(e){return o.Message.setProto3IntField(this,2,e)},TronWebProto.Proposal.repeatedFields_=[6],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Proposal.prototype.toObject=function(e){return TronWebProto.Proposal.toObject(e,this)},TronWebProto.Proposal.toObject=function(e,t){var r,n={proposalId:o.Message.getFieldWithDefault(t,1,0),proposerAddress:t.getProposerAddress_asB64(),parametersMap:(r=t.getParametersMap())?r.toObject(e,void 0):[],expirationTime:o.Message.getFieldWithDefault(t,4,0),createTime:o.Message.getFieldWithDefault(t,5,0),approvalsList:t.getApprovalsList_asB64(),state:o.Message.getFieldWithDefault(t,7,0)};return e&&(n.$jspbMessageInstance=t),n}),TronWebProto.Proposal.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Proposal;return TronWebProto.Proposal.deserializeBinaryFromReader(r,t)},TronWebProto.Proposal.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readInt64();e.setProposalId(r);break;case 2:var r=t.readBytes();e.setProposerAddress(r);break;case 3:var r=e.getParametersMap();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readInt64,o.BinaryReader.prototype.readInt64,null,0,0)});break;case 4:var r=t.readInt64();e.setExpirationTime(r);break;case 5:var r=t.readInt64();e.setCreateTime(r);break;case 6:var r=t.readBytes();e.addApprovals(r);break;case 7:var r=t.readEnum();e.setState(r);break;default:t.skipField()}return e},TronWebProto.Proposal.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Proposal.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Proposal.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getProposalId())&&t.writeInt64(1,r),(r=e.getProposerAddress_asU8()).length>0&&t.writeBytes(2,r),(r=e.getParametersMap(!0))&&r.getLength()>0&&r.serializeBinary(3,t,o.BinaryWriter.prototype.writeInt64,o.BinaryWriter.prototype.writeInt64),0!==(r=e.getExpirationTime())&&t.writeInt64(4,r),0!==(r=e.getCreateTime())&&t.writeInt64(5,r),(r=e.getApprovalsList_asU8()).length>0&&t.writeRepeatedBytes(6,r),0!==(r=e.getState())&&t.writeEnum(7,r)},TronWebProto.Proposal.State={PENDING:0,DISAPPROVED:1,APPROVED:2,CANCELED:3},TronWebProto.Proposal.prototype.getProposalId=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Proposal.prototype.setProposalId=function(e){return o.Message.setProto3IntField(this,1,e)},TronWebProto.Proposal.prototype.getProposerAddress=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.Proposal.prototype.getProposerAddress_asB64=function(){return o.Message.bytesAsB64(this.getProposerAddress())},TronWebProto.Proposal.prototype.getProposerAddress_asU8=function(){return o.Message.bytesAsU8(this.getProposerAddress())},TronWebProto.Proposal.prototype.setProposerAddress=function(e){return o.Message.setProto3BytesField(this,2,e)},TronWebProto.Proposal.prototype.getParametersMap=function(e){return o.Message.getMapField(this,3,e,null)},TronWebProto.Proposal.prototype.clearParametersMap=function(){return this.getParametersMap().clear(),this},TronWebProto.Proposal.prototype.getExpirationTime=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.Proposal.prototype.setExpirationTime=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.Proposal.prototype.getCreateTime=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.Proposal.prototype.setCreateTime=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.Proposal.prototype.getApprovalsList=function(){return o.Message.getRepeatedField(this,6)},TronWebProto.Proposal.prototype.getApprovalsList_asB64=function(){return o.Message.bytesListAsB64(this.getApprovalsList())},TronWebProto.Proposal.prototype.getApprovalsList_asU8=function(){return o.Message.bytesListAsU8(this.getApprovalsList())},TronWebProto.Proposal.prototype.setApprovalsList=function(e){return o.Message.setField(this,6,e||[])},TronWebProto.Proposal.prototype.addApprovals=function(e,t){return o.Message.addToRepeatedField(this,6,e,t)},TronWebProto.Proposal.prototype.clearApprovalsList=function(){return this.setApprovalsList([])},TronWebProto.Proposal.prototype.getState=function(){return o.Message.getFieldWithDefault(this,7,0)},TronWebProto.Proposal.prototype.setState=function(e){return o.Message.setProto3EnumField(this,7,e)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Exchange.prototype.toObject=function(e){return TronWebProto.Exchange.toObject(e,this)},TronWebProto.Exchange.toObject=function(e,t){var r={exchangeId:o.Message.getFieldWithDefault(t,1,0),creatorAddress:t.getCreatorAddress_asB64(),createTime:o.Message.getFieldWithDefault(t,3,0),firstTokenId:t.getFirstTokenId_asB64(),firstTokenBalance:o.Message.getFieldWithDefault(t,7,0),secondTokenId:t.getSecondTokenId_asB64(),secondTokenBalance:o.Message.getFieldWithDefault(t,9,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Exchange.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Exchange;return TronWebProto.Exchange.deserializeBinaryFromReader(r,t)},TronWebProto.Exchange.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readInt64();e.setExchangeId(r);break;case 2:var r=t.readBytes();e.setCreatorAddress(r);break;case 3:var r=t.readInt64();e.setCreateTime(r);break;case 6:var r=t.readBytes();e.setFirstTokenId(r);break;case 7:var r=t.readInt64();e.setFirstTokenBalance(r);break;case 8:var r=t.readBytes();e.setSecondTokenId(r);break;case 9:var r=t.readInt64();e.setSecondTokenBalance(r);break;default:t.skipField()}return e},TronWebProto.Exchange.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Exchange.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Exchange.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getExchangeId())&&t.writeInt64(1,r),(r=e.getCreatorAddress_asU8()).length>0&&t.writeBytes(2,r),0!==(r=e.getCreateTime())&&t.writeInt64(3,r),(r=e.getFirstTokenId_asU8()).length>0&&t.writeBytes(6,r),0!==(r=e.getFirstTokenBalance())&&t.writeInt64(7,r),(r=e.getSecondTokenId_asU8()).length>0&&t.writeBytes(8,r),0!==(r=e.getSecondTokenBalance())&&t.writeInt64(9,r)},TronWebProto.Exchange.prototype.getExchangeId=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Exchange.prototype.setExchangeId=function(e){return o.Message.setProto3IntField(this,1,e)},TronWebProto.Exchange.prototype.getCreatorAddress=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.Exchange.prototype.getCreatorAddress_asB64=function(){return o.Message.bytesAsB64(this.getCreatorAddress())},TronWebProto.Exchange.prototype.getCreatorAddress_asU8=function(){return o.Message.bytesAsU8(this.getCreatorAddress())},TronWebProto.Exchange.prototype.setCreatorAddress=function(e){return o.Message.setProto3BytesField(this,2,e)},TronWebProto.Exchange.prototype.getCreateTime=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.Exchange.prototype.setCreateTime=function(e){return o.Message.setProto3IntField(this,3,e)},TronWebProto.Exchange.prototype.getFirstTokenId=function(){return o.Message.getFieldWithDefault(this,6,"")},TronWebProto.Exchange.prototype.getFirstTokenId_asB64=function(){return o.Message.bytesAsB64(this.getFirstTokenId())},TronWebProto.Exchange.prototype.getFirstTokenId_asU8=function(){return o.Message.bytesAsU8(this.getFirstTokenId())},TronWebProto.Exchange.prototype.setFirstTokenId=function(e){return o.Message.setProto3BytesField(this,6,e)},TronWebProto.Exchange.prototype.getFirstTokenBalance=function(){return o.Message.getFieldWithDefault(this,7,0)},TronWebProto.Exchange.prototype.setFirstTokenBalance=function(e){return o.Message.setProto3IntField(this,7,e)},TronWebProto.Exchange.prototype.getSecondTokenId=function(){return o.Message.getFieldWithDefault(this,8,"")},TronWebProto.Exchange.prototype.getSecondTokenId_asB64=function(){return o.Message.bytesAsB64(this.getSecondTokenId())},TronWebProto.Exchange.prototype.getSecondTokenId_asU8=function(){return o.Message.bytesAsU8(this.getSecondTokenId())},TronWebProto.Exchange.prototype.setSecondTokenId=function(e){return o.Message.setProto3BytesField(this,8,e)},TronWebProto.Exchange.prototype.getSecondTokenBalance=function(){return o.Message.getFieldWithDefault(this,9,0)},TronWebProto.Exchange.prototype.setSecondTokenBalance=function(e){return o.Message.setProto3IntField(this,9,e)},TronWebProto.Account.repeatedFields_=[5,7,16,33],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Account.prototype.toObject=function(e){return TronWebProto.Account.toObject(e,this)},TronWebProto.Account.toObject=function(e,t){var r,n={accountName:t.getAccountName_asB64(),type:o.Message.getFieldWithDefault(t,2,0),address:t.getAddress_asB64(),balance:o.Message.getFieldWithDefault(t,4,0),votesList:o.Message.toObjectList(t.getVotesList(),TronWebProto.Vote.toObject,e),assetMap:(r=t.getAssetMap())?r.toObject(e,void 0):[],assetv2Map:(r=t.getAssetv2Map())?r.toObject(e,void 0):[],frozenList:o.Message.toObjectList(t.getFrozenList(),TronWebProto.Account.Frozen.toObject,e),netUsage:o.Message.getFieldWithDefault(t,8,0),acquiredDelegatedFrozenBalanceForBandwidth:o.Message.getFieldWithDefault(t,41,0),delegatedFrozenBalanceForBandwidth:o.Message.getFieldWithDefault(t,42,0),oldTronPower:o.Message.getFieldWithDefault(t,46,0),tronPower:(r=t.getTronPower())&&TronWebProto.Account.Frozen.toObject(e,r),assetOptimized:o.Message.getBooleanFieldWithDefault(t,60,!1),createTime:o.Message.getFieldWithDefault(t,9,0),latestOprationTime:o.Message.getFieldWithDefault(t,10,0),allowance:o.Message.getFieldWithDefault(t,11,0),latestWithdrawTime:o.Message.getFieldWithDefault(t,12,0),code:t.getCode_asB64(),isWitness:o.Message.getBooleanFieldWithDefault(t,14,!1),isCommittee:o.Message.getBooleanFieldWithDefault(t,15,!1),frozenSupplyList:o.Message.toObjectList(t.getFrozenSupplyList(),TronWebProto.Account.Frozen.toObject,e),assetIssuedName:t.getAssetIssuedName_asB64(),assetIssuedId:t.getAssetIssuedId_asB64(),latestAssetOperationTimeMap:(r=t.getLatestAssetOperationTimeMap())?r.toObject(e,void 0):[],latestAssetOperationTimev2Map:(r=t.getLatestAssetOperationTimev2Map())?r.toObject(e,void 0):[],freeNetUsage:o.Message.getFieldWithDefault(t,19,0),freeAssetNetUsageMap:(r=t.getFreeAssetNetUsageMap())?r.toObject(e,void 0):[],freeAssetNetUsagev2Map:(r=t.getFreeAssetNetUsagev2Map())?r.toObject(e,void 0):[],latestConsumeTime:o.Message.getFieldWithDefault(t,21,0),latestConsumeFreeTime:o.Message.getFieldWithDefault(t,22,0),accountId:t.getAccountId_asB64(),accountResource:(r=t.getAccountResource())&&TronWebProto.Account.AccountResource.toObject(e,r),codehash:t.getCodehash_asB64(),ownerPermission:(r=t.getOwnerPermission())&&TronWebProto.Permission.toObject(e,r),witnessPermission:(r=t.getWitnessPermission())&&TronWebProto.Permission.toObject(e,r),activePermissionList:o.Message.toObjectList(t.getActivePermissionList(),TronWebProto.Permission.toObject,e)};return e&&(n.$jspbMessageInstance=t),n}),TronWebProto.Account.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Account;return TronWebProto.Account.deserializeBinaryFromReader(r,t)},TronWebProto.Account.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setAccountName(r);break;case 2:var r=t.readEnum();e.setType(r);break;case 3:var r=t.readBytes();e.setAddress(r);break;case 4:var r=t.readInt64();e.setBalance(r);break;case 5:var r=new TronWebProto.Vote;t.readMessage(r,TronWebProto.Vote.deserializeBinaryFromReader),e.addVotes(r);break;case 6:var r=e.getAssetMap();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 56:var r=e.getAssetv2Map();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 7:var r=new TronWebProto.Account.Frozen;t.readMessage(r,TronWebProto.Account.Frozen.deserializeBinaryFromReader),e.addFrozen(r);break;case 8:var r=t.readInt64();e.setNetUsage(r);break;case 41:var r=t.readInt64();e.setAcquiredDelegatedFrozenBalanceForBandwidth(r);break;case 42:var r=t.readInt64();e.setDelegatedFrozenBalanceForBandwidth(r);break;case 46:var r=t.readInt64();e.setOldTronPower(r);break;case 47:var r=new TronWebProto.Account.Frozen;t.readMessage(r,TronWebProto.Account.Frozen.deserializeBinaryFromReader),e.setTronPower(r);break;case 60:var r=t.readBool();e.setAssetOptimized(r);break;case 9:var r=t.readInt64();e.setCreateTime(r);break;case 10:var r=t.readInt64();e.setLatestOprationTime(r);break;case 11:var r=t.readInt64();e.setAllowance(r);break;case 12:var r=t.readInt64();e.setLatestWithdrawTime(r);break;case 13:var r=t.readBytes();e.setCode(r);break;case 14:var r=t.readBool();e.setIsWitness(r);break;case 15:var r=t.readBool();e.setIsCommittee(r);break;case 16:var r=new TronWebProto.Account.Frozen;t.readMessage(r,TronWebProto.Account.Frozen.deserializeBinaryFromReader),e.addFrozenSupply(r);break;case 17:var r=t.readBytes();e.setAssetIssuedName(r);break;case 57:var r=t.readBytes();e.setAssetIssuedId(r);break;case 18:var r=e.getLatestAssetOperationTimeMap();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 58:var r=e.getLatestAssetOperationTimev2Map();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 19:var r=t.readInt64();e.setFreeNetUsage(r);break;case 20:var r=e.getFreeAssetNetUsageMap();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 59:var r=e.getFreeAssetNetUsagev2Map();t.readMessage(r,function(e,t){o.Map.deserializeBinary(e,t,o.BinaryReader.prototype.readString,o.BinaryReader.prototype.readInt64,null,"",0)});break;case 21:var r=t.readInt64();e.setLatestConsumeTime(r);break;case 22:var r=t.readInt64();e.setLatestConsumeFreeTime(r);break;case 23:var r=t.readBytes();e.setAccountId(r);break;case 26:var r=new TronWebProto.Account.AccountResource;t.readMessage(r,TronWebProto.Account.AccountResource.deserializeBinaryFromReader),e.setAccountResource(r);break;case 30:var r=t.readBytes();e.setCodehash(r);break;case 31:var r=new TronWebProto.Permission;t.readMessage(r,TronWebProto.Permission.deserializeBinaryFromReader),e.setOwnerPermission(r);break;case 32:var r=new TronWebProto.Permission;t.readMessage(r,TronWebProto.Permission.deserializeBinaryFromReader),e.setWitnessPermission(r);break;case 33:var r=new TronWebProto.Permission;t.readMessage(r,TronWebProto.Permission.deserializeBinaryFromReader),e.addActivePermission(r);break;default:t.skipField()}return e},TronWebProto.Account.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Account.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Account.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getAccountName_asU8()).length>0&&t.writeBytes(1,r),0!==(r=e.getType())&&t.writeEnum(2,r),(r=e.getAddress_asU8()).length>0&&t.writeBytes(3,r),0!==(r=e.getBalance())&&t.writeInt64(4,r),(r=e.getVotesList()).length>0&&t.writeRepeatedMessage(5,r,TronWebProto.Vote.serializeBinaryToWriter),(r=e.getAssetMap(!0))&&r.getLength()>0&&r.serializeBinary(6,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),(r=e.getAssetv2Map(!0))&&r.getLength()>0&&r.serializeBinary(56,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),(r=e.getFrozenList()).length>0&&t.writeRepeatedMessage(7,r,TronWebProto.Account.Frozen.serializeBinaryToWriter),0!==(r=e.getNetUsage())&&t.writeInt64(8,r),0!==(r=e.getAcquiredDelegatedFrozenBalanceForBandwidth())&&t.writeInt64(41,r),0!==(r=e.getDelegatedFrozenBalanceForBandwidth())&&t.writeInt64(42,r),0!==(r=e.getOldTronPower())&&t.writeInt64(46,r),null!=(r=e.getTronPower())&&t.writeMessage(47,r,TronWebProto.Account.Frozen.serializeBinaryToWriter),(r=e.getAssetOptimized())&&t.writeBool(60,r),0!==(r=e.getCreateTime())&&t.writeInt64(9,r),0!==(r=e.getLatestOprationTime())&&t.writeInt64(10,r),0!==(r=e.getAllowance())&&t.writeInt64(11,r),0!==(r=e.getLatestWithdrawTime())&&t.writeInt64(12,r),(r=e.getCode_asU8()).length>0&&t.writeBytes(13,r),(r=e.getIsWitness())&&t.writeBool(14,r),(r=e.getIsCommittee())&&t.writeBool(15,r),(r=e.getFrozenSupplyList()).length>0&&t.writeRepeatedMessage(16,r,TronWebProto.Account.Frozen.serializeBinaryToWriter),(r=e.getAssetIssuedName_asU8()).length>0&&t.writeBytes(17,r),(r=e.getAssetIssuedId_asU8()).length>0&&t.writeBytes(57,r),(r=e.getLatestAssetOperationTimeMap(!0))&&r.getLength()>0&&r.serializeBinary(18,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),(r=e.getLatestAssetOperationTimev2Map(!0))&&r.getLength()>0&&r.serializeBinary(58,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),0!==(r=e.getFreeNetUsage())&&t.writeInt64(19,r),(r=e.getFreeAssetNetUsageMap(!0))&&r.getLength()>0&&r.serializeBinary(20,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),(r=e.getFreeAssetNetUsagev2Map(!0))&&r.getLength()>0&&r.serializeBinary(59,t,o.BinaryWriter.prototype.writeString,o.BinaryWriter.prototype.writeInt64),0!==(r=e.getLatestConsumeTime())&&t.writeInt64(21,r),0!==(r=e.getLatestConsumeFreeTime())&&t.writeInt64(22,r),(r=e.getAccountId_asU8()).length>0&&t.writeBytes(23,r),null!=(r=e.getAccountResource())&&t.writeMessage(26,r,TronWebProto.Account.AccountResource.serializeBinaryToWriter),(r=e.getCodehash_asU8()).length>0&&t.writeBytes(30,r),null!=(r=e.getOwnerPermission())&&t.writeMessage(31,r,TronWebProto.Permission.serializeBinaryToWriter),null!=(r=e.getWitnessPermission())&&t.writeMessage(32,r,TronWebProto.Permission.serializeBinaryToWriter),(r=e.getActivePermissionList()).length>0&&t.writeRepeatedMessage(33,r,TronWebProto.Permission.serializeBinaryToWriter)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Account.Frozen.prototype.toObject=function(e){return TronWebProto.Account.Frozen.toObject(e,this)},TronWebProto.Account.Frozen.toObject=function(e,t){var r={frozenBalance:o.Message.getFieldWithDefault(t,1,0),expireTime:o.Message.getFieldWithDefault(t,2,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Account.Frozen.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Account.Frozen;return TronWebProto.Account.Frozen.deserializeBinaryFromReader(r,t)},TronWebProto.Account.Frozen.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readInt64();e.setFrozenBalance(r);break;case 2:var r=t.readInt64();e.setExpireTime(r);break;default:t.skipField()}return e},TronWebProto.Account.Frozen.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Account.Frozen.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Account.Frozen.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getFrozenBalance())&&t.writeInt64(1,r),0!==(r=e.getExpireTime())&&t.writeInt64(2,r)},TronWebProto.Account.Frozen.prototype.getFrozenBalance=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Account.Frozen.prototype.setFrozenBalance=function(e){return o.Message.setProto3IntField(this,1,e)},TronWebProto.Account.Frozen.prototype.getExpireTime=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Account.Frozen.prototype.setExpireTime=function(e){return o.Message.setProto3IntField(this,2,e)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Account.AccountResource.prototype.toObject=function(e){return TronWebProto.Account.AccountResource.toObject(e,this)},TronWebProto.Account.AccountResource.toObject=function(e,t){var r,n={energyUsage:o.Message.getFieldWithDefault(t,1,0),frozenBalanceForEnergy:(r=t.getFrozenBalanceForEnergy())&&TronWebProto.Account.Frozen.toObject(e,r),latestConsumeTimeForEnergy:o.Message.getFieldWithDefault(t,3,0),acquiredDelegatedFrozenBalanceForEnergy:o.Message.getFieldWithDefault(t,4,0),delegatedFrozenBalanceForEnergy:o.Message.getFieldWithDefault(t,5,0),storageLimit:o.Message.getFieldWithDefault(t,6,0),storageUsage:o.Message.getFieldWithDefault(t,7,0),latestExchangeStorageTime:o.Message.getFieldWithDefault(t,8,0)};return e&&(n.$jspbMessageInstance=t),n}),TronWebProto.Account.AccountResource.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Account.AccountResource;return TronWebProto.Account.AccountResource.deserializeBinaryFromReader(r,t)},TronWebProto.Account.AccountResource.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readInt64();e.setEnergyUsage(r);break;case 2:var r=new TronWebProto.Account.Frozen;t.readMessage(r,TronWebProto.Account.Frozen.deserializeBinaryFromReader),e.setFrozenBalanceForEnergy(r);break;case 3:var r=t.readInt64();e.setLatestConsumeTimeForEnergy(r);break;case 4:var r=t.readInt64();e.setAcquiredDelegatedFrozenBalanceForEnergy(r);break;case 5:var r=t.readInt64();e.setDelegatedFrozenBalanceForEnergy(r);break;case 6:var r=t.readInt64();e.setStorageLimit(r);break;case 7:var r=t.readInt64();e.setStorageUsage(r);break;case 8:var r=t.readInt64();e.setLatestExchangeStorageTime(r);break;default:t.skipField()}return e},TronWebProto.Account.AccountResource.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Account.AccountResource.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Account.AccountResource.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getEnergyUsage())&&t.writeInt64(1,r),null!=(r=e.getFrozenBalanceForEnergy())&&t.writeMessage(2,r,TronWebProto.Account.Frozen.serializeBinaryToWriter),0!==(r=e.getLatestConsumeTimeForEnergy())&&t.writeInt64(3,r),0!==(r=e.getAcquiredDelegatedFrozenBalanceForEnergy())&&t.writeInt64(4,r),0!==(r=e.getDelegatedFrozenBalanceForEnergy())&&t.writeInt64(5,r),0!==(r=e.getStorageLimit())&&t.writeInt64(6,r),0!==(r=e.getStorageUsage())&&t.writeInt64(7,r),0!==(r=e.getLatestExchangeStorageTime())&&t.writeInt64(8,r)},TronWebProto.Account.AccountResource.prototype.getEnergyUsage=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Account.AccountResource.prototype.setEnergyUsage=function(e){return o.Message.setProto3IntField(this,1,e)},TronWebProto.Account.AccountResource.prototype.getFrozenBalanceForEnergy=function(){return o.Message.getWrapperField(this,TronWebProto.Account.Frozen,2)},TronWebProto.Account.AccountResource.prototype.setFrozenBalanceForEnergy=function(e){return o.Message.setWrapperField(this,2,e)},TronWebProto.Account.AccountResource.prototype.clearFrozenBalanceForEnergy=function(){return this.setFrozenBalanceForEnergy(void 0)},TronWebProto.Account.AccountResource.prototype.hasFrozenBalanceForEnergy=function(){return null!=o.Message.getField(this,2)},TronWebProto.Account.AccountResource.prototype.getLatestConsumeTimeForEnergy=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.Account.AccountResource.prototype.setLatestConsumeTimeForEnergy=function(e){return o.Message.setProto3IntField(this,3,e)},TronWebProto.Account.AccountResource.prototype.getAcquiredDelegatedFrozenBalanceForEnergy=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.Account.AccountResource.prototype.setAcquiredDelegatedFrozenBalanceForEnergy=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.Account.AccountResource.prototype.getDelegatedFrozenBalanceForEnergy=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.Account.AccountResource.prototype.setDelegatedFrozenBalanceForEnergy=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.Account.AccountResource.prototype.getStorageLimit=function(){return o.Message.getFieldWithDefault(this,6,0)},TronWebProto.Account.AccountResource.prototype.setStorageLimit=function(e){return o.Message.setProto3IntField(this,6,e)},TronWebProto.Account.AccountResource.prototype.getStorageUsage=function(){return o.Message.getFieldWithDefault(this,7,0)},TronWebProto.Account.AccountResource.prototype.setStorageUsage=function(e){return o.Message.setProto3IntField(this,7,e)},TronWebProto.Account.AccountResource.prototype.getLatestExchangeStorageTime=function(){return o.Message.getFieldWithDefault(this,8,0)},TronWebProto.Account.AccountResource.prototype.setLatestExchangeStorageTime=function(e){return o.Message.setProto3IntField(this,8,e)},TronWebProto.Account.prototype.getAccountName=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Account.prototype.getAccountName_asB64=function(){return o.Message.bytesAsB64(this.getAccountName())},TronWebProto.Account.prototype.getAccountName_asU8=function(){return o.Message.bytesAsU8(this.getAccountName())},TronWebProto.Account.prototype.setAccountName=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Account.prototype.getType=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Account.prototype.setType=function(e){return o.Message.setProto3EnumField(this,2,e)},TronWebProto.Account.prototype.getAddress=function(){return o.Message.getFieldWithDefault(this,3,"")},TronWebProto.Account.prototype.getAddress_asB64=function(){return o.Message.bytesAsB64(this.getAddress())},TronWebProto.Account.prototype.getAddress_asU8=function(){return o.Message.bytesAsU8(this.getAddress())},TronWebProto.Account.prototype.setAddress=function(e){return o.Message.setProto3BytesField(this,3,e)},TronWebProto.Account.prototype.getBalance=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.Account.prototype.setBalance=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.Account.prototype.getVotesList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Vote,5)},TronWebProto.Account.prototype.setVotesList=function(e){return o.Message.setRepeatedWrapperField(this,5,e)},TronWebProto.Account.prototype.addVotes=function(e,t){return o.Message.addToRepeatedWrapperField(this,5,e,TronWebProto.Vote,t)},TronWebProto.Account.prototype.clearVotesList=function(){return this.setVotesList([])},TronWebProto.Account.prototype.getAssetMap=function(e){return o.Message.getMapField(this,6,e,null)},TronWebProto.Account.prototype.clearAssetMap=function(){return this.getAssetMap().clear(),this},TronWebProto.Account.prototype.getAssetv2Map=function(e){return o.Message.getMapField(this,56,e,null)},TronWebProto.Account.prototype.clearAssetv2Map=function(){return this.getAssetv2Map().clear(),this},TronWebProto.Account.prototype.getFrozenList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Account.Frozen,7)},TronWebProto.Account.prototype.setFrozenList=function(e){return o.Message.setRepeatedWrapperField(this,7,e)},TronWebProto.Account.prototype.addFrozen=function(e,t){return o.Message.addToRepeatedWrapperField(this,7,e,TronWebProto.Account.Frozen,t)},TronWebProto.Account.prototype.clearFrozenList=function(){return this.setFrozenList([])},TronWebProto.Account.prototype.getNetUsage=function(){return o.Message.getFieldWithDefault(this,8,0)},TronWebProto.Account.prototype.setNetUsage=function(e){return o.Message.setProto3IntField(this,8,e)},TronWebProto.Account.prototype.getAcquiredDelegatedFrozenBalanceForBandwidth=function(){return o.Message.getFieldWithDefault(this,41,0)},TronWebProto.Account.prototype.setAcquiredDelegatedFrozenBalanceForBandwidth=function(e){return o.Message.setProto3IntField(this,41,e)},TronWebProto.Account.prototype.getDelegatedFrozenBalanceForBandwidth=function(){return o.Message.getFieldWithDefault(this,42,0)},TronWebProto.Account.prototype.setDelegatedFrozenBalanceForBandwidth=function(e){return o.Message.setProto3IntField(this,42,e)},TronWebProto.Account.prototype.getOldTronPower=function(){return o.Message.getFieldWithDefault(this,46,0)},TronWebProto.Account.prototype.setOldTronPower=function(e){return o.Message.setProto3IntField(this,46,e)},TronWebProto.Account.prototype.getTronPower=function(){return o.Message.getWrapperField(this,TronWebProto.Account.Frozen,47)},TronWebProto.Account.prototype.setTronPower=function(e){return o.Message.setWrapperField(this,47,e)},TronWebProto.Account.prototype.clearTronPower=function(){return this.setTronPower(void 0)},TronWebProto.Account.prototype.hasTronPower=function(){return null!=o.Message.getField(this,47)},TronWebProto.Account.prototype.getAssetOptimized=function(){return o.Message.getBooleanFieldWithDefault(this,60,!1)},TronWebProto.Account.prototype.setAssetOptimized=function(e){return o.Message.setProto3BooleanField(this,60,e)},TronWebProto.Account.prototype.getCreateTime=function(){return o.Message.getFieldWithDefault(this,9,0)},TronWebProto.Account.prototype.setCreateTime=function(e){return o.Message.setProto3IntField(this,9,e)},TronWebProto.Account.prototype.getLatestOprationTime=function(){return o.Message.getFieldWithDefault(this,10,0)},TronWebProto.Account.prototype.setLatestOprationTime=function(e){return o.Message.setProto3IntField(this,10,e)},TronWebProto.Account.prototype.getAllowance=function(){return o.Message.getFieldWithDefault(this,11,0)},TronWebProto.Account.prototype.setAllowance=function(e){return o.Message.setProto3IntField(this,11,e)},TronWebProto.Account.prototype.getLatestWithdrawTime=function(){return o.Message.getFieldWithDefault(this,12,0)},TronWebProto.Account.prototype.setLatestWithdrawTime=function(e){return o.Message.setProto3IntField(this,12,e)},TronWebProto.Account.prototype.getCode=function(){return o.Message.getFieldWithDefault(this,13,"")},TronWebProto.Account.prototype.getCode_asB64=function(){return o.Message.bytesAsB64(this.getCode())},TronWebProto.Account.prototype.getCode_asU8=function(){return o.Message.bytesAsU8(this.getCode())},TronWebProto.Account.prototype.setCode=function(e){return o.Message.setProto3BytesField(this,13,e)},TronWebProto.Account.prototype.getIsWitness=function(){return o.Message.getBooleanFieldWithDefault(this,14,!1)},TronWebProto.Account.prototype.setIsWitness=function(e){return o.Message.setProto3BooleanField(this,14,e)},TronWebProto.Account.prototype.getIsCommittee=function(){return o.Message.getBooleanFieldWithDefault(this,15,!1)},TronWebProto.Account.prototype.setIsCommittee=function(e){return o.Message.setProto3BooleanField(this,15,e)},TronWebProto.Account.prototype.getFrozenSupplyList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Account.Frozen,16)},TronWebProto.Account.prototype.setFrozenSupplyList=function(e){return o.Message.setRepeatedWrapperField(this,16,e)},TronWebProto.Account.prototype.addFrozenSupply=function(e,t){return o.Message.addToRepeatedWrapperField(this,16,e,TronWebProto.Account.Frozen,t)},TronWebProto.Account.prototype.clearFrozenSupplyList=function(){return this.setFrozenSupplyList([])},TronWebProto.Account.prototype.getAssetIssuedName=function(){return o.Message.getFieldWithDefault(this,17,"")},TronWebProto.Account.prototype.getAssetIssuedName_asB64=function(){return o.Message.bytesAsB64(this.getAssetIssuedName())},TronWebProto.Account.prototype.getAssetIssuedName_asU8=function(){return o.Message.bytesAsU8(this.getAssetIssuedName())},TronWebProto.Account.prototype.setAssetIssuedName=function(e){return o.Message.setProto3BytesField(this,17,e)},TronWebProto.Account.prototype.getAssetIssuedId=function(){return o.Message.getFieldWithDefault(this,57,"")},TronWebProto.Account.prototype.getAssetIssuedId_asB64=function(){return o.Message.bytesAsB64(this.getAssetIssuedId())},TronWebProto.Account.prototype.getAssetIssuedId_asU8=function(){return o.Message.bytesAsU8(this.getAssetIssuedId())},TronWebProto.Account.prototype.setAssetIssuedId=function(e){return o.Message.setProto3BytesField(this,57,e)},TronWebProto.Account.prototype.getLatestAssetOperationTimeMap=function(e){return o.Message.getMapField(this,18,e,null)},TronWebProto.Account.prototype.clearLatestAssetOperationTimeMap=function(){return this.getLatestAssetOperationTimeMap().clear(),this},TronWebProto.Account.prototype.getLatestAssetOperationTimev2Map=function(e){return o.Message.getMapField(this,58,e,null)},TronWebProto.Account.prototype.clearLatestAssetOperationTimev2Map=function(){return this.getLatestAssetOperationTimev2Map().clear(),this},TronWebProto.Account.prototype.getFreeNetUsage=function(){return o.Message.getFieldWithDefault(this,19,0)},TronWebProto.Account.prototype.setFreeNetUsage=function(e){return o.Message.setProto3IntField(this,19,e)},TronWebProto.Account.prototype.getFreeAssetNetUsageMap=function(e){return o.Message.getMapField(this,20,e,null)},TronWebProto.Account.prototype.clearFreeAssetNetUsageMap=function(){return this.getFreeAssetNetUsageMap().clear(),this},TronWebProto.Account.prototype.getFreeAssetNetUsagev2Map=function(e){return o.Message.getMapField(this,59,e,null)},TronWebProto.Account.prototype.clearFreeAssetNetUsagev2Map=function(){return this.getFreeAssetNetUsagev2Map().clear(),this},TronWebProto.Account.prototype.getLatestConsumeTime=function(){return o.Message.getFieldWithDefault(this,21,0)},TronWebProto.Account.prototype.setLatestConsumeTime=function(e){return o.Message.setProto3IntField(this,21,e)},TronWebProto.Account.prototype.getLatestConsumeFreeTime=function(){return o.Message.getFieldWithDefault(this,22,0)},TronWebProto.Account.prototype.setLatestConsumeFreeTime=function(e){return o.Message.setProto3IntField(this,22,e)},TronWebProto.Account.prototype.getAccountId=function(){return o.Message.getFieldWithDefault(this,23,"")},TronWebProto.Account.prototype.getAccountId_asB64=function(){return o.Message.bytesAsB64(this.getAccountId())},TronWebProto.Account.prototype.getAccountId_asU8=function(){return o.Message.bytesAsU8(this.getAccountId())},TronWebProto.Account.prototype.setAccountId=function(e){return o.Message.setProto3BytesField(this,23,e)},TronWebProto.Account.prototype.getAccountResource=function(){return o.Message.getWrapperField(this,TronWebProto.Account.AccountResource,26)},TronWebProto.Account.prototype.setAccountResource=function(e){return o.Message.setWrapperField(this,26,e)},TronWebProto.Account.prototype.clearAccountResource=function(){return this.setAccountResource(void 0)},TronWebProto.Account.prototype.hasAccountResource=function(){return null!=o.Message.getField(this,26)},TronWebProto.Account.prototype.getCodehash=function(){return o.Message.getFieldWithDefault(this,30,"")},TronWebProto.Account.prototype.getCodehash_asB64=function(){return o.Message.bytesAsB64(this.getCodehash())},TronWebProto.Account.prototype.getCodehash_asU8=function(){return o.Message.bytesAsU8(this.getCodehash())},TronWebProto.Account.prototype.setCodehash=function(e){return o.Message.setProto3BytesField(this,30,e)},TronWebProto.Account.prototype.getOwnerPermission=function(){return o.Message.getWrapperField(this,TronWebProto.Permission,31)},TronWebProto.Account.prototype.setOwnerPermission=function(e){return o.Message.setWrapperField(this,31,e)},TronWebProto.Account.prototype.clearOwnerPermission=function(){return this.setOwnerPermission(void 0)},TronWebProto.Account.prototype.hasOwnerPermission=function(){return null!=o.Message.getField(this,31)},TronWebProto.Account.prototype.getWitnessPermission=function(){return o.Message.getWrapperField(this,TronWebProto.Permission,32)},TronWebProto.Account.prototype.setWitnessPermission=function(e){return o.Message.setWrapperField(this,32,e)},TronWebProto.Account.prototype.clearWitnessPermission=function(){return this.setWitnessPermission(void 0)},TronWebProto.Account.prototype.hasWitnessPermission=function(){return null!=o.Message.getField(this,32)},TronWebProto.Account.prototype.getActivePermissionList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Permission,33)},TronWebProto.Account.prototype.setActivePermissionList=function(e){return o.Message.setRepeatedWrapperField(this,33,e)},TronWebProto.Account.prototype.addActivePermission=function(e,t){return o.Message.addToRepeatedWrapperField(this,33,e,TronWebProto.Permission,t)},TronWebProto.Account.prototype.clearActivePermissionList=function(){return this.setActivePermissionList([])},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Key.prototype.toObject=function(e){return TronWebProto.Key.toObject(e,this)},TronWebProto.Key.toObject=function(e,t){var r={address:t.getAddress_asB64(),weight:o.Message.getFieldWithDefault(t,2,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Key.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Key;return TronWebProto.Key.deserializeBinaryFromReader(r,t)},TronWebProto.Key.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setAddress(r);break;case 2:var r=t.readInt64();e.setWeight(r);break;default:t.skipField()}return e},TronWebProto.Key.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Key.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Key.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getAddress_asU8()).length>0&&t.writeBytes(1,r),0!==(r=e.getWeight())&&t.writeInt64(2,r)},TronWebProto.Key.prototype.getAddress=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Key.prototype.getAddress_asB64=function(){return o.Message.bytesAsB64(this.getAddress())},TronWebProto.Key.prototype.getAddress_asU8=function(){return o.Message.bytesAsU8(this.getAddress())},TronWebProto.Key.prototype.setAddress=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Key.prototype.getWeight=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Key.prototype.setWeight=function(e){return o.Message.setProto3IntField(this,2,e)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.DelegatedResource.prototype.toObject=function(e){return TronWebProto.DelegatedResource.toObject(e,this)},TronWebProto.DelegatedResource.toObject=function(e,t){var r={from:t.getFrom_asB64(),to:t.getTo_asB64(),frozenBalanceForBandwidth:o.Message.getFieldWithDefault(t,3,0),frozenBalanceForEnergy:o.Message.getFieldWithDefault(t,4,0),expireTimeForBandwidth:o.Message.getFieldWithDefault(t,5,0),expireTimeForEnergy:o.Message.getFieldWithDefault(t,6,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.DelegatedResource.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.DelegatedResource;return TronWebProto.DelegatedResource.deserializeBinaryFromReader(r,t)},TronWebProto.DelegatedResource.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setFrom(r);break;case 2:var r=t.readBytes();e.setTo(r);break;case 3:var r=t.readInt64();e.setFrozenBalanceForBandwidth(r);break;case 4:var r=t.readInt64();e.setFrozenBalanceForEnergy(r);break;case 5:var r=t.readInt64();e.setExpireTimeForBandwidth(r);break;case 6:var r=t.readInt64();e.setExpireTimeForEnergy(r);break;default:t.skipField()}return e},TronWebProto.DelegatedResource.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.DelegatedResource.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.DelegatedResource.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getFrom_asU8()).length>0&&t.writeBytes(1,r),(r=e.getTo_asU8()).length>0&&t.writeBytes(2,r),0!==(r=e.getFrozenBalanceForBandwidth())&&t.writeInt64(3,r),0!==(r=e.getFrozenBalanceForEnergy())&&t.writeInt64(4,r),0!==(r=e.getExpireTimeForBandwidth())&&t.writeInt64(5,r),0!==(r=e.getExpireTimeForEnergy())&&t.writeInt64(6,r)},TronWebProto.DelegatedResource.prototype.getFrom=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.DelegatedResource.prototype.getFrom_asB64=function(){return o.Message.bytesAsB64(this.getFrom())},TronWebProto.DelegatedResource.prototype.getFrom_asU8=function(){return o.Message.bytesAsU8(this.getFrom())},TronWebProto.DelegatedResource.prototype.setFrom=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.DelegatedResource.prototype.getTo=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.DelegatedResource.prototype.getTo_asB64=function(){return o.Message.bytesAsB64(this.getTo())},TronWebProto.DelegatedResource.prototype.getTo_asU8=function(){return o.Message.bytesAsU8(this.getTo())},TronWebProto.DelegatedResource.prototype.setTo=function(e){return o.Message.setProto3BytesField(this,2,e)},TronWebProto.DelegatedResource.prototype.getFrozenBalanceForBandwidth=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.DelegatedResource.prototype.setFrozenBalanceForBandwidth=function(e){return o.Message.setProto3IntField(this,3,e)},TronWebProto.DelegatedResource.prototype.getFrozenBalanceForEnergy=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.DelegatedResource.prototype.setFrozenBalanceForEnergy=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.DelegatedResource.prototype.getExpireTimeForBandwidth=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.DelegatedResource.prototype.setExpireTimeForBandwidth=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.DelegatedResource.prototype.getExpireTimeForEnergy=function(){return o.Message.getFieldWithDefault(this,6,0)},TronWebProto.DelegatedResource.prototype.setExpireTimeForEnergy=function(e){return o.Message.setProto3IntField(this,6,e)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.authority.prototype.toObject=function(e){return TronWebProto.authority.toObject(e,this)},TronWebProto.authority.toObject=function(e,t){var r,o={account:(r=t.getAccount())&&TronWebProto.AccountId.toObject(e,r),permissionName:t.getPermissionName_asB64()};return e&&(o.$jspbMessageInstance=t),o}),TronWebProto.authority.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.authority;return TronWebProto.authority.deserializeBinaryFromReader(r,t)},TronWebProto.authority.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=new TronWebProto.AccountId;t.readMessage(r,TronWebProto.AccountId.deserializeBinaryFromReader),e.setAccount(r);break;case 2:var r=t.readBytes();e.setPermissionName(r);break;default:t.skipField()}return e},TronWebProto.authority.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.authority.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.authority.serializeBinaryToWriter=function(e,t){var r=void 0;null!=(r=e.getAccount())&&t.writeMessage(1,r,TronWebProto.AccountId.serializeBinaryToWriter),(r=e.getPermissionName_asU8()).length>0&&t.writeBytes(2,r)},TronWebProto.authority.prototype.getAccount=function(){return o.Message.getWrapperField(this,TronWebProto.AccountId,1)},TronWebProto.authority.prototype.setAccount=function(e){return o.Message.setWrapperField(this,1,e)},TronWebProto.authority.prototype.clearAccount=function(){return this.setAccount(void 0)},TronWebProto.authority.prototype.hasAccount=function(){return null!=o.Message.getField(this,1)},TronWebProto.authority.prototype.getPermissionName=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.authority.prototype.getPermissionName_asB64=function(){return o.Message.bytesAsB64(this.getPermissionName())},TronWebProto.authority.prototype.getPermissionName_asU8=function(){return o.Message.bytesAsU8(this.getPermissionName())},TronWebProto.authority.prototype.setPermissionName=function(e){return o.Message.setProto3BytesField(this,2,e)},TronWebProto.Permission.repeatedFields_=[7],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Permission.prototype.toObject=function(e){return TronWebProto.Permission.toObject(e,this)},TronWebProto.Permission.toObject=function(e,t){var r={type:o.Message.getFieldWithDefault(t,1,0),id:o.Message.getFieldWithDefault(t,2,0),permissionName:o.Message.getFieldWithDefault(t,3,""),threshold:o.Message.getFieldWithDefault(t,4,0),parentId:o.Message.getFieldWithDefault(t,5,0),operations:t.getOperations_asB64(),keysList:o.Message.toObjectList(t.getKeysList(),TronWebProto.Key.toObject,e)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Permission.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Permission;return TronWebProto.Permission.deserializeBinaryFromReader(r,t)},TronWebProto.Permission.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readEnum();e.setType(r);break;case 2:var r=t.readInt32();e.setId(r);break;case 3:var r=t.readString();e.setPermissionName(r);break;case 4:var r=t.readInt64();e.setThreshold(r);break;case 5:var r=t.readInt32();e.setParentId(r);break;case 6:var r=t.readBytes();e.setOperations(r);break;case 7:var r=new TronWebProto.Key;t.readMessage(r,TronWebProto.Key.deserializeBinaryFromReader),e.addKeys(r);break;default:t.skipField()}return e},TronWebProto.Permission.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Permission.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Permission.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getType())&&t.writeEnum(1,r),0!==(r=e.getId())&&t.writeInt32(2,r),(r=e.getPermissionName()).length>0&&t.writeString(3,r),0!==(r=e.getThreshold())&&t.writeInt64(4,r),0!==(r=e.getParentId())&&t.writeInt32(5,r),(r=e.getOperations_asU8()).length>0&&t.writeBytes(6,r),(r=e.getKeysList()).length>0&&t.writeRepeatedMessage(7,r,TronWebProto.Key.serializeBinaryToWriter)},TronWebProto.Permission.PermissionType={OWNER:0,WITNESS:1,ACTIVE:2},TronWebProto.Permission.prototype.getType=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Permission.prototype.setType=function(e){return o.Message.setProto3EnumField(this,1,e)},TronWebProto.Permission.prototype.getId=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Permission.prototype.setId=function(e){return o.Message.setProto3IntField(this,2,e)},TronWebProto.Permission.prototype.getPermissionName=function(){return o.Message.getFieldWithDefault(this,3,"")},TronWebProto.Permission.prototype.setPermissionName=function(e){return o.Message.setProto3StringField(this,3,e)},TronWebProto.Permission.prototype.getThreshold=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.Permission.prototype.setThreshold=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.Permission.prototype.getParentId=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.Permission.prototype.setParentId=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.Permission.prototype.getOperations=function(){return o.Message.getFieldWithDefault(this,6,"")},TronWebProto.Permission.prototype.getOperations_asB64=function(){return o.Message.bytesAsB64(this.getOperations())},TronWebProto.Permission.prototype.getOperations_asU8=function(){return o.Message.bytesAsU8(this.getOperations())},TronWebProto.Permission.prototype.setOperations=function(e){return o.Message.setProto3BytesField(this,6,e)},TronWebProto.Permission.prototype.getKeysList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Key,7)},TronWebProto.Permission.prototype.setKeysList=function(e){return o.Message.setRepeatedWrapperField(this,7,e)},TronWebProto.Permission.prototype.addKeys=function(e,t){return o.Message.addToRepeatedWrapperField(this,7,e,TronWebProto.Key,t)},TronWebProto.Permission.prototype.clearKeysList=function(){return this.setKeysList([])},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Witness.prototype.toObject=function(e){return TronWebProto.Witness.toObject(e,this)},TronWebProto.Witness.toObject=function(e,t){var r={address:t.getAddress_asB64(),votecount:o.Message.getFieldWithDefault(t,2,0),pubkey:t.getPubkey_asB64(),url:o.Message.getFieldWithDefault(t,4,""),totalproduced:o.Message.getFieldWithDefault(t,5,0),totalmissed:o.Message.getFieldWithDefault(t,6,0),latestblocknum:o.Message.getFieldWithDefault(t,7,0),latestslotnum:o.Message.getFieldWithDefault(t,8,0),isjobs:o.Message.getBooleanFieldWithDefault(t,9,!1)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Witness.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Witness;return TronWebProto.Witness.deserializeBinaryFromReader(r,t)},TronWebProto.Witness.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setAddress(r);break;case 2:var r=t.readInt64();e.setVotecount(r);break;case 3:var r=t.readBytes();e.setPubkey(r);break;case 4:var r=t.readString();e.setUrl(r);break;case 5:var r=t.readInt64();e.setTotalproduced(r);break;case 6:var r=t.readInt64();e.setTotalmissed(r);break;case 7:var r=t.readInt64();e.setLatestblocknum(r);break;case 8:var r=t.readInt64();e.setLatestslotnum(r);break;case 9:var r=t.readBool();e.setIsjobs(r);break;default:t.skipField()}return e},TronWebProto.Witness.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Witness.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Witness.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getAddress_asU8()).length>0&&t.writeBytes(1,r),0!==(r=e.getVotecount())&&t.writeInt64(2,r),(r=e.getPubkey_asU8()).length>0&&t.writeBytes(3,r),(r=e.getUrl()).length>0&&t.writeString(4,r),0!==(r=e.getTotalproduced())&&t.writeInt64(5,r),0!==(r=e.getTotalmissed())&&t.writeInt64(6,r),0!==(r=e.getLatestblocknum())&&t.writeInt64(7,r),0!==(r=e.getLatestslotnum())&&t.writeInt64(8,r),(r=e.getIsjobs())&&t.writeBool(9,r)},TronWebProto.Witness.prototype.getAddress=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Witness.prototype.getAddress_asB64=function(){return o.Message.bytesAsB64(this.getAddress())},TronWebProto.Witness.prototype.getAddress_asU8=function(){return o.Message.bytesAsU8(this.getAddress())},TronWebProto.Witness.prototype.setAddress=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Witness.prototype.getVotecount=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Witness.prototype.setVotecount=function(e){return o.Message.setProto3IntField(this,2,e)},TronWebProto.Witness.prototype.getPubkey=function(){return o.Message.getFieldWithDefault(this,3,"")},TronWebProto.Witness.prototype.getPubkey_asB64=function(){return o.Message.bytesAsB64(this.getPubkey())},TronWebProto.Witness.prototype.getPubkey_asU8=function(){return o.Message.bytesAsU8(this.getPubkey())},TronWebProto.Witness.prototype.setPubkey=function(e){return o.Message.setProto3BytesField(this,3,e)},TronWebProto.Witness.prototype.getUrl=function(){return o.Message.getFieldWithDefault(this,4,"")},TronWebProto.Witness.prototype.setUrl=function(e){return o.Message.setProto3StringField(this,4,e)},TronWebProto.Witness.prototype.getTotalproduced=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.Witness.prototype.setTotalproduced=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.Witness.prototype.getTotalmissed=function(){return o.Message.getFieldWithDefault(this,6,0)},TronWebProto.Witness.prototype.setTotalmissed=function(e){return o.Message.setProto3IntField(this,6,e)},TronWebProto.Witness.prototype.getLatestblocknum=function(){return o.Message.getFieldWithDefault(this,7,0)},TronWebProto.Witness.prototype.setLatestblocknum=function(e){return o.Message.setProto3IntField(this,7,e)},TronWebProto.Witness.prototype.getLatestslotnum=function(){return o.Message.getFieldWithDefault(this,8,0)},TronWebProto.Witness.prototype.setLatestslotnum=function(e){return o.Message.setProto3IntField(this,8,e)},TronWebProto.Witness.prototype.getIsjobs=function(){return o.Message.getBooleanFieldWithDefault(this,9,!1)},TronWebProto.Witness.prototype.setIsjobs=function(e){return o.Message.setProto3BooleanField(this,9,e)},TronWebProto.Votes.repeatedFields_=[2,3],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Votes.prototype.toObject=function(e){return TronWebProto.Votes.toObject(e,this)},TronWebProto.Votes.toObject=function(e,t){var r={address:t.getAddress_asB64(),oldVotesList:o.Message.toObjectList(t.getOldVotesList(),TronWebProto.Vote.toObject,e),newVotesList:o.Message.toObjectList(t.getNewVotesList(),TronWebProto.Vote.toObject,e)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Votes.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Votes;return TronWebProto.Votes.deserializeBinaryFromReader(r,t)},TronWebProto.Votes.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setAddress(r);break;case 2:var r=new TronWebProto.Vote;t.readMessage(r,TronWebProto.Vote.deserializeBinaryFromReader),e.addOldVotes(r);break;case 3:var r=new TronWebProto.Vote;t.readMessage(r,TronWebProto.Vote.deserializeBinaryFromReader),e.addNewVotes(r);break;default:t.skipField()}return e},TronWebProto.Votes.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Votes.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Votes.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getAddress_asU8()).length>0&&t.writeBytes(1,r),(r=e.getOldVotesList()).length>0&&t.writeRepeatedMessage(2,r,TronWebProto.Vote.serializeBinaryToWriter),(r=e.getNewVotesList()).length>0&&t.writeRepeatedMessage(3,r,TronWebProto.Vote.serializeBinaryToWriter)},TronWebProto.Votes.prototype.getAddress=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Votes.prototype.getAddress_asB64=function(){return o.Message.bytesAsB64(this.getAddress())},TronWebProto.Votes.prototype.getAddress_asU8=function(){return o.Message.bytesAsU8(this.getAddress())},TronWebProto.Votes.prototype.setAddress=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Votes.prototype.getOldVotesList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Vote,2)},TronWebProto.Votes.prototype.setOldVotesList=function(e){return o.Message.setRepeatedWrapperField(this,2,e)},TronWebProto.Votes.prototype.addOldVotes=function(e,t){return o.Message.addToRepeatedWrapperField(this,2,e,TronWebProto.Vote,t)},TronWebProto.Votes.prototype.clearOldVotesList=function(){return this.setOldVotesList([])},TronWebProto.Votes.prototype.getNewVotesList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Vote,3)},TronWebProto.Votes.prototype.setNewVotesList=function(e){return o.Message.setRepeatedWrapperField(this,3,e)},TronWebProto.Votes.prototype.addNewVotes=function(e,t){return o.Message.addToRepeatedWrapperField(this,3,e,TronWebProto.Vote,t)},TronWebProto.Votes.prototype.clearNewVotesList=function(){return this.setNewVotesList([])},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.MarketOrderDetail.prototype.toObject=function(e){return TronWebProto.MarketOrderDetail.toObject(e,this)},TronWebProto.MarketOrderDetail.toObject=function(e,t){var r={makerorderid:t.getMakerorderid_asB64(),takerorderid:t.getTakerorderid_asB64(),fillsellquantity:o.Message.getFieldWithDefault(t,3,0),fillbuyquantity:o.Message.getFieldWithDefault(t,4,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.MarketOrderDetail.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.MarketOrderDetail;return TronWebProto.MarketOrderDetail.deserializeBinaryFromReader(r,t)},TronWebProto.MarketOrderDetail.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setMakerorderid(r);break;case 2:var r=t.readBytes();e.setTakerorderid(r);break;case 3:var r=t.readInt64();e.setFillsellquantity(r);break;case 4:var r=t.readInt64();e.setFillbuyquantity(r);break;default:t.skipField()}return e},TronWebProto.MarketOrderDetail.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.MarketOrderDetail.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.MarketOrderDetail.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getMakerorderid_asU8()).length>0&&t.writeBytes(1,r),(r=e.getTakerorderid_asU8()).length>0&&t.writeBytes(2,r),0!==(r=e.getFillsellquantity())&&t.writeInt64(3,r),0!==(r=e.getFillbuyquantity())&&t.writeInt64(4,r)},TronWebProto.MarketOrderDetail.prototype.getMakerorderid=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.MarketOrderDetail.prototype.getMakerorderid_asB64=function(){return o.Message.bytesAsB64(this.getMakerorderid())},TronWebProto.MarketOrderDetail.prototype.getMakerorderid_asU8=function(){return o.Message.bytesAsU8(this.getMakerorderid())},TronWebProto.MarketOrderDetail.prototype.setMakerorderid=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.MarketOrderDetail.prototype.getTakerorderid=function(){return o.Message.getFieldWithDefault(this,2,"")},TronWebProto.MarketOrderDetail.prototype.getTakerorderid_asB64=function(){return o.Message.bytesAsB64(this.getTakerorderid())},TronWebProto.MarketOrderDetail.prototype.getTakerorderid_asU8=function(){return o.Message.bytesAsU8(this.getTakerorderid())},TronWebProto.MarketOrderDetail.prototype.setTakerorderid=function(e){return o.Message.setProto3BytesField(this,2,e)},TronWebProto.MarketOrderDetail.prototype.getFillsellquantity=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.MarketOrderDetail.prototype.setFillsellquantity=function(e){return o.Message.setProto3IntField(this,3,e)},TronWebProto.MarketOrderDetail.prototype.getFillbuyquantity=function(){return o.Message.getFieldWithDefault(this,4,0)},TronWebProto.MarketOrderDetail.prototype.setFillbuyquantity=function(e){return o.Message.setProto3IntField(this,4,e)},TronWebProto.Transaction.repeatedFields_=[2,5],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Transaction.prototype.toObject=function(e){return TronWebProto.Transaction.toObject(e,this)},TronWebProto.Transaction.toObject=function(e,t){var r,n={rawData:(r=t.getRawData())&&TronWebProto.Transaction.raw.toObject(e,r),signatureList:t.getSignatureList_asB64(),retList:o.Message.toObjectList(t.getRetList(),TronWebProto.Transaction.Result.toObject,e)};return e&&(n.$jspbMessageInstance=t),n}),TronWebProto.Transaction.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Transaction;return TronWebProto.Transaction.deserializeBinaryFromReader(r,t)},TronWebProto.Transaction.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=new TronWebProto.Transaction.raw;t.readMessage(r,TronWebProto.Transaction.raw.deserializeBinaryFromReader),e.setRawData(r);break;case 2:var r=t.readBytes();e.addSignature(r);break;case 5:var r=new TronWebProto.Transaction.Result;t.readMessage(r,TronWebProto.Transaction.Result.deserializeBinaryFromReader),e.addRet(r);break;default:t.skipField()}return e},TronWebProto.Transaction.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Transaction.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Transaction.serializeBinaryToWriter=function(e,t){var r=void 0;null!=(r=e.getRawData())&&t.writeMessage(1,r,TronWebProto.Transaction.raw.serializeBinaryToWriter),(r=e.getSignatureList_asU8()).length>0&&t.writeRepeatedBytes(2,r),(r=e.getRetList()).length>0&&t.writeRepeatedMessage(5,r,TronWebProto.Transaction.Result.serializeBinaryToWriter)},o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Transaction.Contract.prototype.toObject=function(e){return TronWebProto.Transaction.Contract.toObject(e,this)},TronWebProto.Transaction.Contract.toObject=function(e,t){var r,n={type:o.Message.getFieldWithDefault(t,1,0),parameter:(r=t.getParameter())&&s.Any.toObject(e,r),provider:t.getProvider_asB64(),contractname:t.getContractname_asB64(),permissionId:o.Message.getFieldWithDefault(t,5,0)};return e&&(n.$jspbMessageInstance=t),n}),TronWebProto.Transaction.Contract.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Transaction.Contract;return TronWebProto.Transaction.Contract.deserializeBinaryFromReader(r,t)},TronWebProto.Transaction.Contract.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readEnum();e.setType(r);break;case 2:var r=new s.Any;t.readMessage(r,s.Any.deserializeBinaryFromReader),e.setParameter(r);break;case 3:var r=t.readBytes();e.setProvider(r);break;case 4:var r=t.readBytes();e.setContractname(r);break;case 5:var r=t.readInt32();e.setPermissionId(r);break;default:t.skipField()}return e},TronWebProto.Transaction.Contract.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Transaction.Contract.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Transaction.Contract.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getType())&&t.writeEnum(1,r),null!=(r=e.getParameter())&&t.writeMessage(2,r,s.Any.serializeBinaryToWriter),(r=e.getProvider_asU8()).length>0&&t.writeBytes(3,r),(r=e.getContractname_asU8()).length>0&&t.writeBytes(4,r),0!==(r=e.getPermissionId())&&t.writeInt32(5,r)},TronWebProto.Transaction.Contract.ContractType={ACCOUNTCREATECONTRACT:0,TRANSFERCONTRACT:1,TRANSFERASSETCONTRACT:2,VOTEASSETCONTRACT:3,VOTEWITNESSCONTRACT:4,WITNESSCREATECONTRACT:5,ASSETISSUECONTRACT:6,WITNESSUPDATECONTRACT:8,PARTICIPATEASSETISSUECONTRACT:9,ACCOUNTUPDATECONTRACT:10,FREEZEBALANCECONTRACT:11,UNFREEZEBALANCECONTRACT:12,WITHDRAWBALANCECONTRACT:13,UNFREEZEASSETCONTRACT:14,UPDATEASSETCONTRACT:15,PROPOSALCREATECONTRACT:16,PROPOSALAPPROVECONTRACT:17,PROPOSALDELETECONTRACT:18,SETACCOUNTIDCONTRACT:19,CUSTOMCONTRACT:20,CREATESMARTCONTRACT:30,TRIGGERSMARTCONTRACT:31,GETCONTRACT:32,UPDATESETTINGCONTRACT:33,EXCHANGECREATECONTRACT:41,EXCHANGEINJECTCONTRACT:42,EXCHANGEWITHDRAWCONTRACT:43,EXCHANGETRANSACTIONCONTRACT:44,UPDATEENERGYLIMITCONTRACT:45,ACCOUNTPERMISSIONUPDATECONTRACT:46,CLEARABICONTRACT:48,UPDATEBROKERAGECONTRACT:49,SHIELDEDTRANSFERCONTRACT:51,MARKETSELLASSETCONTRACT:52,MARKETCANCELORDERCONTRACT:53,FREEZEBALANCEV2CONTRACT:54,UNFREEZEBALANCEV2CONTRACT:55,WITHDRAWEXPIREUNFREEZECONTRACT:56,DELEGATERESOURCECONTRACT:57,UNDELEGATERESOURCECONTRACT:58,CANCELALLUNFREEZEV2CONTRACT:59},TronWebProto.Transaction.Contract.prototype.getType=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Transaction.Contract.prototype.setType=function(e){return o.Message.setProto3EnumField(this,1,e)},TronWebProto.Transaction.Contract.prototype.getParameter=function(){return o.Message.getWrapperField(this,s.Any,2)},TronWebProto.Transaction.Contract.prototype.setParameter=function(e){return o.Message.setWrapperField(this,2,e)},TronWebProto.Transaction.Contract.prototype.clearParameter=function(){return this.setParameter(void 0)},TronWebProto.Transaction.Contract.prototype.hasParameter=function(){return null!=o.Message.getField(this,2)},TronWebProto.Transaction.Contract.prototype.getProvider=function(){return o.Message.getFieldWithDefault(this,3,"")},TronWebProto.Transaction.Contract.prototype.getProvider_asB64=function(){return o.Message.bytesAsB64(this.getProvider())},TronWebProto.Transaction.Contract.prototype.getProvider_asU8=function(){return o.Message.bytesAsU8(this.getProvider())},TronWebProto.Transaction.Contract.prototype.setProvider=function(e){return o.Message.setProto3BytesField(this,3,e)},TronWebProto.Transaction.Contract.prototype.getContractname=function(){return o.Message.getFieldWithDefault(this,4,"")},TronWebProto.Transaction.Contract.prototype.getContractname_asB64=function(){return o.Message.bytesAsB64(this.getContractname())},TronWebProto.Transaction.Contract.prototype.getContractname_asU8=function(){return o.Message.bytesAsU8(this.getContractname())},TronWebProto.Transaction.Contract.prototype.setContractname=function(e){return o.Message.setProto3BytesField(this,4,e)},TronWebProto.Transaction.Contract.prototype.getPermissionId=function(){return o.Message.getFieldWithDefault(this,5,0)},TronWebProto.Transaction.Contract.prototype.setPermissionId=function(e){return o.Message.setProto3IntField(this,5,e)},TronWebProto.Transaction.Result.repeatedFields_=[26],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Transaction.Result.prototype.toObject=function(e){return TronWebProto.Transaction.Result.toObject(e,this)},TronWebProto.Transaction.Result.toObject=function(e,t){var r={fee:o.Message.getFieldWithDefault(t,1,0),ret:o.Message.getFieldWithDefault(t,2,0),contractret:o.Message.getFieldWithDefault(t,3,0),assetissueid:o.Message.getFieldWithDefault(t,14,""),withdrawAmount:o.Message.getFieldWithDefault(t,15,0),unfreezeAmount:o.Message.getFieldWithDefault(t,16,0),exchangeReceivedAmount:o.Message.getFieldWithDefault(t,18,0),exchangeInjectAnotherAmount:o.Message.getFieldWithDefault(t,19,0),exchangeWithdrawAnotherAmount:o.Message.getFieldWithDefault(t,20,0),exchangeId:o.Message.getFieldWithDefault(t,21,0),shieldedTransactionFee:o.Message.getFieldWithDefault(t,22,0),orderid:t.getOrderid_asB64(),orderdetailsList:o.Message.toObjectList(t.getOrderdetailsList(),TronWebProto.MarketOrderDetail.toObject,e)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Transaction.Result.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Transaction.Result;return TronWebProto.Transaction.Result.deserializeBinaryFromReader(r,t)},TronWebProto.Transaction.Result.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readInt64();e.setFee(r);break;case 2:var r=t.readEnum();e.setRet(r);break;case 3:var r=t.readEnum();e.setContractret(r);break;case 14:var r=t.readString();e.setAssetissueid(r);break;case 15:var r=t.readInt64();e.setWithdrawAmount(r);break;case 16:var r=t.readInt64();e.setUnfreezeAmount(r);break;case 18:var r=t.readInt64();e.setExchangeReceivedAmount(r);break;case 19:var r=t.readInt64();e.setExchangeInjectAnotherAmount(r);break;case 20:var r=t.readInt64();e.setExchangeWithdrawAnotherAmount(r);break;case 21:var r=t.readInt64();e.setExchangeId(r);break;case 22:var r=t.readInt64();e.setShieldedTransactionFee(r);break;case 25:var r=t.readBytes();e.setOrderid(r);break;case 26:var r=new TronWebProto.MarketOrderDetail;t.readMessage(r,TronWebProto.MarketOrderDetail.deserializeBinaryFromReader),e.addOrderdetails(r);break;default:t.skipField()}return e},TronWebProto.Transaction.Result.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Transaction.Result.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Transaction.Result.serializeBinaryToWriter=function(e,t){var r=void 0;0!==(r=e.getFee())&&t.writeInt64(1,r),0!==(r=e.getRet())&&t.writeEnum(2,r),0!==(r=e.getContractret())&&t.writeEnum(3,r),(r=e.getAssetissueid()).length>0&&t.writeString(14,r),0!==(r=e.getWithdrawAmount())&&t.writeInt64(15,r),0!==(r=e.getUnfreezeAmount())&&t.writeInt64(16,r),0!==(r=e.getExchangeReceivedAmount())&&t.writeInt64(18,r),0!==(r=e.getExchangeInjectAnotherAmount())&&t.writeInt64(19,r),0!==(r=e.getExchangeWithdrawAnotherAmount())&&t.writeInt64(20,r),0!==(r=e.getExchangeId())&&t.writeInt64(21,r),0!==(r=e.getShieldedTransactionFee())&&t.writeInt64(22,r),(r=e.getOrderid_asU8()).length>0&&t.writeBytes(25,r),(r=e.getOrderdetailsList()).length>0&&t.writeRepeatedMessage(26,r,TronWebProto.MarketOrderDetail.serializeBinaryToWriter)},TronWebProto.Transaction.Result.code={SUCESS:0,FAILED:1},TronWebProto.Transaction.Result.contractResult={DEFAULT:0,SUCCESS:1,REVERT:2,BAD_JUMP_DESTINATION:3,OUT_OF_MEMORY:4,PRECOMPILED_CONTRACT:5,STACK_TOO_SMALL:6,STACK_TOO_LARGE:7,ILLEGAL_OPERATION:8,STACK_OVERFLOW:9,OUT_OF_ENERGY:10,OUT_OF_TIME:11,JVM_STACK_OVER_FLOW:12,UNKNOWN:13,TRANSFER_FAILED:14,INVALID_CODE:15},TronWebProto.Transaction.Result.prototype.getFee=function(){return o.Message.getFieldWithDefault(this,1,0)},TronWebProto.Transaction.Result.prototype.setFee=function(e){return o.Message.setProto3IntField(this,1,e)},TronWebProto.Transaction.Result.prototype.getRet=function(){return o.Message.getFieldWithDefault(this,2,0)},TronWebProto.Transaction.Result.prototype.setRet=function(e){return o.Message.setProto3EnumField(this,2,e)},TronWebProto.Transaction.Result.prototype.getContractret=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.Transaction.Result.prototype.setContractret=function(e){return o.Message.setProto3EnumField(this,3,e)},TronWebProto.Transaction.Result.prototype.getAssetissueid=function(){return o.Message.getFieldWithDefault(this,14,"")},TronWebProto.Transaction.Result.prototype.setAssetissueid=function(e){return o.Message.setProto3StringField(this,14,e)},TronWebProto.Transaction.Result.prototype.getWithdrawAmount=function(){return o.Message.getFieldWithDefault(this,15,0)},TronWebProto.Transaction.Result.prototype.setWithdrawAmount=function(e){return o.Message.setProto3IntField(this,15,e)},TronWebProto.Transaction.Result.prototype.getUnfreezeAmount=function(){return o.Message.getFieldWithDefault(this,16,0)},TronWebProto.Transaction.Result.prototype.setUnfreezeAmount=function(e){return o.Message.setProto3IntField(this,16,e)},TronWebProto.Transaction.Result.prototype.getExchangeReceivedAmount=function(){return o.Message.getFieldWithDefault(this,18,0)},TronWebProto.Transaction.Result.prototype.setExchangeReceivedAmount=function(e){return o.Message.setProto3IntField(this,18,e)},TronWebProto.Transaction.Result.prototype.getExchangeInjectAnotherAmount=function(){return o.Message.getFieldWithDefault(this,19,0)},TronWebProto.Transaction.Result.prototype.setExchangeInjectAnotherAmount=function(e){return o.Message.setProto3IntField(this,19,e)},TronWebProto.Transaction.Result.prototype.getExchangeWithdrawAnotherAmount=function(){return o.Message.getFieldWithDefault(this,20,0)},TronWebProto.Transaction.Result.prototype.setExchangeWithdrawAnotherAmount=function(e){return o.Message.setProto3IntField(this,20,e)},TronWebProto.Transaction.Result.prototype.getExchangeId=function(){return o.Message.getFieldWithDefault(this,21,0)},TronWebProto.Transaction.Result.prototype.setExchangeId=function(e){return o.Message.setProto3IntField(this,21,e)},TronWebProto.Transaction.Result.prototype.getShieldedTransactionFee=function(){return o.Message.getFieldWithDefault(this,22,0)},TronWebProto.Transaction.Result.prototype.setShieldedTransactionFee=function(e){return o.Message.setProto3IntField(this,22,e)},TronWebProto.Transaction.Result.prototype.getOrderid=function(){return o.Message.getFieldWithDefault(this,25,"")},TronWebProto.Transaction.Result.prototype.getOrderid_asB64=function(){return o.Message.bytesAsB64(this.getOrderid())},TronWebProto.Transaction.Result.prototype.getOrderid_asU8=function(){return o.Message.bytesAsU8(this.getOrderid())},TronWebProto.Transaction.Result.prototype.setOrderid=function(e){return o.Message.setProto3BytesField(this,25,e)},TronWebProto.Transaction.Result.prototype.getOrderdetailsList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.MarketOrderDetail,26)},TronWebProto.Transaction.Result.prototype.setOrderdetailsList=function(e){return o.Message.setRepeatedWrapperField(this,26,e)},TronWebProto.Transaction.Result.prototype.addOrderdetails=function(e,t){return o.Message.addToRepeatedWrapperField(this,26,e,TronWebProto.MarketOrderDetail,t)},TronWebProto.Transaction.Result.prototype.clearOrderdetailsList=function(){return this.setOrderdetailsList([])},TronWebProto.Transaction.raw.repeatedFields_=[9,11],o.Message.GENERATE_TO_OBJECT&&(TronWebProto.Transaction.raw.prototype.toObject=function(e){return TronWebProto.Transaction.raw.toObject(e,this)},TronWebProto.Transaction.raw.toObject=function(e,t){var r={refBlockBytes:t.getRefBlockBytes_asB64(),refBlockNum:o.Message.getFieldWithDefault(t,3,0),refBlockHash:t.getRefBlockHash_asB64(),expiration:o.Message.getFieldWithDefault(t,8,0),authsList:o.Message.toObjectList(t.getAuthsList(),TronWebProto.authority.toObject,e),data:t.getData_asB64(),contractList:o.Message.toObjectList(t.getContractList(),TronWebProto.Transaction.Contract.toObject,e),scripts:t.getScripts_asB64(),timestamp:o.Message.getFieldWithDefault(t,14,0),feeLimit:o.Message.getFieldWithDefault(t,18,0)};return e&&(r.$jspbMessageInstance=t),r}),TronWebProto.Transaction.raw.deserializeBinary=function(e){var t=new o.BinaryReader(e),r=new TronWebProto.Transaction.raw;return TronWebProto.Transaction.raw.deserializeBinaryFromReader(r,t)},TronWebProto.Transaction.raw.deserializeBinaryFromReader=function(e,t){for(;t.nextField()&&!t.isEndGroup();)switch(t.getFieldNumber()){case 1:var r=t.readBytes();e.setRefBlockBytes(r);break;case 3:var r=t.readInt64();e.setRefBlockNum(r);break;case 4:var r=t.readBytes();e.setRefBlockHash(r);break;case 8:var r=t.readInt64();e.setExpiration(r);break;case 9:var r=new TronWebProto.authority;t.readMessage(r,TronWebProto.authority.deserializeBinaryFromReader),e.addAuths(r);break;case 10:var r=t.readBytes();e.setData(r);break;case 11:var r=new TronWebProto.Transaction.Contract;t.readMessage(r,TronWebProto.Transaction.Contract.deserializeBinaryFromReader),e.addContract(r);break;case 12:var r=t.readBytes();e.setScripts(r);break;case 14:var r=t.readInt64();e.setTimestamp(r);break;case 18:var r=t.readInt64();e.setFeeLimit(r);break;default:t.skipField()}return e},TronWebProto.Transaction.raw.prototype.serializeBinary=function(){var e=new o.BinaryWriter;return TronWebProto.Transaction.raw.serializeBinaryToWriter(this,e),e.getResultBuffer()},TronWebProto.Transaction.raw.serializeBinaryToWriter=function(e,t){var r=void 0;(r=e.getRefBlockBytes_asU8()).length>0&&t.writeBytes(1,r),0!==(r=e.getRefBlockNum())&&t.writeInt64(3,r),(r=e.getRefBlockHash_asU8()).length>0&&t.writeBytes(4,r),0!==(r=e.getExpiration())&&t.writeInt64(8,r),(r=e.getAuthsList()).length>0&&t.writeRepeatedMessage(9,r,TronWebProto.authority.serializeBinaryToWriter),(r=e.getData_asU8()).length>0&&t.writeBytes(10,r),(r=e.getContractList()).length>0&&t.writeRepeatedMessage(11,r,TronWebProto.Transaction.Contract.serializeBinaryToWriter),(r=e.getScripts_asU8()).length>0&&t.writeBytes(12,r),0!==(r=e.getTimestamp())&&t.writeInt64(14,r),0!==(r=e.getFeeLimit())&&t.writeInt64(18,r)},TronWebProto.Transaction.raw.prototype.getRefBlockBytes=function(){return o.Message.getFieldWithDefault(this,1,"")},TronWebProto.Transaction.raw.prototype.getRefBlockBytes_asB64=function(){return o.Message.bytesAsB64(this.getRefBlockBytes())},TronWebProto.Transaction.raw.prototype.getRefBlockBytes_asU8=function(){return o.Message.bytesAsU8(this.getRefBlockBytes())},TronWebProto.Transaction.raw.prototype.setRefBlockBytes=function(e){return o.Message.setProto3BytesField(this,1,e)},TronWebProto.Transaction.raw.prototype.getRefBlockNum=function(){return o.Message.getFieldWithDefault(this,3,0)},TronWebProto.Transaction.raw.prototype.setRefBlockNum=function(e){return o.Message.setProto3IntField(this,3,e)},TronWebProto.Transaction.raw.prototype.getRefBlockHash=function(){return o.Message.getFieldWithDefault(this,4,"")},TronWebProto.Transaction.raw.prototype.getRefBlockHash_asB64=function(){return o.Message.bytesAsB64(this.getRefBlockHash())},TronWebProto.Transaction.raw.prototype.getRefBlockHash_asU8=function(){return o.Message.bytesAsU8(this.getRefBlockHash())},TronWebProto.Transaction.raw.prototype.setRefBlockHash=function(e){return o.Message.setProto3BytesField(this,4,e)},TronWebProto.Transaction.raw.prototype.getExpiration=function(){return o.Message.getFieldWithDefault(this,8,0)},TronWebProto.Transaction.raw.prototype.setExpiration=function(e){return o.Message.setProto3IntField(this,8,e)},TronWebProto.Transaction.raw.prototype.getAuthsList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.authority,9)},TronWebProto.Transaction.raw.prototype.setAuthsList=function(e){return o.Message.setRepeatedWrapperField(this,9,e)},TronWebProto.Transaction.raw.prototype.addAuths=function(e,t){return o.Message.addToRepeatedWrapperField(this,9,e,TronWebProto.authority,t)},TronWebProto.Transaction.raw.prototype.clearAuthsList=function(){return this.setAuthsList([])},TronWebProto.Transaction.raw.prototype.getData=function(){return o.Message.getFieldWithDefault(this,10,"")},TronWebProto.Transaction.raw.prototype.getData_asB64=function(){return o.Message.bytesAsB64(this.getData())},TronWebProto.Transaction.raw.prototype.getData_asU8=function(){return o.Message.bytesAsU8(this.getData())},TronWebProto.Transaction.raw.prototype.setData=function(e){return o.Message.setProto3BytesField(this,10,e)},TronWebProto.Transaction.raw.prototype.getContractList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Transaction.Contract,11)},TronWebProto.Transaction.raw.prototype.setContractList=function(e){return o.Message.setRepeatedWrapperField(this,11,e)},TronWebProto.Transaction.raw.prototype.addContract=function(e,t){return o.Message.addToRepeatedWrapperField(this,11,e,TronWebProto.Transaction.Contract,t)},TronWebProto.Transaction.raw.prototype.clearContractList=function(){return this.setContractList([])},TronWebProto.Transaction.raw.prototype.getScripts=function(){return o.Message.getFieldWithDefault(this,12,"")},TronWebProto.Transaction.raw.prototype.getScripts_asB64=function(){return o.Message.bytesAsB64(this.getScripts())},TronWebProto.Transaction.raw.prototype.getScripts_asU8=function(){return o.Message.bytesAsU8(this.getScripts())},TronWebProto.Transaction.raw.prototype.setScripts=function(e){return o.Message.setProto3BytesField(this,12,e)},TronWebProto.Transaction.raw.prototype.getTimestamp=function(){return o.Message.getFieldWithDefault(this,14,0)},TronWebProto.Transaction.raw.prototype.setTimestamp=function(e){return o.Message.setProto3IntField(this,14,e)},TronWebProto.Transaction.raw.prototype.getFeeLimit=function(){return o.Message.getFieldWithDefault(this,18,0)},TronWebProto.Transaction.raw.prototype.setFeeLimit=function(e){return o.Message.setProto3IntField(this,18,e)},TronWebProto.Transaction.prototype.getRawData=function(){return o.Message.getWrapperField(this,TronWebProto.Transaction.raw,1)},TronWebProto.Transaction.prototype.setRawData=function(e){return o.Message.setWrapperField(this,1,e)},TronWebProto.Transaction.prototype.clearRawData=function(){return this.setRawData(void 0)},TronWebProto.Transaction.prototype.hasRawData=function(){return null!=o.Message.getField(this,1)},TronWebProto.Transaction.prototype.getSignatureList=function(){return o.Message.getRepeatedField(this,2)},TronWebProto.Transaction.prototype.getSignatureList_asB64=function(){return o.Message.bytesListAsB64(this.getSignatureList())},TronWebProto.Transaction.prototype.getSignatureList_asU8=function(){return o.Message.bytesListAsU8(this.getSignatureList())},TronWebProto.Transaction.prototype.setSignatureList=function(e){return o.Message.setField(this,2,e||[])},TronWebProto.Transaction.prototype.addSignature=function(e,t){return o.Message.addToRepeatedField(this,2,e,t)},TronWebProto.Transaction.prototype.clearSignatureList=function(){return this.setSignatureList([])},TronWebProto.Transaction.prototype.getRetList=function(){return o.Message.getRepeatedWrapperField(this,TronWebProto.Transaction.Result,5)},TronWebProto.Transaction.prototype.setRetList=function(e){return o.Message.setRepeatedWrapperField(this,5,e)},TronWebProto.Transaction.prototype.addRet=function(e,t){return o.Message.addToRepeatedWrapperField(this,5,e,TronWebProto.Transaction.Result,t)},TronWebProto.Transaction.prototype.clearRetList=function(){return this.setRetList([])},TronWebProto.AccountType={NORMAL:0,ASSETISSUE:1,CONTRACT:2},o.object.extend(t,TronWebProto)}}]);