"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4936],{1632:(t,e,i)=>{i.d(e,{f:()=>h});var s={px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},r=i(3208),o=(0,i(92610).tv)({base:"w-px h-px inline-block",variants:{isInline:{true:"inline-block",false:"block"}},defaultVariants:{isInline:!1}}),n=i(18884),a=i(9585),l=t=>{var e;return null!=(e=s[t])?e:t},u=i(19605),d=(0,r.Rf)((t,e)=>{let{Component:i,getSpacerProps:s}=function(t){let[e,i]=(0,r.rE)(t,o.variantKeys),{as:s,className:u,x:d=1,y:h=1,...c}=e,m=(0,a.useMemo)(()=>o({...i,className:u}),[(0,n.t6)(i),u]),p=l(d),g=l(h);return{Component:s||"span",getSpacerProps:(t={})=>({...t,...c,"aria-hidden":(0,n.sE)(!0),className:(0,n.$z)(m,t.className),style:{...t.style,...c.style,marginLeft:p,marginTop:g}})}}({...t});return(0,u.jsx)(i,{ref:e,...s()})});d.displayName="HeroUI.Spacer";var h=d},15805:(t,e,i)=>{i.d(e,{a:()=>r});var s=i(9585);function r(t={}){let{rerender:e=!1,delay:i=0}=t,o=(0,s.useRef)(!1),[n,a]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{o.current=!0;let t=null;return e&&(i>0?t=setTimeout(()=>{a(!0)},i):a(!0)),()=>{o.current=!1,e&&a(!1),t&&clearTimeout(t)}},[e]),[(0,s.useCallback)(()=>o.current,[]),n]}},55935:(t,e,i)=>{var s=i(85383);i.o(s,"useRouter")&&i.d(e,{useRouter:function(){return s.useRouter}}),i.o(s,"useSearchParams")&&i.d(e,{useSearchParams:function(){return s.useSearchParams}})},70468:(t,e,i)=>{i.d(e,{P:()=>r});var s=i(18884);function r(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return t=>{e.forEach(e=>(function(t,e){if(null!=t){if((0,s.Tn)(t))return void t(e);try{t.current=e}catch(i){throw Error("Cannot assign value '".concat(e,"' to ref '").concat(t,"'"))}}})(e,t))}}},70601:(t,e,i)=>{i.d(e,{r:()=>e2});var s=i(3208),r=i(23883),o=i(18884),n=i(96539);let a=new WeakMap;function l(t,e,i){if(!t)return"";"string"==typeof e&&(e=e.replace(/\s+/g,""));let s=a.get(t);return`${s}-${i}-${e}`}var u=i(74116),d=i(73561),h=i(7484),c=i(19605),m=(0,s.Rf)((t,e)=>{var i,s;let{as:a,tabKey:m,destroyInactiveTabPanel:p,state:g,className:f,slots:v,classNames:y,...b}=t,x=(0,r.zD)(e),{tabPanelProps:P}=function(t,e,i){var s;let r=(0,d.$)(i)?void 0:0,o=l(e,null!=(s=t.id)?s:null==e?void 0:e.selectedKey,"tabpanel"),a=(0,u.b)({...t,id:o,"aria-labelledby":l(e,null==e?void 0:e.selectedKey,"tab")});return{tabPanelProps:(0,n.v)(a,{tabIndex:r,role:"tabpanel","aria-describedby":t["aria-describedby"],"aria-details":t["aria-details"]})}}({...t,id:String(m)},g,x),{focusProps:D,isFocused:T,isFocusVisible:w}=(0,h.o)(),S=g.selectedItem,E=g.collection.getItem(m).props.children,k=(0,o.$z)(null==y?void 0:y.panel,f,null==(i=null==S?void 0:S.props)?void 0:i.className),A=m===(null==S?void 0:S.key);return E&&(A||!p)?(0,c.jsx)(a||"div",{ref:x,"data-focus":T,"data-focus-visible":w,"data-inert":A?void 0:"true",inert:(0,o.QA)(!A),...A&&(0,n.v)(P,D,b),className:null==(s=v.panel)?void 0:s.call(v,{class:k}),"data-slot":"panel",children:E}):null});m.displayName="HeroUI.TabPanel";var p=i(70468),g=i(9733),f=i(26423),v=i(84612),y=i(89826),b=i(56542),x=i(78749),P=i(36123),D=i(85823),T=i(80897),w=i(88452),S=i(16089),E=i(77104),k=i(13683),A=i(15198),R=i(97841),L=i(58356),j=i(46932),C=i(87820),M=i(82216);function V(t,e,i,s){return(0,C.k)(t,e,(0,M.F)(i),s)}var B=i(61958);function U(t){return t.max-t.min}function I(t,e,i,s=.5){t.origin=s,t.originPoint=(0,R.k)(e.min,e.max,t.origin),t.scale=U(i)/U(e),t.translate=(0,R.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function F(t,e,i,s){I(t.x,e.x,i.x,s?s.originX:void 0),I(t.y,e.y,i.y,s?s.originY:void 0)}function N(t,e,i){t.min=i.min+e.min,t.max=t.min+U(e)}function K(t,e,i){t.min=e.min-i.min,t.max=t.min+U(e)}function W(t,e,i){K(t.x,e.x,i.x),K(t.y,e.y,i.y)}var O=i(9908);function z(t){return[t("x"),t("y")]}var $=i(4815);let H=({current:t})=>t?t.ownerDocument.defaultView:null;var G=i(97133),X=i(92146),q=i(32181),Q=i(68673),_=i(61);let Y=(t,e)=>Math.abs(t-e);class Z{constructor(t,e,{transformPagePoint:i,contextWindow:s=window,dragSnapToOrigin:r=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=te(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(Y(t.x,e.x)**2+Y(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:s}=t,{timestamp:r}=A.uv;this.history.push({...s,timestamp:r});let{onStart:o,onMove:n}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),n&&n(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=J(e,this.transformPagePoint),A.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=te("pointercancel"===t.type?this.lastMoveEventInfo:J(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,o),s&&s(t,o)},!(0,q.M)(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=o,this.contextWindow=s||window;let n=J((0,M.e)(t),this.transformPagePoint),{point:a}=n,{timestamp:l}=A.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,te(n,this.history)),this.removeListeners=(0,Q.F)(V(this.contextWindow,"pointermove",this.handlePointerMove),V(this.contextWindow,"pointerup",this.handlePointerUp),V(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,A.WG)(this.updatePoint)}}function J(t,e){return e?{point:e(t.point)}:t}function tt(t,e){return{x:t.x-e.x,y:t.y-e.y}}function te({point:t},e){return{point:t,delta:tt(t,ti(e)),offset:tt(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,r=ti(t);for(;i>=0&&(s=t[i],!(r.timestamp-s.timestamp>(0,_.f)(.1)));)i--;if(!s)return{x:0,y:0};let o=(0,_.X)(r.timestamp-s.timestamp);if(0===o)return{x:0,y:0};let n={x:(r.x-s.x)/o,y:(r.y-s.y)/o};return n.x===1/0&&(n.x=0),n.y===1/0&&(n.y=0),n}(e,.1)}}function ti(t){return t[t.length-1]}var ts=i(85468),tr=i(57272);function to(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function tn(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function ta(t,e,i){return{min:tl(t,e),max:tl(t,i)}}function tl(t,e){return"number"==typeof t?t:t[e]||0}let tu=new WeakMap;class td{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,O.ge)(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new Z(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,M.e)(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:r}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(E.I[t])return null;else return E.I[t]=!0,()=>{E.I[t]=!1};return E.I.x||E.I.y?null:(E.I.x=E.I.y=!0,()=>{E.I.x=E.I.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),z(t=>{let e=this.getAxisMotionValue(t).get()||0;if(k.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=U(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&A.Gt.postRender(()=>r(t,e)),(0,X.g)(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:r,onDrag:o}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:n}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(n),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,n),this.updateAxis("y",e.point,n),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>z(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:H(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,s=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!s||!i)return;let{velocity:o}=s;this.startAnimation(o);let{onDragEnd:n}=this.getProps();n&&A.Gt.postRender(()=>n(i,s))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!th(t,s,this.currentDirection))return;let r=this.getAxisMotionValue(t),o=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,R.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,R.k)(i,t,s.max):Math.min(t,i)),t}(o,this.constraints[t],this.elastic[t])),r.set(o)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&(0,G.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:r}){return{x:to(t.x,i,r),y:to(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:ta(t,"left","right"),y:ta(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&z(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!(0,G.X)(e))return!1;let s=e.current;(0,L.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let o=(0,$.L)(s,r.root,this.visualElement.getTransformPagePoint()),n=(t=r.layout.layoutBox,{x:tn(t.x,o.x),y:tn(t.y,o.y)});if(i){let t=i((0,B.pA)(n));this.hasMutatedConstraints=!!t,t&&(n=(0,B.FY)(t))}return n}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:n}=this.getProps(),a=this.constraints||{};return Promise.all(z(n=>{if(!th(n,e,this.currentDirection))return;let l=a&&a[n]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[n]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(n,u)})).then(n)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,X.g)(this.visualElement,t),i.start((0,j.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){z(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){z(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){z(e=>{let{drag:i}=this.getProps();if(!th(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,r=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:o}=s.layout.layoutBox[e];r.set(t[e]-(0,R.k)(i,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!(0,G.X)(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};z(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=U(t),r=U(e);return r>s?i=(0,ts.q)(e.min,e.max-s,t.min):s>r&&(i=(0,ts.q)(t.min,t.max-r,e.min)),(0,tr.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),z(e=>{if(!th(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:o}=this.constraints[e];i.set((0,R.k)(r,o,s[e]))})}addListeners(){if(!this.visualElement.current)return;tu.set(this.visualElement,this);let t=V(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();(0,G.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),A.Gt.read(e);let r=(0,C.k)(window,"resize",()=>this.scalePositionWithinConstraints()),o=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(z(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),s(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:r=!1,dragElastic:o=.35,dragMomentum:n=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:r,dragElastic:o,dragMomentum:n}}}function th(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class tc extends w.X{constructor(t){super(t),this.removeGroupControls=S.l,this.removeListeners=S.l,this.controls=new td(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||S.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let tm=t=>(e,i)=>{t&&A.Gt.postRender(()=>t(e,i))};class tp extends w.X{constructor(){super(...arguments),this.removePointerDownListener=S.l}onPointerDown(t){this.session=new Z(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:H(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:tm(t),onStart:tm(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&A.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=V(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tg=i(44294),tf=i(9585),tv=i(30100),ty=i(16007),tb=i(26251);let tx={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tP(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let tD={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!k.px.test(t))return t;else t=parseFloat(t);let i=tP(t,e.target.x),s=tP(t,e.target.y);return`${i}% ${s}%`}};var tT=i(43518),tw=i(16383);let tS=!1;class tE extends tf.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;(0,tw.$)(tA),r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),tS&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),tx.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,{projection:o}=i;return o&&(o.isPresent=r,tS=!0,s||t.layoutDependency!==e||void 0===e||t.isPresent!==r?o.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?o.promote():o.relegate()||A.Gt.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tg.k.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function tk(t){let[e,i]=(0,tv.xQ)(),s=(0,tf.useContext)(ty.L);return(0,c.jsx)(tE,{...t,layoutGroup:s,switchLayoutGroup:(0,tf.useContext)(tb.N),isPresent:e,safeToRemove:i})}let tA={borderRadius:{...tD,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tD,borderTopRightRadius:tD,borderBottomLeftRadius:tD,borderBottomRightRadius:tD,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tT.f.parse(t);if(s.length>5)return t;let r=tT.f.createTransformer(t),o=+("number"!=typeof s[0]),n=i.x.scale*e.x,a=i.y.scale*e.y;s[0+o]/=n,s[1+o]/=a;let l=(0,R.k)(n,a,.5);return"number"==typeof s[2+o]&&(s[2+o]/=l),"number"==typeof s[3+o]&&(s[3+o]/=l),r(s)}}};var tR=i(58091),tL=i(68969);function tj(t){return(0,tL.G)(t)&&"ownerSVGElement"in t}var tC=i(64554),tM=i(56848),tV=i(9877),tB=i(77543),tU=i(87740),tI=i(55150),tF=i(1092),tN=i(34330);let tK=(t,e)=>t.depth-e.depth;class tW{constructor(){this.children=[],this.isDirty=!1}add(t){(0,tN.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,tN.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tK),this.isDirty=!1,this.children.forEach(t)}}var tO=i(83452),tz=i(60570);let t$=["TopLeft","TopRight","BottomLeft","BottomRight"],tH=t$.length,tG=t=>"string"==typeof t?parseFloat(t):t,tX=t=>"number"==typeof t||k.px.test(t);function tq(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let tQ=tY(0,.5,tz.yT),t_=tY(.5,.95,S.l);function tY(t,e,i){return s=>s<t?0:s>e?1:i((0,ts.q)(t,e,s))}function tZ(t,e){t.min=e.min,t.max=e.max}function tJ(t,e){tZ(t.x,e.x),tZ(t.y,e.y)}function t0(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var t1=i(15065);function t5(t,e,i,s,r){return t-=e,t=(0,t1.hq)(t,1/i,s),void 0!==r&&(t=(0,t1.hq)(t,1/r,s)),t}function t8(t,e,[i,s,r],o,n){!function(t,e=0,i=1,s=.5,r,o=t,n=t){if(k.KN.test(e)&&(e=parseFloat(e),e=(0,R.k)(n.min,n.max,e/100)-n.min),"number"!=typeof e)return;let a=(0,R.k)(o.min,o.max,s);t===o&&(a-=e),t.min=t5(t.min,e,i,a,r),t.max=t5(t.max,e,i,a,r)}(t,e[i],e[s],e[r],e.scale,o,n)}let t2=["x","scaleX","originX"],t3=["y","scaleY","originY"];function t6(t,e,i,s){t8(t.x,e,t2,i?i.x:void 0,s?s.x:void 0),t8(t.y,e,t3,i?i.y:void 0,s?s.y:void 0)}function t4(t){return 0===t.translate&&1===t.scale}function t7(t){return t4(t.x)&&t4(t.y)}function t9(t,e){return t.min===e.min&&t.max===e.max}function et(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ee(t,e){return et(t.x,e.x)&&et(t.y,e.y)}function ei(t){return U(t.x)/U(t.y)}function es(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class er{constructor(){this.members=[]}add(t){(0,tN.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,tN.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var eo=i(47732);let en={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ea=["","X","Y","Z"],el=0;function eu(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ed({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=el++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tR.Q.value&&(en.nodes=en.calculatedTargetDeltas=en.calculatedProjections=0),this.nodes.forEach(em),this.nodes.forEach(ex),this.nodes.forEach(eP),this.nodes.forEach(ep),tR.Q.addProjectionMetrics&&tR.Q.addProjectionMetrics(en)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new tW)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tU.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=tj(e)&&!(tj(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=0,r=()=>this.root.updateBlockedByResize=!1;A.Gt.read(()=>{s=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==s&&(s=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tM.k.now(),s=({timestamp:r})=>{let o=r-i;o>=250&&((0,A.WG)(s),t(o-e))};return A.Gt.setup(s,!0),()=>(0,A.WG)(s)}(r,250),tx.hasAnimatedSinceResize&&(tx.hasAnimatedSinceResize=!1,this.nodes.forEach(eb)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||r.getDefaultTransition()||ek,{onLayoutAnimationStart:n,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!ee(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,tC.r)(o,"layout"),onPlay:n,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||eb(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,A.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eD),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,tF.P)(i);if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",A.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ef);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(ev);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(ey),this.nodes.forEach(eh),this.nodes.forEach(ec)):this.nodes.forEach(ev),this.clearAllSnapshots();let t=tM.k.now();A.uv.delta=(0,tr.q)(0,1e3/60,t-A.uv.timestamp),A.uv.timestamp=t,A.uv.isProcessing=!0,A.PP.update.process(A.uv),A.PP.preRender.process(A.uv),A.PP.render.process(A.uv),A.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tg.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eg),this.sharedNodes.forEach(eT)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||U(this.snapshot.measuredBox.x)||U(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,O.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!t7(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,eo.HD)(this.latestValues)||o)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),eL((e=s).x),eL(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,O.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(eC))){let{scroll:t}=this.root;t&&((0,t1.Ql)(e.x,t.offset.x),(0,t1.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,O.ge)();if(tJ(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:o}=s;s!==this.root&&r&&o.layoutScroll&&(r.wasRoot&&tJ(e,t),(0,t1.Ql)(e.x,r.offset.x),(0,t1.Ql)(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=(0,O.ge)();tJ(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,t1.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,eo.HD)(s.latestValues)&&(0,t1.Ww)(i,s.latestValues)}return(0,eo.HD)(this.latestValues)&&(0,t1.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,O.ge)();tJ(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,eo.HD)(i.latestValues))continue;(0,eo.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,O.ge)();tJ(s,i.measurePageBox()),t6(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,eo.HD)(this.latestValues)&&t6(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==A.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=A.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,O.ge)(),this.relativeTargetOrigin=(0,O.ge)(),W(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,O.ge)(),this.targetWithTransforms=(0,O.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,n,a;this.forceRelativeParentToResolveTarget(),o=this.target,n=this.relativeTarget,a=this.relativeParent.target,N(o.x,n.x,a.x),N(o.y,n.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tJ(this.target,this.layout.layoutBox),(0,t1.o4)(this.target,this.targetDelta)):tJ(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,O.ge)(),this.relativeTargetOrigin=(0,O.ge)(),W(this.relativeTargetOrigin,this.target,t.target),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tR.Q.value&&en.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,eo.vk)(this.parent.latestValues)||(0,eo.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===A.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;tJ(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,n=this.treeScale.y;(0,t1.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,O.ge)());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(t0(this.prevProjectionDelta.x,this.projectionDelta.x),t0(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),F(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===n&&es(this.projectionDelta.x,this.prevProjectionDelta.x)&&es(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tR.Q.value&&en.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,O.xU)(),this.projectionDelta=(0,O.xU)(),this.projectionDeltaWithTransform=(0,O.xU)()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},o={...this.latestValues},n=(0,O.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=(0,O.ge)(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,h=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(eE));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(ew(n.x,t.x,s),ew(n.y,t.y,s),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,m,p,g,f;W(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,g=a,f=s,eS(m.x,p.x,g.x,f),eS(m.y,p.y,g.y,f),i&&(u=this.relativeTarget,c=i,t9(u.x,c.x)&&t9(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=(0,O.ge)()),tJ(i,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,i,s,r,o){r?(t.opacity=(0,R.k)(0,i.opacity??1,tQ(s)),t.opacityExit=(0,R.k)(e.opacity??1,0,t_(s))):o&&(t.opacity=(0,R.k)(e.opacity??1,i.opacity??1,s));for(let r=0;r<tH;r++){let o=`border${t$[r]}Radius`,n=tq(e,o),a=tq(i,o);(void 0!==n||void 0!==a)&&(n||(n=0),a||(a=0),0===n||0===a||tX(n)===tX(a)?(t[o]=Math.max((0,R.k)(tG(n),tG(a),s),0),(k.KN.test(a)||k.KN.test(n))&&(t[o]+="%")):t[o]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,R.k)(e.rotate||0,i.rotate||0,s))}(o,r,this.latestValues,s,h,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,A.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.Gt.update(()=>{tx.hasAnimatedSinceResize=!0,tV.q.layout++,this.motionValue||(this.motionValue=(0,tB.OQ)(0)),this.currentAnimation=function(t,e,i){let s=(0,tI.S)(t)?t:(0,tB.OQ)(t);return s.start((0,j.f)("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tV.q.layout--},onComplete:()=>{tV.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ej(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,O.ge)();let e=U(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=U(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}tJ(e,i),(0,t1.Ww)(e,r),F(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new er),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&eu("z",t,s,this.animationValues);for(let e=0;e<ea.length;e++)eu(`rotate${ea[e]}`,t,s,this.animationValues),eu(`skew${ea[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=(0,tO.u)(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=(0,tO.u)(e?.pointerEvents)||""),this.hasProjected&&!(0,eo.HD)(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=s.animationValues||s.latestValues;this.applyTransformsToTarget();let o=function(t,e,i){let s="",r=t.x.translate/e.x,o=t.y.translate/e.y,n=i?.z||0;if((r||o||n)&&(s=`translate3d(${r}px, ${o}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:o,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),o&&(s+=`rotateY(${o}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(o=i(r,o)),t.transform=o;let{x:n,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*n.origin}% ${100*a.origin}% 0`,s.animationValues?t.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,tw.H){if(void 0===r[e])continue;let{correct:i,applyTo:n,isCSSVariable:a}=tw.H[e],l="none"===o?r[e]:i(r[e],s);if(n){let e=n.length;for(let i=0;i<e;i++)t[n[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=s===this?(0,tO.u)(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(ef),this.root.sharedNodes.clear()}}}function eh(t){t.updateLayout()}function ec(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:r}=t.options,o=e.source!==t.layout.source;"size"===r?z(t=>{let s=o?e.measuredBox[t]:e.layoutBox[t],r=U(s);s.min=i[t].min,s.max=s.min+r}):ej(r,e.layoutBox,i)&&z(s=>{let r=o?e.measuredBox[s]:e.layoutBox[s],n=U(i[s]);r.max=r.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,O.xU)();F(n,i,e.layoutBox);let a=(0,O.xU)();o?F(a,t.applyTransform(s,!0),e.measuredBox):F(a,i,e.layoutBox);let l=!t7(n),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:o}=s;if(r&&o){let n=(0,O.ge)();W(n,e.layoutBox,r.layoutBox);let a=(0,O.ge)();W(a,i,o.layoutBox),ee(n,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function em(t){tR.Q.value&&en.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ep(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function eg(t){t.clearSnapshot()}function ef(t){t.clearMeasurements()}function ev(t){t.isLayoutDirty=!1}function ey(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function eb(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ex(t){t.resolveTargetDelta()}function eP(t){t.calcProjection()}function eD(t){t.resetSkewAndRotation()}function eT(t){t.removeLeadSnapshot()}function ew(t,e,i){t.translate=(0,R.k)(e.translate,0,i),t.scale=(0,R.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function eS(t,e,i,s){t.min=(0,R.k)(e.min,i.min,s),t.max=(0,R.k)(e.max,i.max,s)}function eE(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let ek={duration:.45,ease:[.4,0,.1,1]},eA=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),eR=eA("applewebkit/")&&!eA("chrome/")?Math.round:S.l;function eL(t){t.min=eR(t.min),t.max=eR(t.max)}function ej(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ei(e)-ei(i)))}function eC(t){return t!==t.root&&t.scroll?.wasRoot}let eM=ed({attachResizeListener:(t,e)=>(0,C.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eV={current:void 0},eB=ed({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!eV.current){let t=new eM({});t.mount(window),t.setOptions({layoutScroll:!0}),eV.current=t}return eV.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),eU={...i(89586).l,pan:{Feature:tp},drag:{Feature:tc,ProjectionNode:eB,MeasureLayout:tk},layout:{ProjectionNode:eB,MeasureLayout:tk}};var eI=i(42620),eF=i(15805),eN=(0,s.Rf)((t,e)=>{var i;let{className:s,as:a,item:u,state:d,classNames:m,isDisabled:w,listRef:S,slots:E,motionProps:k,disableAnimation:A,disableCursorAnimation:R,shouldSelectOnPressUp:L,onClick:j,tabRef:C,...M}=t,{key:V}=u,B=(0,r.zD)(e),U=a||(t.href?"a":"button"),{tabProps:I,isSelected:F,isDisabled:N,isPressed:K}=function(t,e,i){let{key:s,isDisabled:r,shouldSelectOnPressUp:o}=t,{selectionManager:a,selectedKey:u}=e,d=s===u,h=r||e.isDisabled||e.selectionManager.isDisabled(s),{itemProps:c,isPressed:m}=(0,P.p)({selectionManager:a,key:s,ref:i,isDisabled:h,shouldSelectOnPressUp:o,linkBehavior:"selection"}),p=l(e,s,"tab"),g=l(e,s,"tabpanel"),{tabIndex:f}=c,v=e.collection.getItem(s),D=(0,y.$)(null==v?void 0:v.props,{labelable:!0});delete D.id;let T=(0,b._h)(null==v?void 0:v.props),{focusableProps:w}=(0,x.Wc)({isDisabled:h},i);return{tabProps:(0,n.v)(D,w,T,c,{id:p,"aria-selected":d,"aria-disabled":h||void 0,"aria-controls":d?g:void 0,tabIndex:h?void 0:f,role:"tab"}),isSelected:d,isDisabled:h,isPressed:m}}({key:V,isDisabled:w,shouldSelectOnPressUp:L},d,B);null==t.children&&delete I["aria-controls"];let W=w||N,{focusProps:O,isFocused:z,isFocusVisible:$}=(0,h.o)(),{hoverProps:H,isHovered:G}=(0,D.M)({isDisabled:W}),X=(0,o.$z)(null==m?void 0:m.tab,s),[,q]=(0,eF.a)({rerender:!0});return(0,c.jsxs)(U,{ref:(0,p.P)(B,C),"data-disabled":(0,o.sE)(N),"data-focus":(0,o.sE)(z),"data-focus-visible":(0,o.sE)($),"data-hover":(0,o.sE)(G),"data-hover-unselected":(0,o.sE)((G||K)&&!F),"data-pressed":(0,o.sE)(K),"data-selected":(0,o.sE)(F),"data-slot":"tab",...(0,n.v)(I,!W?{...O,...H}:{},(0,g.$)(M,{enabled:"string"==typeof U,omitPropNames:new Set(["title"])}),{onClick:(0,f.c)(()=>{(null==B?void 0:B.current)&&(null==S?void 0:S.current)&&(0,v.A)(B.current,{scrollMode:"if-needed",behavior:"smooth",block:"end",inline:"end",boundary:null==S?void 0:S.current})},j,I.onClick)}),className:null==(i=E.tab)?void 0:i.call(E,{class:X}),title:null==M?void 0:M.titleValue,type:"button"===U?"button":void 0,children:[F&&!A&&!R&&q?(0,c.jsx)(T.F,{features:eU,children:(0,c.jsx)(eI.m.span,{className:E.cursor({class:null==m?void 0:m.cursor}),"data-slot":"cursor",layoutDependency:!1,layoutId:"cursor",transition:{type:"spring",bounce:.15,duration:.5},...k})}):null,(0,c.jsx)("div",{className:E.tabContent({class:null==m?void 0:m.tabContent}),"data-slot":"tabContent",children:u.rendered})]})});eN.displayName="HeroUI.Tab";var eK=i(31081),eW=i(18887),eO=i(92610),ez=i(56457),e$=(0,eO.tv)({slots:{base:"inline-flex",tabList:["flex","p-1","h-fit","gap-2","items-center","flex-nowrap","overflow-x-scroll","scrollbar-hide","bg-default-100"],tab:["z-0","w-full","px-3","py-1","flex","group","relative","justify-center","items-center","outline-none","cursor-pointer","transition-opacity","tap-highlight-transparent","data-[disabled=true]:cursor-not-allowed","data-[disabled=true]:opacity-30","data-[hover-unselected=true]:opacity-disabled",...ez.zb],tabContent:["relative","z-10","text-inherit","whitespace-nowrap","transition-colors","text-default-500","group-data-[selected=true]:text-foreground"],cursor:["absolute","z-0","bg-white"],panel:["py-3","px-1","outline-none","data-[inert=true]:hidden",...ez.zb],tabWrapper:[]},variants:{variant:{solid:{cursor:"inset-0"},light:{tabList:"bg-transparent dark:bg-transparent",cursor:"inset-0"},underlined:{tabList:"bg-transparent dark:bg-transparent",cursor:"h-[2px] w-[80%] bottom-0 shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]"},bordered:{tabList:"bg-transparent dark:bg-transparent border-medium border-default-200 shadow-sm",cursor:"inset-0"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},size:{sm:{tabList:"rounded-medium",tab:"h-7 text-tiny rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"h-8 text-small rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"h-9 text-medium rounded-medium",cursor:"rounded-medium"}},radius:{none:{tabList:"rounded-none",tab:"rounded-none",cursor:"rounded-none"},sm:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},md:{tabList:"rounded-medium",tab:"rounded-small",cursor:"rounded-small"},lg:{tabList:"rounded-large",tab:"rounded-medium",cursor:"rounded-medium"},full:{tabList:"rounded-full",tab:"rounded-full",cursor:"rounded-full"}},fullWidth:{true:{base:"w-full",tabList:"w-full"}},isDisabled:{true:{tabList:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{tab:"transition-none",tabContent:"transition-none"}},placement:{top:{},start:{tabList:"flex-col",panel:"py-0 px-3",tabWrapper:"flex"},end:{tabList:"flex-col",panel:"py-0 px-3",tabWrapper:"flex flex-row-reverse"},bottom:{tabWrapper:"flex flex-col-reverse"}}},defaultVariants:{color:"default",variant:"solid",size:"md",fullWidth:!1,isDisabled:!1},compoundVariants:[{variant:["solid","bordered","light"],color:"default",class:{cursor:["bg-background","dark:bg-default","shadow-small"],tabContent:"group-data-[selected=true]:text-default-foreground"}},{variant:["solid","bordered","light"],color:"primary",class:{cursor:eW.k.solid.primary,tabContent:"group-data-[selected=true]:text-primary-foreground"}},{variant:["solid","bordered","light"],color:"secondary",class:{cursor:eW.k.solid.secondary,tabContent:"group-data-[selected=true]:text-secondary-foreground"}},{variant:["solid","bordered","light"],color:"success",class:{cursor:eW.k.solid.success,tabContent:"group-data-[selected=true]:text-success-foreground"}},{variant:["solid","bordered","light"],color:"warning",class:{cursor:eW.k.solid.warning,tabContent:"group-data-[selected=true]:text-warning-foreground"}},{variant:["solid","bordered","light"],color:"danger",class:{cursor:eW.k.solid.danger,tabContent:"group-data-[selected=true]:text-danger-foreground"}},{variant:"underlined",color:"default",class:{cursor:"bg-foreground",tabContent:"group-data-[selected=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{cursor:"bg-primary",tabContent:"group-data-[selected=true]:text-primary"}},{variant:"underlined",color:"secondary",class:{cursor:"bg-secondary",tabContent:"group-data-[selected=true]:text-secondary"}},{variant:"underlined",color:"success",class:{cursor:"bg-success",tabContent:"group-data-[selected=true]:text-success"}},{variant:"underlined",color:"warning",class:{cursor:"bg-warning",tabContent:"group-data-[selected=true]:text-warning"}},{variant:"underlined",color:"danger",class:{cursor:"bg-danger",tabContent:"group-data-[selected=true]:text-danger"}},{disableAnimation:!0,variant:"underlined",class:{tab:["after:content-['']","after:absolute","after:bottom-0","after:h-[2px]","after:w-[80%]","after:opacity-0","after:shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","data-[selected=true]:after:opacity-100"]}},{disableAnimation:!0,color:"default",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-default data-[selected=true]:text-default-foreground"}},{disableAnimation:!0,color:"primary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-primary data-[selected=true]:text-primary-foreground"}},{disableAnimation:!0,color:"secondary",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-secondary data-[selected=true]:text-secondary-foreground"}},{disableAnimation:!0,color:"success",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-success data-[selected=true]:text-success-foreground"}},{disableAnimation:!0,color:"warning",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-warning data-[selected=true]:text-warning-foreground"}},{disableAnimation:!0,color:"danger",variant:["solid","bordered","light"],class:{tab:"data-[selected=true]:bg-danger data-[selected=true]:text-danger-foreground"}},{disableAnimation:!0,color:"default",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-foreground"}},{disableAnimation:!0,color:"primary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-primary"}},{disableAnimation:!0,color:"secondary",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-secondary"}},{disableAnimation:!0,color:"success",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-success"}},{disableAnimation:!0,color:"warning",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-warning"}},{disableAnimation:!0,color:"danger",variant:"underlined",class:{tab:"data-[selected=true]:after:bg-danger"}}],compoundSlots:[{variant:"underlined",slots:["tab","tabList","cursor"],class:["rounded-none"]}]}),eH=i(69036),eG=i(47314);function eX(t,e){let i=null;if(t){var s,r,o,n;for(i=t.getFirstKey();null!=i&&(e.has(i)||(null==(r=t.getItem(i))||null==(s=r.props)?void 0:s.isDisabled))&&i!==t.getLastKey();)i=t.getKeyAfter(i);null!=i&&(e.has(i)||(null==(n=t.getItem(i))||null==(o=n.props)?void 0:o.isDisabled))&&i===t.getLastKey()&&(i=t.getFirstKey())}return i}class eq{getKeyLeftOf(t){return this.flipDirection?this.getNextKey(t):this.getPreviousKey(t)}getKeyRightOf(t){return this.flipDirection?this.getPreviousKey(t):this.getNextKey(t)}isDisabled(t){var e,i;return this.disabledKeys.has(t)||!!(null==(i=this.collection.getItem(t))||null==(e=i.props)?void 0:e.isDisabled)}getFirstKey(){let t=this.collection.getFirstKey();return null!=t&&this.isDisabled(t)&&(t=this.getNextKey(t)),t}getLastKey(){let t=this.collection.getLastKey();return null!=t&&this.isDisabled(t)&&(t=this.getPreviousKey(t)),t}getKeyAbove(t){return this.tabDirection?null:this.getPreviousKey(t)}getKeyBelow(t){return this.tabDirection?null:this.getNextKey(t)}getNextKey(t){let e=t;do null==(e=this.collection.getKeyAfter(e))&&(e=this.collection.getFirstKey());while(null!=e&&this.isDisabled(e));return e}getPreviousKey(t){let e=t;do null==(e=this.collection.getKeyBefore(e))&&(e=this.collection.getLastKey());while(null!=e&&this.isDisabled(e));return e}constructor(t,e,i,s=new Set){this.collection=t,this.flipDirection="rtl"===e&&"horizontal"===i,this.disabledKeys=s,this.tabDirection="horizontal"===i}}var eQ=i(24215),e_=i(3351),eY=i(13451);let eZ=(0,tf.createContext)(null);var eJ=i(36196);let e0=t=>!t.isLayoutDirty&&t.willUpdate(!1),e1=t=>!0===t,e5=t=>e1(!0===t)||"id"===t,e8=t=>{let{children:e,id:i,inherit:s=!0}=t,r=(0,tf.useContext)(ty.L),o=(0,tf.useContext)(eZ),[n,a]=function(){let t=function(){let t=(0,tf.useRef)(!1);return(0,eJ.E)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}(),[e,i]=(0,tf.useState)(0),s=(0,tf.useCallback)(()=>{t.current&&i(e+1)},[e]);return[(0,tf.useCallback)(()=>A.Gt.postRender(s),[s]),e]}(),l=(0,tf.useRef)(null),u=r.id||o;null===l.current&&(e5(s)&&u&&(i=i?u+"-"+i:u),l.current={id:i,group:e1(s)&&r.group||function(){let t=new Set,e=new WeakMap,i=()=>t.forEach(e0);return{add:s=>{t.add(s),e.set(s,s.addEventListener("willUpdate",i))},remove:s=>{t.delete(s);let r=e.get(s);r&&(r(),e.delete(s)),i()},dirty:i}}()});let d=(0,tf.useMemo)(()=>({...l.current,forceRender:n}),[a]);return(0,c.jsx)(ty.L.Provider,{value:d,children:e})};var e2=(0,s.Rf)(function(t,e){let{Component:i,values:l,state:d,destroyInactiveTabPanel:h,getBaseProps:p,getTabListProps:f,getWrapperProps:v}=function(t){var e,i,l;let d=(0,eK.o)(),[h,c]=(0,s.rE)(t,e$.variantKeys),{ref:m,as:p,className:f,classNames:v,children:y,disableCursorAnimation:b,motionProps:x,isVertical:P=!1,shouldSelectOnPressUp:D=!0,destroyInactiveTabPanel:T=!0,...w}=h,S=p||"div",E="string"==typeof S,k=(0,r.zD)(m),A=null!=(i=null!=(e=null==t?void 0:t.disableAnimation)?e:null==d?void 0:d.disableAnimation)&&i,R=function(t){var e,i;let s=function(t){var e;let[i,s]=(0,eG.P)(t.selectedKey,null!=(e=t.defaultSelectedKey)?e:null,t.onSelectionChange),r=(0,tf.useMemo)(()=>null!=i?[i]:[],[i]),{collection:o,disabledKeys:n,selectionManager:a}=(0,eH.p)({...t,selectionMode:"single",disallowEmptySelection:!0,allowDuplicateSelectionEvents:!0,selectedKeys:r,onSelectionChange:e=>{var r;if("all"===e)return;let o=null!=(r=e.values().next().value)?r:null;o===i&&t.onSelectionChange&&t.onSelectionChange(o),s(o)}}),l=null!=i?o.getItem(i):null;return{collection:o,disabledKeys:n,selectionManager:a,selectedKey:i,setSelectedKey:s,selectedItem:l}}({...t,onSelectionChange:t.onSelectionChange?e=>{var i;null!=e&&(null==(i=t.onSelectionChange)||i.call(t,e))}:void 0,suppressTextValueWarning:!0,defaultSelectedKey:null!=(i=null!=(e=t.defaultSelectedKey)?e:eX(t.collection,t.disabledKeys?new Set(t.disabledKeys):new Set))?i:void 0}),{selectionManager:r,collection:o,selectedKey:n}=s,a=(0,tf.useRef)(n);return(0,tf.useEffect)(()=>{let e=n;null==t.selectedKey&&(r.isEmpty||null==e||!o.getItem(e))&&null!=(e=eX(o,s.disabledKeys))&&r.setSelectedKeys([e]),(null==e||null!=r.focusedKey)&&(r.isFocused||e===a.current)||r.setFocusedKey(e),a.current=e}),{...s,isDisabled:t.isDisabled||!1}}({children:y,...w}),{tabListProps:L}=function(t,e,i){let{orientation:s="horizontal",keyboardActivation:r="automatic"}=t,{collection:o,selectionManager:l,disabledKeys:d}=e,{direction:h}=(0,e_.Y)(),c=(0,tf.useMemo)(()=>new eq(o,h,s,d),[o,d,s,h]),{collectionProps:m}=(0,eY.y)({ref:i,selectionManager:l,keyboardDelegate:c,selectOnFocus:"automatic"===r,disallowEmptySelection:!0,scrollRef:i,linkBehavior:"selection"}),p=(0,eQ.Bi)();a.set(e,p);let g=(0,u.b)({...t,id:p});return{tabListProps:{...(0,n.v)(m,g),role:"tablist","aria-orientation":s,tabIndex:void 0}}}(w,R,k),j=(0,tf.useMemo)(()=>e$({...c,disableAnimation:A,...P?{placement:"start"}:{}}),[(0,o.t6)(c),A,P]),C=(0,o.$z)(null==v?void 0:v.base,f),M=(0,tf.useMemo)(()=>({state:R,slots:j,classNames:v,motionProps:x,disableAnimation:A,listRef:k,shouldSelectOnPressUp:D,disableCursorAnimation:b,isDisabled:null==t?void 0:t.isDisabled}),[R,j,k,x,A,b,D,null==t?void 0:t.isDisabled,v]),V=(0,tf.useCallback)(t=>({"data-slot":"base",className:j.base({class:(0,o.$z)(C,null==t?void 0:t.className)}),...(0,n.v)((0,g.$)(w,{enabled:E}),t)}),[C,w,j]),B=null!=(l=c.placement)?l:P?"start":"top",U=(0,tf.useCallback)(t=>({"data-slot":"tabWrapper",className:j.tabWrapper({class:(0,o.$z)(null==v?void 0:v.tabWrapper,null==t?void 0:t.className)}),"data-placement":B,"data-vertical":P||"start"===B||"end"===B?"vertical":"horizontal"}),[v,j,B,P]),I=(0,tf.useCallback)(t=>({ref:k,"data-slot":"tabList",className:j.tabList({class:(0,o.$z)(null==v?void 0:v.tabList,null==t?void 0:t.className)}),...(0,n.v)(L,t)}),[k,L,v,j]);return{Component:S,domRef:k,state:R,values:M,destroyInactiveTabPanel:T,getBaseProps:V,getTabListProps:I,getWrapperProps:U}}({...t,ref:e}),y=(0,tf.useId)(),b=!t.disableAnimation&&!t.disableCursorAnimation,x={state:d,listRef:l.listRef,slots:l.slots,classNames:l.classNames,isDisabled:l.isDisabled,motionProps:l.motionProps,disableAnimation:l.disableAnimation,shouldSelectOnPressUp:l.shouldSelectOnPressUp,disableCursorAnimation:l.disableCursorAnimation},P=[...d.collection].map(t=>(0,c.jsx)(eN,{item:t,...x,...t.props},t.key)),D=(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{...p(),children:(0,c.jsx)(i,{...f(),children:b?(0,c.jsx)(e8,{id:y,children:P}):P})}),[...d.collection].map(t=>(0,c.jsx)(m,{classNames:l.classNames,destroyInactiveTabPanel:h,slots:l.slots,state:l.state,tabKey:t.key},t.key))]});return"placement"in t||"isVertical"in t?(0,c.jsx)("div",{...v(),children:D}):D})},71413:(t,e,i)=>{i.d(e,{i:()=>s});var s=i(53837).q},73561:(t,e,i)=>{i.d(e,{$:()=>n});var s=i(72625),r=i(73360),o=i(9585);function n(t,e){let i=null==e?void 0:e.isDisabled,[n,a]=(0,o.useState)(!1);return(0,r.N)(()=>{if((null==t?void 0:t.current)&&!i){let e=()=>{t.current&&a(!!(0,s.N$)(t.current,{tabbable:!0}).nextNode())};e();let i=new MutationObserver(e);return i.observe(t.current,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["tabIndex","disabled"]}),()=>{i.disconnect()}}}),!i&&n}},84612:(t,e,i)=>{i.d(e,{A:()=>d});let s=t=>"object"==typeof t&&null!=t&&1===t.nodeType,r=(t,e)=>(!e||"hidden"!==t)&&"visible"!==t&&"clip"!==t,o=(t,e)=>{if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){let i=getComputedStyle(t,null);return r(i.overflowY,e)||r(i.overflowX,e)||(t=>{let e=(t=>{if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch(t){return null}})(t);return!!e&&(e.clientHeight<t.scrollHeight||e.clientWidth<t.scrollWidth)})(t)}return!1},n=(t,e,i,s,r,o,n,a)=>o<t&&n>e||o>t&&n<e?0:o<=t&&a<=i||n>=e&&a>=i?o-t-s:n>e&&a<i||o<t&&a>i?n-e+r:0,a=t=>{let e=t.parentElement;return null==e?t.getRootNode().host||null:e},l=(t,e)=>{var i,r,l,u;if("undefined"==typeof document)return[];let{scrollMode:d,block:h,inline:c,boundary:m,skipOverflowHiddenElements:p}=e,g="function"==typeof m?m:t=>t!==m;if(!s(t))throw TypeError("Invalid target");let f=document.scrollingElement||document.documentElement,v=[],y=t;for(;s(y)&&g(y);){if((y=a(y))===f){v.push(y);break}null!=y&&y===document.body&&o(y)&&!o(document.documentElement)||null!=y&&o(y,p)&&v.push(y)}let b=null!=(r=null==(i=window.visualViewport)?void 0:i.width)?r:innerWidth,x=null!=(u=null==(l=window.visualViewport)?void 0:l.height)?u:innerHeight,{scrollX:P,scrollY:D}=window,{height:T,width:w,top:S,right:E,bottom:k,left:A}=t.getBoundingClientRect(),{top:R,right:L,bottom:j,left:C}=(t=>{let e=window.getComputedStyle(t);return{top:parseFloat(e.scrollMarginTop)||0,right:parseFloat(e.scrollMarginRight)||0,bottom:parseFloat(e.scrollMarginBottom)||0,left:parseFloat(e.scrollMarginLeft)||0}})(t),M="start"===h||"nearest"===h?S-R:"end"===h?k+j:S+T/2-R+j,V="center"===c?A+w/2-C+L:"end"===c?E+L:A-C,B=[];for(let t=0;t<v.length;t++){let e=v[t],{height:i,width:s,top:r,right:a,bottom:l,left:u}=e.getBoundingClientRect();if("if-needed"===d&&S>=0&&A>=0&&k<=x&&E<=b&&(e===f&&!o(e)||S>=r&&k<=l&&A>=u&&E<=a))break;let m=getComputedStyle(e),p=parseInt(m.borderLeftWidth,10),g=parseInt(m.borderTopWidth,10),y=parseInt(m.borderRightWidth,10),R=parseInt(m.borderBottomWidth,10),L=0,j=0,C="offsetWidth"in e?e.offsetWidth-e.clientWidth-p-y:0,U="offsetHeight"in e?e.offsetHeight-e.clientHeight-g-R:0,I="offsetWidth"in e?0===e.offsetWidth?0:s/e.offsetWidth:0,F="offsetHeight"in e?0===e.offsetHeight?0:i/e.offsetHeight:0;if(f===e)L="start"===h?M:"end"===h?M-x:"nearest"===h?n(D,D+x,x,g,R,D+M,D+M+T,T):M-x/2,j="start"===c?V:"center"===c?V-b/2:"end"===c?V-b:n(P,P+b,b,p,y,P+V,P+V+w,w),L=Math.max(0,L+D),j=Math.max(0,j+P);else{L="start"===h?M-r-g:"end"===h?M-l+R+U:"nearest"===h?n(r,l,i,g,R+U,M,M+T,T):M-(r+i/2)+U/2,j="start"===c?V-u-p:"center"===c?V-(u+s/2)+C/2:"end"===c?V-a+y+C:n(u,a,s,p,y+C,V,V+w,w);let{scrollLeft:t,scrollTop:o}=e;L=0===F?0:Math.max(0,Math.min(o+L/F,e.scrollHeight-i/F+U)),j=0===I?0:Math.max(0,Math.min(t+j/I,e.scrollWidth-s/I+C)),M+=o-L,V+=t-j}B.push({el:e,top:L,left:j})}return B},u=t=>!1===t?{block:"end",inline:"nearest"}:(t=>t===Object(t)&&0!==Object.keys(t).length)(t)?t:{block:"start",inline:"nearest"};function d(t,e){if(!t.isConnected||!(t=>{let e=t;for(;e&&e.parentNode;){if(e.parentNode===document)return!0;e=e.parentNode instanceof ShadowRoot?e.parentNode.host:e.parentNode}return!1})(t))return;if("object"==typeof e&&"function"==typeof e.behavior)return e.behavior(l(t,e));let i="boolean"==typeof e||null==e?void 0:e.behavior;for(let{el:s,top:r,left:o}of l(t,u(e)))s.scroll({top:r,left:o,behavior:i})}},94661:(t,e,i)=>{i.d(e,{q:()=>u});var s=i(10001),r=i(3208),o=i(23883),n=i(18884),a=i(19605),l=(0,r.Rf)((t,e)=>{let{as:i,children:r,className:l,...u}=t,{slots:d,classNames:h}=(0,s.k)(),c=(0,o.zD)(e);return(0,a.jsx)(i||"footer",{ref:c,className:d.footer({class:(0,n.$z)(null==h?void 0:h.footer,l)}),...u,children:r})});l.displayName="HeroUI.ModalFooter";var u=l}}]);