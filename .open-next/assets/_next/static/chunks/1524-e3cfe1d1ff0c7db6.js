"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1524],{11524:(e,a,r)=>{r.d(a,{A:()=>E});var i=r(19605);function t(e){let{isSelected:a,disableAnimation:r,...t}=e;return(0,i.jsx)("svg",{"aria-hidden":"true",fill:"none",role:"presentation",stroke:"currentColor",strokeDasharray:22,strokeDashoffset:a?44:66,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,style:!r&&a?{transition:"stroke-dashoffset 250ms linear 0.2s"}:{},viewBox:"0 0 17 18",...t,children:(0,i.jsx)("polyline",{points:"1 9 7 14 15 4"})})}function l(e){let{isSelected:a,disableAnimation:r,...t}=e;return(0,i.jsx)("svg",{stroke:"currentColor",strokeWidth:3,viewBox:"0 0 24 24",...t,children:(0,i.jsx)("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function n(e){let{isIndeterminate:a,...r}=e;return(0,i.jsx)(a?l:t,{...r})}var[o,d]=(0,r(71242).q)({name:"CheckboxGroupContext",strict:!1}),s=r(31081),u=r(9585),c=r(89121),f=r(92610),b=r(56457),p=(0,f.tv)({slots:{base:"group relative max-w-fit inline-flex items-center justify-start cursor-pointer tap-highlight-transparent p-2 -m-2 select-none",wrapper:["relative","inline-flex","items-center","justify-center","flex-shrink-0","overflow-hidden","before:content-['']","before:absolute","before:inset-0","before:border-solid","before:border-2","before:box-border","before:border-default","after:content-['']","after:absolute","after:inset-0","after:scale-50","after:opacity-0","after:origin-center","group-data-[selected=true]:after:scale-100","group-data-[selected=true]:after:opacity-100","group-data-[hover=true]:before:bg-default-100",...b.wA],hiddenInput:b.n3,icon:"z-10 w-4 h-3 opacity-0 group-data-[selected=true]:opacity-100 pointer-events-none",label:"relative text-foreground select-none"},variants:{color:{default:{wrapper:"after:bg-default after:text-default-foreground text-default-foreground"},primary:{wrapper:"after:bg-primary after:text-primary-foreground text-primary-foreground"},secondary:{wrapper:"after:bg-secondary after:text-secondary-foreground text-secondary-foreground"},success:{wrapper:"after:bg-success after:text-success-foreground text-success-foreground"},warning:{wrapper:"after:bg-warning after:text-warning-foreground text-warning-foreground"},danger:{wrapper:"after:bg-danger after:text-danger-foreground text-danger-foreground"}},size:{sm:{wrapper:["w-4 h-4 me-2","rounded-[calc(theme(borderRadius.medium)*0.5)]","before:rounded-[calc(theme(borderRadius.medium)*0.5)]","after:rounded-[calc(theme(borderRadius.medium)*0.5)]"],label:"text-small",icon:"w-3 h-2"},md:{wrapper:["w-5 h-5 me-2","rounded-[calc(theme(borderRadius.medium)*0.6)]","before:rounded-[calc(theme(borderRadius.medium)*0.6)]","after:rounded-[calc(theme(borderRadius.medium)*0.6)]"],label:"text-medium",icon:"w-4 h-3"},lg:{wrapper:["w-6 h-6 me-2","rounded-[calc(theme(borderRadius.medium)*0.7)]","before:rounded-[calc(theme(borderRadius.medium)*0.7)]","after:rounded-[calc(theme(borderRadius.medium)*0.7)]"],label:"text-large",icon:"w-5 h-4"}},radius:{none:{wrapper:"rounded-none before:rounded-none after:rounded-none"},sm:{wrapper:["rounded-[calc(theme(borderRadius.medium)*0.5)]","before:rounded-[calc(theme(borderRadius.medium)*0.5)]","after:rounded-[calc(theme(borderRadius.medium)*0.5)]"]},md:{wrapper:["rounded-[calc(theme(borderRadius.medium)*0.6)]","before:rounded-[calc(theme(borderRadius.medium)*0.6)]","after:rounded-[calc(theme(borderRadius.medium)*0.6)]"]},lg:{wrapper:["rounded-[calc(theme(borderRadius.medium)*0.7)]","before:rounded-[calc(theme(borderRadius.medium)*0.7)]","after:rounded-[calc(theme(borderRadius.medium)*0.7)]"]},full:{wrapper:"rounded-full before:rounded-full after:rounded-full"}},lineThrough:{true:{label:["inline-flex","items-center","justify-center","before:content-['']","before:absolute","before:bg-foreground","before:w-0","before:h-0.5","group-data-[selected=true]:opacity-60","group-data-[selected=true]:before:w-full"]}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},isInvalid:{true:{wrapper:"before:border-danger",label:"text-danger"}},disableAnimation:{true:{wrapper:"transition-none",icon:"transition-none",label:"transition-none"},false:{wrapper:["before:transition-colors","group-data-[pressed=true]:scale-95","transition-transform","after:transition-transform-opacity","after:!ease-linear","after:!duration-200","motion-reduce:transition-none"],icon:"transition-opacity motion-reduce:transition-none",label:"transition-colors-opacity before:transition-width motion-reduce:transition-none"}}},defaultVariants:{color:"primary",size:"md",isDisabled:!1,lineThrough:!1}});(0,f.tv)({slots:{base:"relative flex flex-col gap-2",label:"relative text-medium text-foreground-500",wrapper:"flex flex-col flex-wrap gap-2 data-[orientation=horizontal]:flex-row",description:"text-small text-foreground-400",errorMessage:"text-small text-danger"},variants:{isRequired:{true:{label:"after:content-['*'] after:text-danger after:ml-0.5"}},isInvalid:{true:{description:"text-danger"}},disableAnimation:{true:{},false:{description:"transition-colors !duration-150 motion-reduce:transition-none"}}},defaultVariants:{isInvalid:!1,isRequired:!1}});var m=r(12495),v=r(85823),h=r(7484),g=r(26423),x=r(96539),y=r(18884),w=r(83421),R=r(97429),k=r(60144),C=r(24750);function I(e,a,r){let i=(0,w.KZ)({...e,value:a.isSelected}),{isInvalid:t,validationErrors:l,validationDetails:n}=i.displayValidation,{labelProps:o,inputProps:d,isSelected:s,isPressed:c,isDisabled:f,isReadOnly:b}=(0,C.e)({...e,isInvalid:t},a,r);(0,R.X)(e,i,r);let{isIndeterminate:p,isRequired:m,validationBehavior:v="aria"}=e;(0,u.useEffect)(()=>{r.current&&(r.current.indeterminate=!!p)});let{pressProps:h}=(0,k.d)({isDisabled:f||b,onPress(){let{[w.Lf]:a}=e,{commitValidation:r}=a||i;r()}});return{labelProps:(0,x.v)(o,h),inputProps:{...d,checked:s,"aria-required":m&&"aria"===v||void 0,required:m&&"native"===v},isSelected:s,isPressed:c,isDisabled:f,isReadOnly:b,isInvalid:t,validationErrors:l,validationDetails:n}}let D=new WeakMap;var S=r(70468),P=r(6576),V=r(88373),j=(0,r(3208).Rf)((e,a)=>{let{Component:r,children:t,icon:l=(0,i.jsx)(n,{}),getBaseProps:o,getWrapperProps:f,getInputProps:b,getIconProps:R,getLabelProps:k}=function(){var e,a,r,i,t,l,n,o;let f=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},b=(0,s.o)(),R=d(),{validationBehavior:k}=(0,P.CC)(V.c)||{},C=!!R,{as:j,ref:E,value:O="",children:B,icon:q,name:N,isRequired:z,isReadOnly:A=!1,autoFocus:M=!1,isSelected:G,size:T=null!=(e=null==R?void 0:R.size)?e:"md",color:U=null!=(a=null==R?void 0:R.color)?a:"primary",radius:H=null==R?void 0:R.radius,lineThrough:L=null!=(r=null==R?void 0:R.lineThrough)&&r,isDisabled:W=null!=(i=null==R?void 0:R.isDisabled)&&i,disableAnimation:$=null!=(l=null!=(t=null==R?void 0:R.disableAnimation)?t:null==b?void 0:b.disableAnimation)&&l,validationState:_,isInvalid:F=_?"invalid"===_:null!=(n=null==R?void 0:R.isInvalid)&&n,isIndeterminate:K=!1,validationBehavior:Z=C?R.validationBehavior:null!=(o=null!=k?k:null==b?void 0:b.validationBehavior)?o:"native",defaultSelected:X,classNames:Y,className:J,onValueChange:Q,validate:ee,...ea}=f;R&&y.gt&&(G&&(0,y.R8)("The Checkbox.Group is being used, `isSelected` will be ignored. Use the `value` of the Checkbox.Group instead.","Checkbox"),X&&(0,y.R8)("The Checkbox.Group is being used, `defaultSelected` will be ignored. Use the `defaultValue` of the Checkbox.Group instead.","Checkbox"));let er=(0,u.useRef)(null),ei=(0,u.useRef)(null),et=f.onChange;C&&(et=(0,g.c)(()=>{R.groupState.resetValidation()},et));let el=(0,u.useId)(),en=(0,u.useMemo)(()=>({name:N,value:O,children:B,autoFocus:M,defaultSelected:X,isIndeterminate:K,isRequired:z,isInvalid:F,isSelected:G,isDisabled:W,isReadOnly:A,"aria-label":(0,y.j1)(ea["aria-label"],B),"aria-labelledby":ea["aria-labelledby"]||el,onChange:Q}),[N,O,B,M,X,K,z,F,G,W,A,ea["aria-label"],ea["aria-labelledby"],el,Q]),eo=(0,c.H)(en),ed={isInvalid:F,isRequired:z,validate:ee,validationState:_,validationBehavior:Z},{inputProps:es,isSelected:eu,isDisabled:ec,isReadOnly:ef,isPressed:eb,isInvalid:ep}=C?function(e,a,r){var i,t;let l=(0,c.H)({isReadOnly:e.isReadOnly||a.isReadOnly,isSelected:a.isSelected(e.value),onChange(r){r?a.addValue(e.value):a.removeValue(e.value),e.onChange&&e.onChange(r)}}),{name:n,descriptionId:o,errorMessageId:d,validationBehavior:s}=D.get(a);s=null!=(i=e.validationBehavior)?i:s;let{realtimeValidation:f}=(0,w.KZ)({...e,value:l.isSelected,name:void 0,validationBehavior:"aria"}),b=(0,u.useRef)(w.YD),p=()=>{a.setInvalid(e.value,f.isInvalid?f:b.current)};(0,u.useEffect)(p);let m=a.realtimeValidation.isInvalid?a.realtimeValidation:f,v="native"===s?a.displayValidation:m,h=I({...e,isReadOnly:e.isReadOnly||a.isReadOnly,isDisabled:e.isDisabled||a.isDisabled,name:e.name||n,isRequired:null!=(t=e.isRequired)?t:a.isRequired,validationBehavior:s,[w.Lf]:{realtimeValidation:m,displayValidation:v,resetValidation:a.resetValidation,commitValidation:a.commitValidation,updateValidation(e){b.current=e,p()}}},l,r);return{...h,inputProps:{...h.inputProps,"aria-describedby":[e["aria-describedby"],a.isInvalid?d:null,o].filter(Boolean).join(" ")||void 0}}}({...en,...ed},R.groupState,ei):I({...en,...ed},eo,ei),em="invalid"===_||F||ep,ev=!(ec||ef)&&eb,{hoverProps:eh,isHovered:eg}=(0,v.M)({isDisabled:es.disabled}),{focusProps:ex,isFocused:ey,isFocusVisible:ew}=(0,h.o)({autoFocus:es.autoFocus}),eR=(0,u.useMemo)(()=>p({color:U,size:T,radius:H,isInvalid:em,lineThrough:L,isDisabled:ec,disableAnimation:$}),[U,T,H,em,L,ec,$]);(0,m.U)(()=>{if(!ei.current)return;let e=!!ei.current.checked;eo.setSelected(e)},[ei.current]);let ek=function(e,a=[]){let r=(0,u.useRef)(e);return(0,m.U)(()=>{r.current=e}),(0,u.useCallback)((...e)=>{var a;return null==(a=r.current)?void 0:a.call(r,...e)},a)}(et),eC=(0,u.useCallback)(e=>{if(ef||ec)return void e.preventDefault();null==ek||ek(e)},[ef,ec,ek]),eI=(0,y.$z)(null==Y?void 0:Y.base,J),eD=(0,u.useCallback)(()=>({ref:er,className:eR.base({class:eI}),"data-disabled":(0,y.sE)(ec),"data-selected":(0,y.sE)(eu||K),"data-invalid":(0,y.sE)(em),"data-hover":(0,y.sE)(eg),"data-focus":(0,y.sE)(ey),"data-pressed":(0,y.sE)(ev),"data-readonly":(0,y.sE)(es.readOnly),"data-focus-visible":(0,y.sE)(ew),"data-indeterminate":(0,y.sE)(K),...(0,x.v)(eh,ea)}),[eR,eI,ec,eu,K,em,eg,ey,ev,es.readOnly,ew,eh,ea]),eS=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,"aria-hidden":!0,className:(0,y.$z)(eR.wrapper({class:(0,y.$z)(null==Y?void 0:Y.wrapper,null==e?void 0:e.className)}))}},[eR,null==Y?void 0:Y.wrapper]),eP=(0,u.useCallback)(()=>({ref:(0,S.P)(ei,E),...(0,x.v)(es,ex),className:eR.hiddenInput({class:null==Y?void 0:Y.hiddenInput}),onChange:(0,g.c)(es.onChange,eC)}),[es,ex,eC,null==Y?void 0:Y.hiddenInput]),eV=(0,u.useCallback)(()=>({id:el,className:eR.label({class:null==Y?void 0:Y.label})}),[eR,null==Y?void 0:Y.label,ec,eu,em]),ej=(0,u.useCallback)(()=>({isSelected:eu,isIndeterminate:K,disableAnimation:$,className:eR.icon({class:null==Y?void 0:Y.icon})}),[eR,null==Y?void 0:Y.icon,eu,K,$]);return{Component:j||"label",icon:q,children:B,isSelected:eu,isDisabled:ec,isInvalid:em,isFocused:ey,isHovered:eg,isFocusVisible:ew,getBaseProps:eD,getWrapperProps:eS,getInputProps:eP,getLabelProps:eV,getIconProps:ej}}({...e,ref:a}),C="function"==typeof l?l(R()):(0,u.cloneElement)(l,R());return(0,i.jsxs)(r,{...o(),children:[(0,i.jsx)("input",{...b()}),(0,i.jsx)("span",{...f(),children:C}),t&&(0,i.jsx)("span",{...k(),children:t})]})});j.displayName="HeroUI.Checkbox";var E=j},24750:(e,a,r)=>{r.d(a,{e:()=>d});var i=r(96539),t=r(89826),l=r(49878),n=r(60144),o=r(78749);function d(e,a,r){let{isDisabled:d=!1,isReadOnly:s=!1,value:u,name:c,children:f,"aria-label":b,"aria-labelledby":p,validationState:m="valid",isInvalid:v}=e,{pressProps:h,isPressed:g}=(0,n.d)({isDisabled:d}),{pressProps:x,isPressed:y}=(0,n.d)({onPress(){var e;a.toggle(),null==(e=r.current)||e.focus()},isDisabled:d||s}),{focusableProps:w}=(0,o.Wc)(e,r),R=(0,i.v)(h,w),k=(0,t.$)(e,{labelable:!0});return(0,l.F)(r,a.isSelected,a.setSelected),{labelProps:(0,i.v)(x,{onClick:e=>e.preventDefault()}),inputProps:(0,i.v)(k,{"aria-invalid":v||"invalid"===m||void 0,"aria-errormessage":e["aria-errormessage"],"aria-controls":e["aria-controls"],"aria-readonly":s||void 0,onChange:e=>{e.stopPropagation(),a.setSelected(e.target.checked)},disabled:d,...null==u?{}:{value:u},name:c,type:"checkbox",...R}),isSelected:a.isSelected,isPressed:g||y,isDisabled:d,isReadOnly:s,isInvalid:v||"invalid"===m}}},89121:(e,a,r)=>{r.d(a,{H:()=>t});var i=r(47314);function t(e={}){let{isReadOnly:a}=e,[r,l]=(0,i.P)(e.isSelected,e.defaultSelected||!1,e.onChange);return{isSelected:r,setSelected:function(e){a||l(e)},toggle:function(){a||l(!r)}}}}}]);