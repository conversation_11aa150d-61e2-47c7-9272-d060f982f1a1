"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9586],{61:(t,e,i)=>{i.d(e,{X:()=>s,f:()=>n});let n=t=>1e3*t,s=t=>t/1e3},1092:(t,e,i)=>{i.d(e,{P:()=>s});var n=i(98466);function s(t){return t.props[n.n]}},4815:(t,e,i)=>{i.d(e,{L:()=>a,m:()=>r});var n=i(61958),s=i(15065);function r(t,e){return(0,n.FY)((0,n.bS)(t.getBoundingClientRect(),e))}function a(t,e,i){let n=r(t,i),{scroll:a}=e;return a&&((0,s.Ql)(n.x,a.offset.x),(0,s.Ql)(n.y,a.offset.y)),n}},5826:(t,e,i)=>{i.d(e,{h:()=>p,q:()=>d});var n=i(38729),s=i(15198);let r=new Set,a=!1,o=!1,l=!1;function u(){if(o){let t=Array.from(r).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,n.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}o=!1,a=!1,r.forEach(t=>t.complete(l)),r.clear()}function h(){r.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(o=!0)})}function d(){l=!0,h(),u(),l=!1}class p{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(r.add(this),a||(a=!0,s.Gt.read(h),s.Gt.resolveKeyframes(u))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),r.delete(this)}cancel(){"scheduled"===this.state&&(r.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},9877:(t,e,i)=>{i.d(e,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},9908:(t,e,i)=>{i.d(e,{ge:()=>a,xU:()=>s});let n=()=>({translate:0,scale:1,origin:0,originPoint:0}),s=()=>({x:n(),y:n()}),r=()=>({min:0,max:0}),a=()=>({x:r(),y:r()})},15065:(t,e,i)=>{i.d(e,{OU:()=>u,Ql:()=>h,Ww:()=>p,hq:()=>r,o4:()=>l});var n=i(97841),s=i(47732);function r(t,e,i){return i+e*(t-i)}function a(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function o(t,e=0,i=1,n,s){t.min=a(t.min,e,i,n,s),t.max=a(t.max,e,i,n,s)}function l(t,{x:e,y:i}){o(t.x,e.translate,e.scale,e.originPoint),o(t.y,i.translate,i.scale,i.originPoint)}function u(t,e,i,n=!1){let r,a,o=i.length;if(o){e.x=e.y=1;for(let u=0;u<o;u++){a=(r=i[u]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&p(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,l(t,a)),n&&(0,s.HD)(r.latestValues)&&p(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function h(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,s,r=.5){let a=(0,n.k)(t.min,t.max,r);o(t,e,i,a,s)}function p(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},15198:(t,e,i)=>{i.d(e,{Gt:()=>s,PP:()=>o,WG:()=>r,uv:()=>a});var n=i(16089);let{schedule:s,cancel:r,state:a,steps:o}=(0,i(74908).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},16089:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t},24365:(t,e,i)=>{i.d(e,{y:()=>a});var n=i(69038),s=i(81495),r=i(31435);let a={test:t=>r.B.test(t)||n.u.test(t)||s.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):s.V.test(t)?s.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):s.V.transform(t),getAnimatableNone:t=>{let e=a.parse(t);return e.alpha=0,a.transform(e)}}},30547:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>a});var n=i(39363);let s=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&s.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,a,o,l]=s.match(n.S);return{[t]:parseFloat(r),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},31435:(t,e,i)=>{i.d(e,{B:()=>u});var n=i(57272),s=i(48782),r=i(91598),a=i(30547);let o=t=>(0,n.q)(0,255,t),l={...s.ai,transform:t=>Math.round(o(t))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(s.X4.transform(n))+")"}},31690:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},32181:(t,e,i)=>{i.d(e,{M:()=>n});let n=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary},34330:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>s,Kq:()=>n})},38729:(t,e,i)=>{i.d(e,{E4:()=>o,Hr:()=>d,W9:()=>h});var n=i(60059),s=i(17199),r=i(48782),a=i(13683);let o=t=>t===r.ai||t===a.px,l=new Set(["x","y","z"]),u=s.U.filter(t=>!l.has(t));function h(t){let e=[];return u.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}let d={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>(0,n.ry)(e,"x"),y:(t,{transform:e})=>(0,n.ry)(e,"y")};d.translateX=d.x,d.translateY=d.y},39363:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},43518:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var n=i(24365);let s=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(39363),a=i(91598);let o="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],a=0,h=e.replace(u,t=>(n.y.test(t)?(s.color.push(a),r.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(s.var.push(a),r.push("var"),i.push(t)):(s.number.push(a),r.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:h,indexes:s,types:r}}function d(t){return h(t).values}function p(t){let{split:e,types:i}=h(t),s=e.length;return t=>{let r="";for(let u=0;u<s;u++)if(r+=e[u],void 0!==t[u]){let e=i[u];e===o?r+=(0,a.a)(t[u]):e===l?r+=n.y.transform(t[u]):r+=t[u]}return r}}let c=t=>"number"==typeof t?0:n.y.test(t)?n.y.getAnimatableNone(t):t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(r.S)?.length||0)+(t.match(s)?.length||0)>0},parse:d,createTransformer:p,getAnimatableNone:function(t){let e=d(t);return p(t)(e.map(c))}}},46932:(t,e,i)=>{i.d(e,{f:()=>tL});var n=i(64554),s=i(15198),r=i(68673),a=i(57272),o=i(61),l=i(56848),u=i(9877),h=i(58356),d=i(1343),p=i(24365),c=i(43518),m=i(69038),f=i(81495);function v(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var y=i(31435);function g(t,e){return i=>i>0?e:t}var b=i(97841);let w=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},T=[m.u,y.B,f.V],S=t=>T.find(e=>e.test(t));function V(t){let e=S(t);if((0,h.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===f.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=v(o,n,t+1/3),r=v(o,n,t),a=v(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let A=(t,e)=>{let i=V(t),n=V(e);if(!i||!n)return g(t,e);let s={...i};return t=>(s.red=w(i.red,n.red,t),s.green=w(i.green,n.green,t),s.blue=w(i.blue,n.blue,t),s.alpha=(0,b.k)(i.alpha,n.alpha,t),y.B.transform(s))},x=new Set(["none","hidden"]);function M(t,e){return i=>(0,b.k)(t,e,i)}function k(t){return"number"==typeof t?M:"string"==typeof t?(0,d.p)(t)?g:p.y.test(t)?A:P:Array.isArray(t)?C:"object"==typeof t?p.y.test(t)?A:F:g}function C(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>k(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function F(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=k(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let P=(t,e)=>{let i=c.f.createTransformer(e),n=(0,c.V)(t),s=(0,c.V)(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?x.has(t)&&!s.values.length||x.has(e)&&!n.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,r.F)(C(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,s),s.values),i):((0,h.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),g(t,e))};function E(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,b.k)(t,e,i):k(t)(t,e)}let O=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>s.Gt.update(e,t),stop:()=>(0,s.WG)(e),now:()=>s.uv.isProcessing?s.uv.timestamp:l.k.now()}},I=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function D(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var N=i(88625);function q(t,e,i){let n=Math.max(e-5,0);return(0,N.f)(i-t(n),e-n)}let B={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function R(t,e){return t*Math.sqrt(1-e*e)}let X=["duration","bounce"],$=["stiffness","damping","mass"];function K(t,e){return e.some(e=>void 0!==t[e])}function W(t=B.visualDuration,e=B.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,l=n.keyframes[0],u=n.keyframes[n.keyframes.length-1],d={done:!1,value:l},{stiffness:p,damping:c,mass:m,duration:f,velocity:v,isResolvedFromDuration:y}=function(t){let e={velocity:B.velocity,stiffness:B.stiffness,damping:B.damping,mass:B.mass,isResolvedFromDuration:!1,...t};if(!K(t,$)&&K(t,X))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*(0,a.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:B.mass,stiffness:n,damping:s}}else{let i=function({duration:t=B.duration,bounce:e=B.bounce,velocity:i=B.velocity,mass:n=B.mass}){let s,r;(0,h.$)(t<=(0,o.f)(B.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let l=1-e;l=(0,a.q)(B.minDamping,B.maxDamping,l),t=(0,a.q)(B.minDuration,B.maxDuration,(0,o.X)(t)),l<1?(s=e=>{let n=e*l,s=n*t;return .001-(n-i)/R(e,l)*Math.exp(-s)},r=e=>{let n=e*l*t,r=Math.pow(l,2)*Math.pow(e,2)*t,a=Math.exp(-n),o=R(Math.pow(e,2),l);return(n*i+i-r)*a*(-s(e)+.001>0?-1:1)/o}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let u=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=(0,o.f)(t),isNaN(u))return{stiffness:B.stiffness,damping:B.damping,duration:t};{let e=Math.pow(u,2)*n;return{stiffness:e,damping:2*l*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:B.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-(0,o.X)(n.velocity||0)}),g=v||0,b=c/(2*Math.sqrt(p*m)),w=u-l,T=(0,o.X)(Math.sqrt(p/m)),S=5>Math.abs(w);if(s||(s=S?B.restSpeed.granular:B.restSpeed.default),r||(r=S?B.restDelta.granular:B.restDelta.default),b<1){let t=R(T,b);i=e=>u-Math.exp(-b*T*e)*((g+b*T*w)/t*Math.sin(t*e)+w*Math.cos(t*e))}else if(1===b)i=t=>u-Math.exp(-T*t)*(w+(g+T*w)*t);else{let t=T*Math.sqrt(b*b-1);i=e=>{let i=Math.exp(-b*T*e),n=Math.min(t*e,300);return u-i*((g+b*T*w)*Math.sinh(n)+t*w*Math.cosh(n))/t}}let V={calculatedDuration:y&&f||null,next:t=>{let e=i(t);if(y)d.done=t>=f;else{let n=0===t?g:0;b<1&&(n=0===t?(0,o.f)(g):q(i,t,e));let a=Math.abs(u-e)<=r;d.done=Math.abs(n)<=s&&a}return d.value=d.done?u:e,d},toString:()=>{let t=Math.min(D(V),2e4),e=I(e=>V.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return V}function L({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,p,c=t[0],m={done:!1,value:c},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,v=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,g=c+y,b=void 0===a?g:a(g);b!==g&&(y=b-c);let w=t=>-y*Math.exp(-t/n),T=t=>b+w(t),S=t=>{let e=w(t),i=T(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},V=t=>{f(m.value)&&(d=t,p=W({keyframes:[m.value,v(m.value)],velocity:q(T,t,m.value),damping:s,stiffness:r,restDelta:u,restSpeed:h}))};return V(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==d||(e=!0,S(t),V(t)),void 0!==d&&t>=d)?p.next(t-d):(e||S(t),m)}}}W.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(D(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:(0,o.X)(s)}}(t,100,W);return t.ease=e.ease,t.duration=(0,o.f)(e.duration),t.type="keyframes",t};var j=i(16089);let G=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function U(t,e,i,n){if(t===e&&i===n)return j.l;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=G(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:G(s(t),e,n)}let Y=U(.42,0,1,1),_=U(0,0,.58,1),z=U(.42,0,.58,1),H=t=>Array.isArray(t)&&"number"!=typeof t[0];var Q=i(55983),Z=i(31690);let J=U(.33,1.53,.69,.99),tt=(0,Z.G)(J),te=(0,Q.V)(tt),ti=t=>(t*=2)<1?.5*tt(t):.5*(2-Math.pow(2,-10*(t-1)));var tn=i(60570);let ts=t=>Array.isArray(t)&&"number"==typeof t[0],tr={linear:j.l,easeIn:Y,easeInOut:z,easeOut:_,circIn:tn.po,circInOut:tn.tn,circOut:tn.yT,backIn:tt,backInOut:te,backOut:J,anticipate:ti},ta=t=>"string"==typeof t,to=t=>{if(ts(t)){(0,h.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,s]=t;return U(e,i,n,s)}return ta(t)?((0,h.V)(void 0!==tr[t],`Invalid easing type '${t}'`,"invalid-easing-type"),tr[t]):t};var tl=i(84953),tu=i(85468);function th({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var s;let o=H(n)?n.map(to):to(n),l={done:!1,value:e[0]},u=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let o=t.length;if((0,h.V)(o===e.length,"Both input and output ranges must be the same length","range-length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];let l=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let u=function(t,e,i){let n=[],s=i||tl.W.mix||E,a=t.length-1;for(let i=0;i<a;i++){let a=s(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||j.l:e;a=(0,r.F)(t,a)}n.push(a)}return n}(e,n,s),d=u.length,p=i=>{if(l&&i<t[0])return e[0];let n=0;if(d>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=(0,tu.q)(t[n],t[n+1],i);return u[n](s)};return i?e=>p((0,a.q)(t[0],t[o-1],e)):p}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=(0,tu.q)(0,e,n);t.push((0,b.k)(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(o)?o:e.map(()=>o||z).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(l.value=u(e),l.done=e>=t,l)}}let td=t=>null!==t;function tp(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(td),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let tc={decay:L,inertia:L,tween:th,keyframes:th,spring:W};function tm(t){"string"==typeof t.type&&(t.type=tc[t.type])}class tf{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let tv=t=>t/100;class ty extends tf{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==l.k.now()&&this.tick(l.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},u.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tm(t);let{type:e=th,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:a=0}=t,{keyframes:o}=t,l=e||th;l!==th&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,r.F)(tv,E(o[0],o[1])),o=[0,100]);let u=l({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=D(u));let{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:p,repeatDelay:c,type:m,onUpdate:f,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let b=this.currentTime,w=i;if(d){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,c&&(i-=c/o)):"mirror"===p&&(w=r)),b=(0,a.q)(0,1,i)*o}let T=g?{done:!1,value:h[0]}:w.next(b);s&&(T.value=s(T.value));let{done:S}=T;g||null===l||(S=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let V=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return V&&m!==L&&(T.value=tp(h,this.options,v,this.speed)),f&&f(T.value),V&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return(0,o.X)(this.calculatedDuration)}get time(){return(0,o.X)(this.currentTime)}set time(t){t=(0,o.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(l.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,o.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=O,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(l.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,u.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}var tg=i(5826);let tb=t=>t.startsWith("--");function tw(t){let e;return()=>(void 0===e&&(e=t()),e)}let tT=tw(()=>void 0!==window.ScrollTimeline);var tS=i(58091);let tV={},tA=function(t,e){let i=tw(t);return()=>tV[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),tx=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,tM={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tx([0,.65,.55,1]),circOut:tx([.55,0,1,.45]),backIn:tx([.31,.01,.66,-.59]),backOut:tx([.33,1.53,.69,.99])};function tk(t){return"function"==typeof t&&"applyToOptions"in t}class tC extends tf{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,(0,h.V)("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return tk(t)&&tA()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let d={[e]:i};l&&(d.offset=l);let p=function t(e,i){if(e)return"function"==typeof e?tA()?I(e,i):"ease-out":ts(e)?tx(e):Array.isArray(e)?e.map(e=>t(e,i)||tM.easeOut):tM[e]}(o,s);Array.isArray(p)&&(d.easing=p),tS.Q.value&&u.q.waapi++;let c={delay:n,duration:s,easing:Array.isArray(p)?"linear":p,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let m=t.animate(d,c);return tS.Q.value&&m.finished.finally(()=>{u.q.waapi--}),m}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=tp(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tb(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,o.X)(Number(t))}get time(){return(0,o.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,o.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tT())?(this.animation.timeline=t,j.l):e(this)}}let tF={anticipate:ti,backInOut:te,circInOut:tn.tn};class tP extends tC{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tF&&(t.ease=tF[t.ease])}(t),tm(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ty({...r,autoplay:!1}),l=(0,o.f)(this.finishedTime??this.time);e.setWithVelocity(a.sample(l-10).value,a.sample(l).value,10),a.stop()}}let tE=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(c.f.test(t)||"0"===t)&&!t.startsWith("url("));var tO=i(20714);let tI=new Set(["opacity","clipPath","filter","transform"]),tD=tw(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tN extends tf{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:u,element:h,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=l.k.now();let p={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:u,element:h,...d},c=h?.KeyframeResolver||tg.h;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,p,!i),o,u,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:u,onUpdate:d}=i;this.resolvedAt=l.k.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=tE(s,e),o=tE(r,e);return(0,h.$)(a===o,`You are trying to animate ${e} from "${s}" to "${r}". "${a?r:s}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tk(i))&&n)}(t,s,r,a)&&((tl.W.instantAnimations||!o)&&d?.(tp(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let p={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!u&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(0,tO.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return tD()&&i&&tI.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(p)?new tP({...p,element:p.motionValue.owner.current}):new ty(p);c.finished.then(()=>this.notifyFinished()).catch(j.l),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,tg.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tq=t=>null!==t;var tB=i(17199);let tR={type:"spring",stiffness:500,damping:25,restSpeed:10},tX=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),t$={type:"keyframes",duration:.8},tK={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tW=(t,{keyframes:e})=>e.length>2?t$:tB.f.has(t)?t.startsWith("scale")?tX(e[1]):tR:tK,tL=(t,e,i,r={},a,l)=>u=>{let h=(0,n.r)(r,t)||{},d=h.delay||r.delay||0,{elapsed:p=0}=r;p-=(0,o.f)(d);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...h,delay:-p,onUpdate:t=>{e.set(t),h.onUpdate&&h.onUpdate(t)},onComplete:()=>{u(),h.onComplete&&h.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(h)&&Object.assign(c,tW(t,c)),c.duration&&(c.duration=(0,o.f)(c.duration)),c.repeatDelay&&(c.repeatDelay=(0,o.f)(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let m=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(m=!0)),(tl.W.instantAnimations||tl.W.skipAnimations)&&(m=!0,c.duration=0,c.delay=0),c.allowFlatten=!h.type&&!h.ease,m&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(tq),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(c.keyframes,h);if(void 0!==t)return void s.Gt.update(()=>{c.onUpdate(t),c.onComplete()})}return h.isSync?new ty(c):new tN(c)}},47732:(t,e,i)=>{function n(t){return void 0===t||1===t}function s({scale:t,scaleX:e,scaleY:i}){return!n(t)||!n(e)||!n(i)}function r(t){return s(t)||a(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function a(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>r,vF:()=>a,vk:()=>s})},55983:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},56848:(t,e,i)=>{let n;i.d(e,{k:()=>o});var s=i(84953),r=i(15198);function a(){n=void 0}let o={now:()=>(void 0===n&&o.set(r.uv.isProcessing||s.W.useManualTiming?r.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(a)}}},58356:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>s});let n=()=>{},s=()=>{}},60059:(t,e,i)=>{i.d(e,{Ib:()=>p,ry:()=>d,zs:()=>h});let n=t=>180*t/Math.PI,s=t=>a(n(Math.atan2(t[1],t[0]))),r={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:s,rotateZ:s,skewX:t=>n(Math.atan(t[1])),skewY:t=>n(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},a=t=>((t%=360)<0&&(t+=360),t),o=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:o,scaleY:l,scale:t=>(o(t)+l(t))/2,rotateX:t=>a(n(Math.atan2(t[6],t[5]))),rotateY:t=>a(n(Math.atan2(-t[2],t[0]))),rotateZ:s,rotate:s,skewX:t=>n(Math.atan(t[4])),skewY:t=>n(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function h(t){return+!!t.includes("scale")}function d(t,e){let i,n;if(!t||"none"===t)return h(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=u,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=r,n=e}if(!n)return h(e);let a=i[e],o=n[1].split(",").map(c);return"function"==typeof a?a(o):o[a]}let p=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return d(i,e)};function c(t){return parseFloat(t.trim())}},60570:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>o,yT:()=>a});var n=i(55983),s=i(31690);let r=t=>1-Math.sin(Math.acos(t)),a=(0,s.G)(r),o=(0,n.V)(r)},61958:(t,e,i)=>{function n({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function s({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function r(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}i.d(e,{FY:()=>n,bS:()=>r,pA:()=>s})},64554:(t,e,i)=>{i.d(e,{r:()=>n});function n(t,e){return t?.[e]??t?.default??t}},68673:(t,e,i)=>{i.d(e,{F:()=>s});let n=(t,e)=>i=>e(t(i)),s=(...t)=>t.reduce(n)},69038:(t,e,i)=>{i.d(e,{u:()=>s});var n=i(31435);let s={test:(0,i(30547).$)("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:n.B.transform}},77104:(t,e,i)=>{i.d(e,{D:()=>s,I:()=>n});let n={x:!1,y:!1};function s(){return n.x||n.y}},77543:(t,e,i)=>{i.d(e,{OQ:()=>h});var n=i(87740),s=i(88625),r=i(56848),a=i(15198);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=r.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},81495:(t,e,i)=>{i.d(e,{V:()=>o});var n=i(48782),s=i(13683),r=i(91598),a=i(30547);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+s.KN.transform((0,r.a)(e))+", "+s.KN.transform((0,r.a)(i))+", "+(0,r.a)(n.X4.transform(a))+")"}},82216:(t,e,i)=>{i.d(e,{F:()=>r,e:()=>s});var n=i(32181);function s(t){return{point:{x:t.pageX,y:t.pageY}}}let r=t=>e=>(0,n.M)(e)&&t(e,s(e))},85468:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},87740:(t,e,i)=>{i.d(e,{v:()=>s});var n=i(34330);class s{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},87820:(t,e,i)=>{i.d(e,{k:()=>n});function n(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}},88452:(t,e,i)=>{i.d(e,{X:()=>n});class n{constructor(t){this.isMounted=!1,this.node=t}update(){}}},88625:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},89586:(t,e,i)=>{i.d(e,{l:()=>t1});var n=i(84046),s=i(68849);function r(t,e,i){let n=t.getProps();return(0,s.a)(n,e,void 0!==i?i:n.custom,t)}var a=i(64554),o=i(15198),l=i(17199);let u=new Set(["width","height","top","left","right","bottom",...l.U]);var h=i(77543);let d=t=>Array.isArray(t);var p=i(92146),c=i(1092),m=i(46932);function f(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:l=t.getDefaultTransition(),transitionEnd:v,...y}=e;n&&(l=n);let g=[],b=s&&t.animationState&&t.animationState.getState()[s];for(let e in y){let n=t.getValue(e,t.latestValues[e]??null),s=y[e];if(void 0===s||b&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(b,e))continue;let r={delay:i,...(0,a.r)(l||{},e)},h=n.get();if(void 0!==h&&!n.isAnimating&&!Array.isArray(s)&&s===h&&!r.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=(0,c.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,o.Gt);null!==t&&(r.startTime=t,d=!0)}}(0,p.g)(t,e),n.start((0,m.f)(e,n,s,t.shouldReduceMotion&&u.has(e)?{type:!1}:r,t,d));let f=n.animation;f&&g.push(f)}return v&&Promise.all(g).then(()=>{o.Gt.update(()=>{v&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=r(t,e)||{};for(let e in s={...s,...i}){var a;let i=d(a=s[e])?a[a.length-1]||0:a;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,h.OQ)(i))}}(t,v)})}),g}function v(t,e,i={}){let n=r(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let a=n?()=>Promise.all(f(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=0,r=1,a){let o=[],l=t.variantChildren.size,u=(l-1)*s,h="function"==typeof n,d=h?t=>n(t,l):1===r?(t=0)=>t*s:(t=0)=>u-t*s;return Array.from(t.variantChildren).sort(y).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(v(t,e,{...a,delay:i+(h?0:n)+d(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n,r,a,o,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([a(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[a,o]:[o,a];return t().then(()=>e())}}function y(t,e){return t.sortNodePosition(e)}function g(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}var b=i(93095),w=i(64558);let T=w._.length,S=[...w.U].reverse(),V=w.U.length;function A(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function x(){return{animate:A(!0),whileInView:A(),whileHover:A(),whileTap:A(),whileDrag:A(),whileFocus:A(),exit:A()}}var M=i(88452);class k extends M.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>v(t,e,i)));else if("string"==typeof e)n=v(t,e,i);else{let s="function"==typeof e?r(t,e,i.custom):e;n=Promise.all(f(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=x(),s=!0,a=e=>(i,n)=>{let s=r(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<T;t++){let n=w._[t],s=e.props[n];((0,b.w)(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},h=[],p=new Set,c={},m=1/0;for(let e=0;e<V;e++){var f,v;let r=S[e],y=i[r],w=void 0!==l[r]?l[r]:u[r],T=(0,b.w)(w),V=r===o?y.isActive:null;!1===V&&(m=e);let A=w===u[r]&&w!==l[r]&&T;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),y.protectedKeys={...c},!y.isActive&&null===V||!w&&!y.prevProp||(0,n.N)(w)||"boolean"==typeof w)continue;let x=(f=y.prevProp,"string"==typeof(v=w)?v!==f:!!Array.isArray(v)&&!g(v,f)),M=x||r===o&&y.isActive&&!A&&T||e>m&&T,k=!1,C=Array.isArray(w)?w:[w],F=C.reduce(a(r),{});!1===V&&(F={});let{prevResolvedValues:P={}}=y,E={...P,...F},O=e=>{M=!0,p.has(e)&&(k=!0,p.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=F[t],i=P[t];if(c.hasOwnProperty(t))continue;let n=!1;(d(e)&&d(i)?g(e,i):e===i)?void 0!==e&&p.has(t)?O(t):y.protectedKeys[t]=!0:null!=e?O(t):p.add(t)}y.prevProp=w,y.prevResolvedValues=F,y.isActive&&(c={...c,...F}),s&&t.blockInitialAnimation&&(M=!1);let I=!(A&&x)||k;M&&I&&h.push(...C.map(t=>({animation:t,options:{type:r}})))}if(p.size){let e={};if("boolean"!=typeof l.initial){let i=r(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}p.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let y=!!h.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=o(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=x(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,n.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let C=0;class F extends M.X{constructor(){super(...arguments),this.id=C++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var P=i(77104);function E(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function O(t){return!("touch"===t.pointerType||(0,P.D)())}var I=i(82216);function D(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&o.Gt.postRender(()=>s(e,(0,I.e)(e)))}class N extends M.X{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=E(t,i),a=t=>{if(!O(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{O(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(D(this.node,e,"Start"),t=>D(this.node,t,"End"))))}unmount(){}}var q=i(68673),B=i(87820);class R extends M.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,q.F)((0,B.k)(this.node.current,"focus",()=>this.onFocus()),(0,B.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var X=i(20714);let $=(t,e)=>!!e&&(t===e||$(t,e.parentElement));var K=i(32181);let W=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function j(t){return e=>{"Enter"===e.key&&t(e)}}function G(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let U=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=j(()=>{if(L.has(i))return;G(i,"down");let t=j(()=>{G(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>G(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function Y(t){return(0,K.M)(t)&&!(0,P.D)()}function _(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&o.Gt.postRender(()=>s(e,(0,I.e)(e)))}class z extends M.X{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=E(t,i),a=t=>{let n=t.currentTarget;if(!Y(t))return;L.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),L.has(n)&&L.delete(n),Y(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||$(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),(0,X.s)(t))&&(t.addEventListener("focus",t=>U(t,s)),W.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(_(this.node,e,"Start"),(t,{success:e})=>_(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let H=new WeakMap,Q=new WeakMap,Z=t=>{let e=H.get(t.target);e&&e(t)},J=t=>{t.forEach(Z)},tt={some:0,all:1};class te extends M.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:tt[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;Q.has(i)||Q.set(i,{});let n=Q.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(J,{root:t,...e})),n[s]}(e);return H.set(t,i),n.observe(t),()=>{H.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}var ti=i(9585),tn=i(60059),ts=i(1343),tr=i(4815),ta=i(48782),to=i(13683);let tl=t=>e=>e.test(t),tu=[ta.ai,to.px,to.KN,to.uj,to.vw,to.vh,{test:t=>"auto"===t,parse:t=>t}],th=t=>tu.find(tl(t));var td=i(58356);let tp=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),tc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var tm=i(5826);let tf=t=>/^0[^.\s]+$/u.test(t);var tv=i(43518),ty=i(39363);let tg=new Set(["brightness","contrast","saturate","opacity"]);function tb(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(ty.S)||[];if(!n)return t;let s=i.replace(n,""),r=+!!tg.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let tw=/\b([a-z-]*)\(.*?\)/gu,tT={...tv.f,getAnimatableNone:t=>{let e=t.match(tw);return e?e.map(tb).join(" "):t}};var tS=i(24365);let tV={...i(15919).W,color:tS.y,backgroundColor:tS.y,outlineColor:tS.y,fill:tS.y,stroke:tS.y,borderColor:tS.y,borderTopColor:tS.y,borderRightColor:tS.y,borderBottomColor:tS.y,borderLeftColor:tS.y,filter:tT,WebkitFilter:tT},tA=t=>tV[t];function tx(t,e){let i=tA(t);return i!==tT&&(i=tv.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tM=new Set(["auto","none","0"]);var tk=i(38729);class tC extends tm.h{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,ts.p)(n))){let s=function t(e,i,n=1){(0,td.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,r]=function(t){let e=tc.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return tp(t)?parseFloat(t):t}return(0,ts.p)(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!u.has(i)||2!==t.length)return;let[n,s]=t,r=th(n),a=th(s);if(r!==a)if((0,tk.E4)(r)&&(0,tk.E4)(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tk.Hr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tf(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!tM.has(e)&&(0,tv.V)(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=tx(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tk.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=tk.Hr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}var tF=i(55150),tP=i(56848);let tE=[...tu,tS.y,tv.f],tO=t=>tE.find(tl(t));var tI=i(87740),tD=i(21320),tN=i(9908),tq=i(33798);let tB={current:null},tR={current:!1},tX=new WeakMap;var t$=i(38267);let tK=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tW{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tm.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tP.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,o.Gt.render(this.render,!1,!0))};let{latestValues:l,renderState:u}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=e.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=(0,t$.e)(e),this.isVariantNode=(0,t$.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==l[t]&&(0,tF.S)(e)&&e.set(l[t],!1)}}mount(t){this.current=t,tX.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),tR.current||function(){if(tR.current=!0,tq.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>tB.current=t.matches;t.addEventListener("change",e),e()}else tB.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tB.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,o.WG)(this.notifyUpdate),(0,o.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=l.f.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&o.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in tD.B){let e=tD.B[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,tN.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<tK.length;e++){let i=tK[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if((0,tF.S)(s))t.addValue(n,s);else if((0,tF.S)(r))t.addValue(n,(0,h.OQ)(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,h.OQ)(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,h.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(tp(i)||tf(i))?i=parseFloat(i):!tO(i)&&tv.f.test(e)&&(i=tx(t,e)),this.setBaseTarget(t,(0,tF.S)(i)?i.get():i)),(0,tF.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=(0,s.a)(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,tF.S)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tI.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class tL extends tW{constructor(){super(...arguments),this.KeyframeResolver=tC}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,tF.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}var tj=i(651);function tG(t,{style:e,vars:i},n,s){let r,a=t.style;for(r in e)a[r]=e[r];for(r in s?.applyProjectionStyles(a,n),i)a.setProperty(r,i[r])}var tU=i(35808);class tY extends tL{constructor(){super(...arguments),this.type="html",this.renderInstance=tG}readValueFromInstance(t,e){if(l.f.has(e))return this.projection?.isProjecting?(0,tn.zs)(e):(0,tn.Ib)(t,e);{let i=window.getComputedStyle(t),n=((0,ts.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,tr.m)(t,e)}build(t,e,i){(0,tj.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,tU.x)(t,e,i)}}var t_=i(20460),tz=i(14784);let tH=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var tQ=i(5773),tZ=i(70081);class tJ extends tL{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tN.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(l.f.has(e)){let t=tA(e);return t&&t.default||0}return e=tH.has(e)?e:(0,t_.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,tZ.x)(t,e,i)}build(t,e,i){(0,tz.B)(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in tG(t,e,void 0,n),e.attrs)t.setAttribute(tH.has(i)?i:(0,t_.I)(i),e.attrs[i])}mount(t){this.isSVGTag=(0,tQ.n)(t.tagName),super.mount(t)}}var t0=i(81956);let t1={renderer:(t,e)=>(0,t0.Q)(t)?new tJ(e):new tY(e,{allowProjection:t!==ti.Fragment}),animation:{Feature:k},exit:{Feature:F},inView:{Feature:te},tap:{Feature:z},focus:{Feature:R},hover:{Feature:N}}},91598:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},92146:(t,e,i)=>{i.d(e,{g:()=>r});var n=i(84953),s=i(55150);function r(t,e){let i=t.getValue("willChange");if((0,s.S)(i)&&i.add)return i.add(e);if(!i&&n.W.WillChange){let i=new n.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},97841:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i}}]);