(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1730],{1632:(e,s,r)=>{"use strict";r.d(s,{f:()=>m});var t={px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},a=r(3208),l=(0,r(92610).tv)({base:"w-px h-px inline-block",variants:{isInline:{true:"inline-block",false:"block"}},defaultVariants:{isInline:!1}}),n=r(18884),i=r(9585),o=e=>{var s;return null!=(s=t[e])?s:e},c=r(19605),d=(0,a.Rf)((e,s)=>{let{Component:r,getSpacerProps:t}=function(e){let[s,r]=(0,a.rE)(e,l.variantKeys),{as:t,className:c,x:d=1,y:m=1,...x}=s,h=(0,i.useMemo)(()=>l({...r,className:c}),[(0,n.t6)(r),c]),u=o(d),p=o(m);return{Component:t||"span",getSpacerProps:(e={})=>({...e,...x,"aria-hidden":(0,n.sE)(!0),className:(0,n.$z)(h,e.className),style:{...e.style,...x.style,marginLeft:u,marginTop:p}})}}({...e});return(0,c.jsx)(r,{ref:s,...t()})});d.displayName="HeroUI.Spacer";var m=d},27301:(e,s,r)=>{Promise.resolve().then(r.bind(r,75300))},39546:(e,s,r)=>{"use strict";r.d(s,{A:()=>n});var t=r(19605),a=r(66700),l=r(9585);function n(e){var s,r,n,i;let{preventZoom:o=!0,...c}=e,d=(0,l.useRef)(null);return(0,l.useEffect)(()=>{if(!o)return;let e=()=>{let e=document.querySelector("meta[name=viewport]");e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover"),d.current&&16>parseFloat(window.getComputedStyle(d.current).fontSize)&&(d.current.style.fontSize="16px")},s=()=>{setTimeout(()=>{let e=document.querySelector("meta[name=viewport]");e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover")},300)},r=d.current;if(r)return r.addEventListener("focus",e),r.addEventListener("blur",s),()=>{r.removeEventListener("focus",e),r.removeEventListener("blur",s)}},[o]),(0,t.jsx)(a.r,{...c,ref:d,classNames:{input:["text-base","sm:text-sm",...Array.isArray(null==(s=c.classNames)?void 0:s.input)?c.classNames.input:[null==(r=c.classNames)?void 0:r.input].filter(Boolean)],inputWrapper:[...Array.isArray(null==(n=c.classNames)?void 0:n.inputWrapper)?c.classNames.inputWrapper:[null==(i=c.classNames)?void 0:i.inputWrapper].filter(Boolean)]},style:{fontSize:"16px",...c.style}})}},55935:(e,s,r)=>{"use strict";var t=r(85383);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},72031:(e,s,r)=>{"use strict";r.d(s,{A:()=>n,h:()=>i});var t=r(19605),a=r(9585);let l=(0,a.createContext)(void 0);function n(e){let{children:s}=e,[r,n]=(0,a.useState)(null),[i,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{c()},[]);let c=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let s=await e.json();n(s.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{o(!1)}};return(0,t.jsx)(l.Provider,{value:{user:r,loading:i,login:e=>{n(e)},logout:()=>{n(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:s})}function i(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}},75300:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>S});var t=r(19605),a=r(9585),l=r(72031),n=r(55935),i=r(13983),o=r(54110),c=r(56995),d=r(21598),m=r(19864),x=r(63707),h=r(1632),u=r(90536),p=r(91102),j=r(63833),f=r(93072),v=r(66700),g=r(57059),y=r(5230),N=r(94661),w=r(76562),b=r(39546);let T=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;if(null==e)return"0."+"0".repeat(s);let r="string"==typeof e?parseFloat(e):e;return isNaN(r)?"0."+"0".repeat(s):r.toFixed(s)};function S(){var e,s;let{user:r,loading:S,logout:k}=(0,l.h)(),C=(0,n.useRouter)(),[P,A]=(0,a.useState)([]),[R,O]=(0,a.useState)(!0),[z,D]=(0,a.useState)(null),[E,F]=(0,a.useState)(!1),[U,_]=(0,a.useState)(!1),[K,X]=(0,a.useState)(!1),[q,L]=(0,a.useState)(!1),[J,I]=(0,a.useState)({name:"",network:"nile",password:""}),[M,W]=(0,a.useState)({name:"",privateKey:"",network:"nile",password:""}),[Y,Z]=(0,a.useState)({toAddress:"",amount:"",currency:"TRX",password:""}),[B,H]=(0,a.useState)(!1),[$,G]=(0,a.useState)(!1),[V,Q]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(!S){if(!r)return void C.push("/auth/signin");ee()}},[r,S,C]);let ee=async()=>{try{O(!0);let e=await fetch("/api/wallets");if(e.ok){let s=await e.json();if(s.success&&s.data){let e=s.data;A(e);let r=e.find(e=>e.is_default)||e[0];r&&(D(r),await es(r))}else console.error("获取钱包列表失败:",s.error)}}catch(e){console.error("加载钱包失败:",e)}finally{O(!1)}},es=async e=>{try{let s=e.network||"nile",r=await fetch("/api/wallets/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({address:e.address,currency:"TRX",network:s})}),t=await fetch("/api/wallets/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({address:e.address,currency:"USDT",network:s})}),a=0,l=0;r.ok&&(a=(await r.json()).balance||0),t.ok&&(l=(await t.json()).balance||0);let n={TRX:a,USDT:l};console.log("钱包 ".concat(e.address," 余额更新:"),n),A(s=>s.map(s=>s.id===e.id?{...s,balance:n}:s)),(null==z?void 0:z.id)===e.id&&(D({...e,balance:n}),console.log("选中钱包余额已更新:",{...e,balance:n}))}catch(e){console.error("获取余额失败:",e)}},er=async()=>{if(!J.name.trim())return void alert("请输入钱包名称");if(!J.password.trim())return void alert("请设置钱包密码");if(J.password.length<6)return void alert("密码长度至少6位");try{H(!0);let e=await fetch("/api/wallets/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(J)});if(e.ok){let s=await e.json();alert("钱包创建成功！\n\n地址: ".concat(s.address,"\n\n请务必安全保存您的私钥！")),F(!1),I({name:"",network:"nile",password:""}),await ee()}else{let s=await e.json();alert("创建失败: ".concat(s.message))}}catch(e){console.error("创建钱包失败:",e),alert("创建钱包失败，请重试")}finally{H(!1)}},et=async()=>{if(!M.name.trim()||!M.privateKey.trim())return void alert("请填写完整信息");if(!M.password.trim())return void alert("请设置钱包密码");if(M.password.length<6)return void alert("密码长度至少6位");try{G(!0);let e=await fetch("/api/wallets/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(M)});if(e.ok)alert("钱包导入成功！"),_(!1),W({name:"",privateKey:"",network:"nile",password:""}),await ee();else{let s=await e.json();alert("导入失败: ".concat(s.message))}}catch(e){console.error("导入钱包失败:",e),alert("导入钱包失败，请重试")}finally{G(!1)}},ea=async()=>{if(!z||!Y.toAddress.trim()||!Y.amount.trim())return void alert("请填写完整的转账信息");try{Q(!0);let e=await fetch("/api/wallets/transfer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({walletId:z.id,...Y,amount:parseFloat(Y.amount)})});if(e.ok){let s=await e.json();s.success?(alert("转账成功！\n交易哈希: ".concat(s.txHash)),X(!1),Z({toAddress:"",amount:"",currency:"TRX",password:""}),await es(z)):alert("转账失败: ".concat(s.error))}else{let s=await e.json();alert("转账失败: ".concat(s.message))}}catch(e){console.error("转账失败:",e),alert("转账失败，请重试")}finally{Q(!1)}},el=async()=>{if(confirm("确定要退出登录吗？"))try{await fetch("/api/auth/logout",{method:"POST"})}catch(e){console.error("退出登录失败:",e)}finally{k(),C.push("/login")}};return S||R?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)(i.o,{size:"lg"})}):r?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900",children:"TRON钱包"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1 text-sm md:text-base",children:"安全、便捷的数字资产管理"})]}),(0,t.jsxs)("div",{className:"text-right ml-4",children:[(0,t.jsxs)("p",{className:"text-sm md:text-base font-medium text-gray-900",children:["\uD83D\uDC4B ",(null==r?void 0:r.name)||(null==r?void 0:r.username)]}),(0,t.jsx)("p",{className:"text-xs md:text-sm text-gray-500",children:(null==r?void 0:r.role)==="super_admin"?"超级管理员":(null==r?void 0:r.role)==="agent"?"代理":"用户"})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 mb-4 opacity-75",children:[(0,t.jsx)(o.T,{color:"default",variant:"ghost",size:"sm",onPress:()=>F(!0),className:"flex-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-all duration-200",startContent:(0,t.jsx)("span",{className:"text-sm mr-1",children:"➕"}),children:"创建新钱包"}),(0,t.jsx)(o.T,{color:"default",variant:"light",size:"sm",onPress:el,className:"px-4 text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200",startContent:(0,t.jsx)("span",{className:"text-sm mr-1",children:"\uD83D\uDEAA"}),children:"退出登录"})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)(o.T,{color:"primary",variant:"solid",size:"lg",onPress:()=>_(!0),className:"font-bold text-xl flex-1 sm:flex-2 h-16 shadow-xl bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300",startContent:(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10  rounded-lg mr-2",children:(0,t.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE5"})}),children:"导入钱包"}),(0,t.jsx)(o.T,{color:"warning",variant:"solid",size:"lg",onPress:()=>C.push("/brush"),className:"font-bold text-xl flex-1 sm:flex-2 h-16 shadow-xl bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 hover:from-orange-600 hover:via-red-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-300 border-0",startContent:(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10  rounded-full mr-2",children:(0,t.jsx)("span",{className:"text-2xl animate-pulse",children:"\uD83C\uDFAF"})}),children:"理财专区"})]})]}),0===P.length?(0,t.jsx)(c.Z,{className:"p-8 text-center",children:(0,t.jsxs)(d.U,{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"还没有钱包"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"创建或导入您的第一个TRON钱包开始使用"}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsx)(o.T,{color:"primary",size:"lg",onPress:()=>F(!0),children:"创建新钱包"}),(0,t.jsx)(o.T,{color:"secondary",variant:"bordered",size:"lg",onPress:()=>_(!0),children:"导入现有钱包"})]})]})}):(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)(c.Z,{children:[(0,t.jsx)(m.d,{children:(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"我的钱包"})}),(0,t.jsx)(d.U,{className:"space-y-3",children:P.map(e=>(0,t.jsx)("div",{className:"p-3 rounded-lg border cursor-pointer transition-colors ".concat((null==z?void 0:z.id)===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"),onClick:()=>{D(e),es(e)},children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[e.address.slice(0,8),"...",e.address.slice(-6)]}),(0,t.jsx)("p",{className:"text-xs text-blue-600",children:"mainnet"===e.network?"主网":"nile"===e.network?"Nile测试网":"shasta"===e.network?"Shasta测试网":"Nile测试网"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-end gap-1",children:[(0,t.jsx)(x.R,{size:"sm",color:"created"===e.wallet_type?"success":"primary",variant:"flat",children:"created"===e.wallet_type?"已创建":"已导入"}),e.is_default&&(0,t.jsx)(x.R,{size:"sm",color:"warning",variant:"flat",children:"默认"})]})]})},e.id))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:z&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Z,{children:[(0,t.jsx)(m.d,{children:(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"钱包余额"})}),(0,t.jsxs)(d.U,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:[T(null==(e=z.balance)?void 0:e.TRX,6)," TRX"]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"TRON"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:[T(null==(s=z.balance)?void 0:s.USDT,2)," USDT"]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Tether USD"})]})]}),(0,t.jsx)(h.f,{y:4}),(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsx)(o.T,{color:"primary",size:"lg",onPress:()=>X(!0),children:"转账"}),(0,t.jsx)(o.T,{color:"success",variant:"bordered",size:"lg",onPress:()=>L(!0),children:"收款"}),(0,t.jsx)(o.T,{color:"default",variant:"bordered",size:"lg",onPress:()=>es(z),children:"刷新"})]})]})]}),(0,t.jsxs)(c.Z,{children:[(0,t.jsx)(m.d,{children:(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"钱包信息"})}),(0,t.jsx)(d.U,{children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"钱包名称"}),(0,t.jsx)("p",{className:"font-medium",children:z.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"钱包地址"}),(0,t.jsx)("p",{className:"font-mono text-sm break-all",children:z.address})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"钱包类型"}),(0,t.jsx)("p",{className:"font-medium",children:"created"===z.wallet_type?"创建的钱包":"导入的钱包"})]})]})})]})]})})]}),(0,t.jsx)(u.Y,{isOpen:E,onClose:()=>F(!1),children:(0,t.jsxs)(p.g,{children:[(0,t.jsx)(j.c,{children:"创建新钱包"}),(0,t.jsxs)(f.h,{children:[(0,t.jsx)(v.r,{label:"钱包名称",placeholder:"输入钱包名称",value:J.name,onChange:e=>I({...J,name:e.target.value})}),(0,t.jsx)(v.r,{label:"钱包密码",type:"password",placeholder:"设置钱包密码（用于转账时验证）",value:J.password,onChange:e=>I({...J,password:e.target.value}),description:"密码用于保护您的私钥，转账时需要输入此密码"}),(0,t.jsxs)(g.d,{label:"网络",selectedKeys:[J.network],onSelectionChange:e=>I({...J,network:Array.from(e)[0]}),children:[(0,t.jsx)(y.y,{children:"主网 (Mainnet)"},"mainnet"),(0,t.jsx)(y.y,{children:"测试网 (Nile)"},"nile")]})]}),(0,t.jsxs)(N.q,{children:[(0,t.jsx)(o.T,{variant:"light",onPress:()=>F(!1),children:"取消"}),(0,t.jsx)(o.T,{color:"primary",onPress:er,isLoading:B,children:"创建钱包"})]})]})}),(0,t.jsx)(u.Y,{isOpen:U,onClose:()=>_(!1),children:(0,t.jsxs)(p.g,{children:[(0,t.jsx)(j.c,{children:"导入钱包"}),(0,t.jsxs)(f.h,{children:[(0,t.jsx)(v.r,{label:"钱包名称",placeholder:"输入钱包名称",value:M.name,onChange:e=>W({...M,name:e.target.value})}),(0,t.jsx)(v.r,{label:"私钥",placeholder:"输入64位私钥",type:"password",value:M.privateKey,onChange:e=>W({...M,privateKey:e.target.value})}),(0,t.jsx)(v.r,{label:"钱包密码",type:"password",placeholder:"设置钱包密码（用于转账时验证）",value:M.password,onChange:e=>W({...M,password:e.target.value}),description:"密码用于保护您的私钥，转账时需要输入此密码"}),(0,t.jsxs)(g.d,{label:"网络",selectedKeys:[M.network],onSelectionChange:e=>W({...M,network:Array.from(e)[0]}),children:[(0,t.jsx)(y.y,{children:"主网 (Mainnet)"},"mainnet"),(0,t.jsx)(y.y,{children:"测试网 (Nile)"},"nile")]})]}),(0,t.jsxs)(N.q,{children:[(0,t.jsx)(o.T,{variant:"light",onPress:()=>_(!1),children:"取消"}),(0,t.jsx)(o.T,{color:"primary",onPress:et,isLoading:$,children:"导入钱包"})]})]})}),(0,t.jsx)(u.Y,{isOpen:K,onClose:()=>X(!1),children:(0,t.jsxs)(p.g,{children:[(0,t.jsx)(j.c,{children:"发送转账"}),(0,t.jsxs)(f.h,{children:[(0,t.jsx)(b.A,{label:"接收地址",placeholder:"输入TRON地址",value:Y.toAddress,onChange:e=>Z({...Y,toAddress:e.target.value})}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(b.A,{label:"金额",placeholder:"输入转账金额",type:"number",value:Y.amount,onChange:e=>Z({...Y,amount:e.target.value})}),(0,t.jsxs)(g.d,{label:"币种",selectedKeys:[Y.currency],onSelectionChange:e=>Z({...Y,currency:Array.from(e)[0]}),children:[(0,t.jsx)(y.y,{children:"TRX"},"TRX"),(0,t.jsx)(y.y,{children:"USDT"},"USDT")]})]}),(0,t.jsx)(b.A,{label:"钱包密码",placeholder:"输入钱包密码",type:"password",value:Y.password,onChange:e=>Z({...Y,password:e.target.value})})]}),(0,t.jsxs)(N.q,{children:[(0,t.jsx)(o.T,{variant:"light",onPress:()=>X(!1),children:"取消"}),(0,t.jsx)(o.T,{color:"primary",onPress:ea,isLoading:V,children:"确认转账"})]})]})}),(0,t.jsx)(u.Y,{isOpen:q,onClose:()=>L(!1),children:(0,t.jsxs)(p.g,{children:[(0,t.jsx)(j.c,{children:"收款"}),(0,t.jsx)(f.h,{children:z&&(0,t.jsxs)("div",{className:"text-center space-y-4",children:[(0,t.jsx)("p",{className:"text-lg font-semibold",children:"扫描二维码收款"}),(0,t.jsx)("div",{className:"bg-white p-4 rounded-lg border flex justify-center",children:(0,t.jsx)(w.A,{value:z.address,size:200,className:"mx-auto"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"钱包地址"}),(0,t.jsx)("p",{className:"font-mono text-sm break-all bg-gray-100 p-2 rounded",children:z.address})]})]})}),(0,t.jsxs)(N.q,{children:[(0,t.jsx)(o.T,{variant:"light",onPress:()=>L(!1),children:"关闭"}),(0,t.jsx)(o.T,{color:"primary",onPress:()=>{z&&(navigator.clipboard.writeText(z.address),alert("地址已复制到剪贴板"))},children:"复制地址"})]})]})})]})}):null}},76562:(e,s,r)=>{"use strict";r.d(s,{A:()=>n});var t=r(19605),a=r(9585),l=r(84884);function n(e){let{value:s,size:r=200,className:n=""}=e,i=(0,a.useRef)(null);return((0,a.useEffect)(()=>{i.current&&s&&l.toCanvas(i.current,s,{width:r,margin:2,color:{dark:"#000000",light:"#FFFFFF"}}).catch(e=>{console.error("生成二维码失败:",e)})},[s,r]),s)?(0,t.jsx)("canvas",{ref:i,className:"border rounded-lg ".concat(n),style:{maxWidth:"100%",height:"auto"}}):(0,t.jsx)("div",{className:"bg-gray-100 flex items-center justify-center ".concat(n),style:{width:r,height:r},children:(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"无效地址"})})}},94661:(e,s,r)=>{"use strict";r.d(s,{q:()=>c});var t=r(10001),a=r(3208),l=r(23883),n=r(18884),i=r(19605),o=(0,a.Rf)((e,s)=>{let{as:r,children:a,className:o,...c}=e,{slots:d,classNames:m}=(0,t.k)(),x=(0,l.zD)(s);return(0,i.jsx)(r||"footer",{ref:x,className:d.footer({class:(0,n.$z)(null==m?void 0:m.footer,o)}),...c,children:a})});o.displayName="HeroUI.ModalFooter";var c=o}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,7443,4273,8688,201,6029,3748,4884,390,110,7358],()=>s(27301)),_N_E=e.O()}]);