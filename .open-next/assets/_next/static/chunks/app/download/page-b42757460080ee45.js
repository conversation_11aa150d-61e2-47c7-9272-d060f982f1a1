(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2351],{19864:(e,s,n)=>{"use strict";n.d(s,{d:()=>c});var a=n(31663),l=n(3208),r=n(23883),t=n(18884),i=n(19605),o=(0,l.Rf)((e,s)=>{var n;let{as:l,className:o,children:c,...d}=e,x=(0,r.zD)(s),{slots:m,classNames:u}=(0,a.f)(),h=(0,t.$z)(null==u?void 0:u.header,o);return(0,i.jsx)(l||"div",{ref:x,className:null==(n=m.header)?void 0:n.call(m,{class:h}),...d,children:c})});o.displayName="HeroUI.CardHeader";var c=o},55935:(e,s,n)=>{"use strict";var a=n(85383);n.o(a,"useRouter")&&n.d(s,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(s,{useSearchParams:function(){return a.useSearchParams}})},66590:(e,s,n)=>{Promise.resolve().then(n.bind(n,84377))},84377:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>f});var a=n(19605),l=n(9585),r=n(56995),t=n(19864),i=n(21598),o=n(54110),c=n(63707),d=n(55935);function x(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let s=16*Math.random()|0;return("x"==e?s:3&s|8).toString(16)})}async function m(e){try{let s=await fetch(e),n=await s.blob();return new Promise((e,s)=>{let a=new FileReader;a.onloadend=()=>{let s=a.result.split(",")[1];e(s)},a.onerror=s,a.readAsDataURL(n)})}catch(e){return console.error("获取图标Base64失败:",e),""}}async function u(e){let{appName:s,appDescription:n,appUrl:a,iconBase64:l,bundleId:r="com.tronwallet.app"}=e,t=x(),i=x(),o=l;return o||(o=await m("/icons/icon-180x180.png")),'<?xml version="1.0" encoding="UTF-8"?>\n<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">\n<plist version="1.0">\n<dict>\n    <key>PayloadContent</key>\n    <array>\n        <dict>\n            <key>FullScreen</key>\n            <true/>\n            <key>Icon</key>\n            <data>'.concat(o,"</data>\n            <key>IsRemovable</key>\n            <true/>\n            <key>Label</key>\n            <string>").concat(s,"</string>\n            <key>PayloadDescription</key>\n            <string>").concat(n,"</string>\n            <key>PayloadDisplayName</key>\n            <string>Web Clip (").concat(s,")</string>\n            <key>PayloadIdentifier</key>\n            <string>").concat(r,".webclip</string>\n            <key>PayloadType</key>\n            <string>com.apple.webClip.managed</string>\n            <key>PayloadUUID</key>\n            <string>").concat(t,"</string>\n            <key>PayloadVersion</key>\n            <integer>1</integer>\n            <key>Precomposed</key>\n            <true/>\n            <key>URL</key>\n            <string>").concat(a,"</string>\n        </dict>\n    </array>\n    <key>PayloadDescription</key>\n    <string>Install ").concat(s," on your iOS device</string>\n    <key>PayloadDisplayName</key>\n    <string>").concat(s,"</string>\n    <key>PayloadIdentifier</key>\n    <string>").concat(r,"</string>\n    <key>PayloadOrganization</key>\n    <string>").concat(s,"</string>\n    <key>PayloadRemovalDisallowed</key>\n    <false/>\n    <key>PayloadType</key>\n    <string>Configuration</string>\n    <key>PayloadUUID</key>\n    <string>").concat(i,"</string>\n    <key>PayloadVersion</key>\n    <integer>1</integer>\n</dict>\n</plist>")}function h(){return/iPad|iPhone|iPod/.test(navigator.userAgent)}function p(){let e=navigator.userAgent.toLowerCase();return e.includes("safari")&&!e.includes("chrome")&&!e.includes("crios")&&!e.includes("edg")&&!e.includes("vivaldi")}async function g(e){if(!h())throw Error("此功能仅适用于iOS设备");if(window.matchMedia("(display-mode: standalone)").matches||!0===window.navigator.standalone)return alert("应用已经安装在您的设备上！"),!0;if(!p())return alert("请使用Safari浏览器进行安装以获得最佳体验"),!1;try{let s=await u(e);return!function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"app.mobileconfig";try{let n=new Blob([e],{type:"application/x-apple-aspen-config"}),a=URL.createObjectURL(n),l=document.createElement("a");l.href=a,l.download=s,l.style.display="none",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a),console.log("Mobile Configuration文件下载成功")}catch(e){throw console.error("下载Mobile Configuration文件失败:",e),e}}(s,"".concat(e.appName,".mobileconfig")),setTimeout(()=>{alert('配置文件已下载。请在"设置"应用中安装 '.concat(e.appName,' 配置文件，或者点击Safari底部的分享按钮\uD83D\uDCE4，然后选择"添加到主屏幕"。'))},500),!0}catch(s){return console.error("iOS WebClip安装失败:",s),alert('请点击Safari底部的分享按钮\uD83D\uDCE4，然后选择"添加到主屏幕"来安装'.concat(e.appName,"应用。")),!1}}async function y(){return g({appName:"TRON钱包",appDescription:"安全、便捷的TRON区块链钱包应用",appUrl:window.location.origin,bundleId:"com.tronwallet.app"})}function f(){let e=(0,d.useRouter)(),[s,n]=(0,l.useState)(null),[x,m]=(0,l.useState)(!1),[u,g]=(0,l.useState)(!1),[f,j]=(0,l.useState)(null),[N,v]=(0,l.useState)({isIOS:!1,isAndroid:!1,isMobile:!1,browser:""});(0,l.useEffect)(()=>{let e=navigator.userAgent,s=/iPad|iPhone|iPod/.test(e),a=/Android/.test(e),l=/Mobi|Android/i.test(e),r="Unknown";e.includes("Chrome")?r="Chrome":e.includes("Firefox")?r="Firefox":e.includes("Safari")?r="Safari":e.includes("Edge")&&(r="Edge"),v({isIOS:s,isAndroid:a,isMobile:l,browser:r}),window.matchMedia("(display-mode: standalone)").matches&&g(!0);let t=e=>{console.log("PWA安装提示事件触发"),e.preventDefault(),n(e),m(!0),j(null)},i=()=>{console.log("PWA安装完成"),g(!0),m(!1),n(null),j(null)};return window.addEventListener("beforeinstallprompt",t),window.addEventListener("appinstalled",i),console.log("检查PWA安装条件:",{isAndroid:a,browser:r,isSecure:"https:"===location.protocol||"localhost"===location.hostname,hasServiceWorker:"serviceWorker"in navigator,hasManifest:null!==document.querySelector('link[rel="manifest"]')}),()=>{window.removeEventListener("beforeinstallprompt",t),window.removeEventListener("appinstalled",i)}},[]);let b=async()=>{if(s)try{await s.prompt();let e=await s.userChoice;"accepted"===e.outcome?console.log("用户接受了PWA安装"):console.log("用户拒绝了PWA安装"),n(null),m(!1)}catch(e){console.error("PWA安装失败:",e)}},w=async()=>{if(!h())return void alert("此功能仅适用于iOS设备");if(!p())return void alert("请使用Safari浏览器进行安装以获得最佳体验");try{await y()?console.log("iOS WebClip安装成功"):console.log("iOS WebClip安装需要用户手动操作")}catch(e){console.error("iOS WebClip安装失败:",e),alert('请点击Safari底部的分享按钮\uD83D\uDCE4，然后选择"添加到主屏幕"来安装TRON钱包应用')}},D=async()=>{if(console.log("开始Android PWA安装"),j(null),!N.isAndroid){let e="此功能仅适用于Android设备";j(e),alert(e);return}if(u)return void alert("应用已经安装在您的设备上！");try{if(s){console.log("使用PWA安装提示"),await s.prompt();let e=await s.userChoice;console.log("用户选择结果:",e.outcome),"accepted"===e.outcome?console.log("用户接受了PWA安装"):(console.log("用户拒绝了PWA安装"),j("用户取消了安装")),n(null),m(!1)}else{console.log("没有PWA安装提示，引导手动安装");let e="",s=navigator.userAgent;e=s.includes("Chrome")?'请按以下步骤安装TRON钱包：\n\n\uD83D\uDD39 方法一：地址栏安装\n1. 查看地址栏右侧的安装图标 ⬇️\n2. 点击安装图标\n3. 确认安装\n\n\uD83D\uDD39 方法二：菜单安装\n1. 点击右上角菜单 ⋮\n2. 选择"安装应用"或"添加到主屏幕"\n3. 确认安装':s.includes("Firefox")?'请按以下步骤安装TRON钱包：\n\n1. 点击地址栏右侧的"+"图标\n2. 选择"安装此站点为应用"\n3. 确认安装':s.includes("Edge")?'请按以下步骤安装TRON钱包：\n\n1. 点击地址栏右侧的安装图标\n2. 或通过菜单选择"应用"→"安装此站点为应用"\n3. 确认安装':'请通过浏览器菜单中的"添加到主屏幕"或"安装应用"选项来安装TRON钱包。\n\n建议使用Chrome浏览器获得最佳体验。',alert(e),j("需要手动安装")}}catch(s){console.error("Android PWA安装失败:",s);let e=s.message||"安装失败，请重试";j(e),alert("安装失败: ".concat(e,"\n\n请尝试通过浏览器菜单手动安装应用。"))}},P=async()=>{s?await b():alert('\n\uD83D\uDCBB 在桌面端安装TRON钱包：\n\nChrome/Edge浏览器：\n1️⃣ 点击地址栏右侧的安装图标 ⬇️\n2️⃣ 或点击右上角菜单 → "安装TRON钱包"\n3️⃣ 确认安装\n\nFirefox浏览器：\n1️⃣ 点击地址栏右侧的"+"图标\n2️⃣ 选择"安装此站点为应用"\n3️⃣ 确认安装\n\n✨ 安装后您将获得：\n• 桌面应用图标\n• 独立窗口运行\n• 快速启动\n• 系统集成\n      ')},k=N.isIOS?{title:"iOS Safari 安装说明",steps:["点击底部的分享按钮 \uD83D\uDCE4",'向下滚动找到"添加到主屏幕"','点击"添加到主屏幕"',"确认安装，应用将出现在主屏幕上"],note:"iOS设备需要使用Safari浏览器才能安装PWA"}:N.isAndroid?{title:"Android 安装说明",steps:['点击下方的"安装应用"按钮','或者点击浏览器菜单中的"安装应用"',"确认安装提示","应用将添加到主屏幕和应用抽屉"],note:"Android设备推荐使用Chrome浏览器获得最佳体验"}:{title:"桌面端安装说明",steps:["点击地址栏右侧的安装图标",'或者点击下方的"安装应用"按钮',"确认安装提示","应用将添加到桌面和开始菜单"],note:"桌面端推荐使用Chrome、Edge或Firefox浏览器"};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsx)(r.Z,{children:(0,a.jsx)(t.d,{children:(0,a.jsxs)("div",{className:"text-center w-full",children:[(0,a.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"下载 TRON 钱包"}),(0,a.jsx)("p",{className:"text-gray-600",children:"安装到您的设备，享受原生应用体验"})]})})}),(0,a.jsx)(r.Z,{className:u?"bg-green-50":"bg-blue-50",children:(0,a.jsx)(i.U,{className:"text-center",children:u?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl mb-3",children:"✅"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-green-800 mb-2",children:"应用已安装"}),(0,a.jsx)("p",{className:"text-green-700 mb-4",children:"TRON钱包已成功安装到您的设备上"}),(0,a.jsx)(o.T,{color:"success",onPress:()=>e.push("/wallet"),className:"w-full sm:w-auto",children:"打开钱包"})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDCF1"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-blue-800 mb-2",children:"安装 PWA 应用"}),(0,a.jsx)("p",{className:"text-blue-700 mb-4",children:"将TRON钱包安装到您的设备，获得更好的使用体验"}),(0,a.jsxs)("div",{className:"space-y-3",children:[N.isIOS&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.T,{color:"primary",size:"lg",onPress:w,className:"w-full",startContent:(0,a.jsx)("span",{className:"text-xl",children:"\uD83C\uDF4E"}),children:"安装到 iOS 主屏幕"}),!p()&&(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(c.R,{color:"warning",variant:"flat",size:"sm",children:"请使用 Safari 浏览器安装！"})})]}),N.isAndroid&&(0,a.jsx)(o.T,{color:"primary",size:"lg",onPress:D,className:"w-full",startContent:(0,a.jsx)("span",{className:"text-xl",children:"\uD83E\uDD16"}),children:"安装 Android 应用"}),!N.isMobile&&(0,a.jsx)(o.T,{color:"primary",size:"lg",onPress:P,className:"w-full",startContent:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDCBB"}),children:x?"桌面端 安装PWA应用":"桌面端 安装指导"}),x&&(0,a.jsx)(o.T,{color:"secondary",size:"lg",onPress:b,className:"w-full",variant:"bordered",startContent:(0,a.jsx)("span",{className:"text-xl",children:"⬇️"}),children:"通用PWA安装"})]}),f&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-red-800 text-sm",children:[(0,a.jsx)("span",{className:"font-medium",children:"安装提示: "}),f]})})]})})}),(0,a.jsxs)(r.Z,{children:[(0,a.jsx)(t.d,{children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"设备信息"})}),(0,a.jsx)(i.U,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"设备类型:"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(c.R,{size:"sm",color:N.isMobile?"primary":"default",variant:"flat",children:N.isIOS?"iOS":N.isAndroid?"Android":N.isMobile?"移动设备":"桌面设备"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"浏览器:"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(c.R,{size:"sm",color:["Chrome","Edge","Safari"].includes(N.browser)?"success":"warning",variant:"flat",children:N.browser})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"PWA支持:"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(c.R,{size:"sm",color:x||N.isIOS?"success":"warning",variant:"flat",children:x||N.isIOS?"支持":"有限支持"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"安装状态:"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(c.R,{size:"sm",color:u?"success":"default",variant:"flat",children:u?"已安装":"未安装"})})]})]})})]}),!u&&(0,a.jsxs)(r.Z,{children:[(0,a.jsx)(t.d,{children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:k.title})}),(0,a.jsxs)(i.U,{children:[(0,a.jsx)("ol",{className:"list-decimal list-inside space-y-2 text-sm mb-4",children:k.steps.map((e,s)=>(0,a.jsx)("li",{className:"text-gray-700",children:e},s))}),(0,a.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"text-yellow-800 text-sm",children:["\uD83D\uDCA1 ",k.note]})})]})]}),(0,a.jsxs)(r.Z,{children:[(0,a.jsx)(t.d,{children:(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"PWA应用优势"})}),(0,a.jsx)(i.U,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:"⚡"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:"快速启动"}),(0,a.jsx)("p",{className:"text-gray-600",children:"像原生应用一样快速启动"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCF1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:"原生体验"}),(0,a.jsx)("p",{className:"text-gray-600",children:"全屏显示，无浏览器界面"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDD12"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:"安全可靠"}),(0,a.jsx)("p",{className:"text-gray-600",children:"HTTPS加密，数据安全"})]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCBE"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold",children:"离线缓存"}),(0,a.jsx)("p",{className:"text-gray-600",children:"部分功能支持离线使用"})]})]})]})})]}),(0,a.jsx)(r.Z,{children:(0,a.jsx)(i.U,{children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)(o.T,{color:"primary",variant:"bordered",onPress:()=>e.push("/wallet"),className:"flex-1",children:"直接使用网页版"}),(0,a.jsx)(o.T,{color:"secondary",variant:"bordered",onPress:()=>e.push("/"),className:"flex-1",children:"返回首页"})]})})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,390,110,7358],()=>s(66590)),_N_E=e.O()}]);