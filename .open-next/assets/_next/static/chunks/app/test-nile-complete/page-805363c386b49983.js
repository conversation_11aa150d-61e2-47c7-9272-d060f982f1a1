(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5956],{21598:(e,s,a)=>{"use strict";a.d(s,{U:()=>o});var l=a(31663),r=a(3208),t=a(23883),i=a(18884),n=a(19605),d=(0,r.Rf)((e,s)=>{var a;let{as:r,className:d,children:o,...c}=e,u=(0,t.zD)(s),{slots:h,classNames:b}=(0,l.f)(),m=(0,i.$z)(null==b?void 0:b.body,d);return(0,n.jsx)(r||"div",{ref:u,className:null==(a=h.body)?void 0:a.call(h,{class:m}),...c,children:o})});d.displayName="HeroUI.CardBody";var o=d},28817:(e,s,a)=>{"use strict";a.d(s,{o:()=>r});var l=a(19605),r=e=>(0,l.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,l.jsx)("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})})},31663:(e,s,a)=>{"use strict";a.d(s,{f:()=>r,u:()=>l});var[l,r]=(0,a(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},51414:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var l=a(19605),r=a(9585),t=a(56995),i=a(19864),n=a(21598),d=a(66700),o=a(54110),c=a(89213);function u(){let[e,s]=(0,r.useState)("TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"),[a,u]=(0,r.useState)(!1),[h,b]=(0,r.useState)(null),m=async()=>{u(!0);try{let e=await fetch("/api/test-nile-wallet"),s=await e.json();b(a=>({...a,walletTest:{success:e.ok,data:s,timestamp:new Date().toLocaleString()}}))}catch(e){b(s=>({...s,walletTest:{success:!1,error:e,timestamp:new Date().toLocaleString()}}))}finally{u(!1)}},x=async s=>{u(!0);try{let a=await fetch("/api/wallets/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({address:e,currency:s,network:"nile"})}),l=await a.json();b(e=>({...e,["".concat(s,"Balance")]:{success:a.ok,data:l,timestamp:new Date().toLocaleString()}}))}catch(e){b(a=>({...a,["".concat(s,"Balance")]:{success:!1,error:e,timestamp:new Date().toLocaleString()}}))}finally{u(!1)}};return(0,l.jsx)("div",{className:"container mx-auto p-4 max-w-6xl",children:(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"Nile网络完整功能测试"})}),(0,l.jsxs)(n.U,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h2",{className:"text-lg font-semibold",children:"测试控制"})}),(0,l.jsxs)(n.U,{className:"space-y-4",children:[(0,l.jsx)(d.r,{label:"测试地址",value:e,onChange:e=>s(e.target.value),placeholder:"输入Nile网络TRON地址",description:"默认为Nile测试网USDT合约地址"}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsx)(o.T,{color:"primary",onPress:m,isLoading:a,className:"w-full",children:"测试钱包创建"}),(0,l.jsx)(o.T,{color:"secondary",onPress:()=>x("TRX"),isLoading:a,className:"w-full",children:"测试TRX余额"}),(0,l.jsx)(o.T,{color:"success",onPress:()=>x("USDT"),isLoading:a,className:"w-full",children:"测试USDT余额"})]}),(0,l.jsx)(c.y,{}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,l.jsx)("h3",{className:"font-semibold mb-2",children:"Nile测试网信息"}),(0,l.jsxs)("ul",{className:"space-y-1",children:[(0,l.jsx)("li",{children:"• 网络: Nile Testnet"}),(0,l.jsx)("li",{children:"• 浏览器: nile.tronscan.org"}),(0,l.jsx)("li",{children:"• USDT合约: TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"}),(0,l.jsx)("li",{children:"• 免费TRX: 可从水龙头获取"})]})]})]})]}),(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h2",{className:"text-lg font-semibold",children:"网络状态"})}),(0,l.jsx)(n.U,{children:(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{children:"网络:"}),(0,l.jsx)("span",{className:"text-blue-600 font-medium",children:"Nile Testnet"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{children:"状态:"}),(0,l.jsx)("span",{className:"text-green-600 font-medium",children:"正常"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{children:"API密钥:"}),(0,l.jsx)("span",{className:"text-green-600 font-medium",children:"已配置"})]})]})})]})]}),h&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold",children:"测试结果"}),h.walletTest&&(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h3",{className:"text-md font-semibold",children:"钱包创建测试"})}),(0,l.jsx)(n.U,{children:(0,l.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64",children:JSON.stringify(h.walletTest,null,2)})})]}),h.TRXBalance&&(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h3",{className:"text-md font-semibold",children:"TRX余额测试"})}),(0,l.jsx)(n.U,{children:(0,l.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64",children:JSON.stringify(h.TRXBalance,null,2)})})]}),h.USDTBalance&&(0,l.jsxs)(t.Z,{children:[(0,l.jsx)(i.d,{children:(0,l.jsx)("h3",{className:"text-md font-semibold",children:"USDT余额测试"})}),(0,l.jsx)(n.U,{children:(0,l.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64",children:JSON.stringify(h.USDTBalance,null,2)})})]})]}),(0,l.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,l.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"测试说明"}),(0,l.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,l.jsx)("li",{children:"• 钱包创建测试：验证Nile网络钱包创建和余额查询"}),(0,l.jsx)("li",{children:"• TRX余额测试：验证Nile网络TRX余额查询功能"}),(0,l.jsx)("li",{children:"• USDT余额测试：验证Nile网络USDT余额查询功能"}),(0,l.jsx)("li",{children:"• 所有测试都使用Nile测试网络，不会产生实际费用"})]})]})]})]})})}},56995:(e,s,a)=>{"use strict";a.d(s,{Z:()=>w});var l=a(31663),r=a(92610),t=a(56457),i=(0,r.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),n=a(9585),d=a(26423),o=a(96539),c=a(7484),u=a(85823),h=a(90890),b=a(31081),m=a(3208),x=a(18884),f=a(9733),p=a(23883),j=a(32965),v=a(14171),g=a(19605),N=(0,m.Rf)((e,s)=>{let{children:a,context:r,Component:t,isPressable:N,disableAnimation:w,disableRipple:y,getCardProps:T,getRippleProps:k}=function(e){var s,a,l,r;let t=(0,b.o)(),[v,g]=(0,m.rE)(e,i.variantKeys),{ref:N,as:w,children:y,onClick:T,onPress:k,autoFocus:D,className:S,classNames:P,allowTextSelectionOnPress:U=!0,...C}=v,R=(0,p.zD)(N),B=w||(e.isPressable?"button":"div"),X="string"==typeof B,Z=null!=(a=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&a,z=null!=(r=null!=(l=e.disableRipple)?l:null==t?void 0:t.disableRipple)&&r,H=(0,x.$z)(null==P?void 0:P.base,S),{onClear:E,onPress:A,ripples:M}=(0,j.k)(),O=(0,n.useCallback)(e=>{z||Z||R.current&&A(e)},[z,Z,R,A]),{buttonProps:L,isPressed:_}=(0,h.l)({onPress:(0,d.c)(k,O),elementType:w,isDisabled:!e.isPressable,onClick:T,allowTextSelectionOnPress:U,...C},R),{hoverProps:F,isHovered:I}=(0,u.M)({isDisabled:!e.isHoverable,...C}),{isFocusVisible:V,isFocused:W,focusProps:$}=(0,c.o)({autoFocus:D}),J=(0,n.useMemo)(()=>i({...g,disableAnimation:Z}),[(0,x.t6)(g),Z]),Y=(0,n.useMemo)(()=>({slots:J,classNames:P,disableAnimation:Z,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[J,P,e.isDisabled,e.isFooterBlurred,Z,e.fullWidth]),G=(0,n.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:R,className:J.base({class:H}),tabIndex:e.isPressable?0:-1,"data-hover":(0,x.sE)(I),"data-pressed":(0,x.sE)(_),"data-focus":(0,x.sE)(W),"data-focus-visible":(0,x.sE)(V),"data-disabled":(0,x.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...L,...$,role:"button"}:{},e.isHoverable?F:{},(0,f.$)(C,{enabled:X}),(0,f.$)(s))}},[R,J,H,X,e.isPressable,e.isHoverable,e.isDisabled,I,_,V,L,$,F,C]),q=(0,n.useCallback)(()=>({ripples:M,onClear:E}),[M,E]);return{context:Y,domRef:R,Component:B,classNames:P,children:y,isHovered:I,isPressed:_,disableAnimation:Z,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:z,handlePress:O,isFocusVisible:V,getCardProps:G,getRippleProps:q}}({...e,ref:s});return(0,g.jsxs)(t,{...T(),children:[(0,g.jsx)(l.u,{value:r,children:a}),N&&!w&&!y&&(0,g.jsx)(v.j,{...k()})]})});N.displayName="HeroUI.Card";var w=N},64991:(e,s,a)=>{Promise.resolve().then(a.bind(a,51414))},89213:(e,s,a)=>{"use strict";a.d(s,{y:()=>o});var l=a(9733),r=(0,a(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),t=a(9585),i=a(3208),n=a(19605),d=(0,i.Rf)((e,s)=>{let{Component:a,getDividerProps:i}=function(e){var s;let a,i,{as:n,className:d,orientation:o,...c}=e,u=n||"hr";"hr"===u&&"vertical"===o&&(u="div");let{separatorProps:h}=(s={elementType:"string"==typeof u?u:"hr",orientation:o},i=(0,l.$)(s,{enabled:"string"==typeof s.elementType}),("vertical"===s.orientation&&(a="vertical"),"hr"!==s.elementType)?{separatorProps:{...i,role:"separator","aria-orientation":a}}:{separatorProps:i}),b=(0,t.useMemo)(()=>r({orientation:o,className:d}),[o,d]);return{Component:u,getDividerProps:(0,t.useCallback)((e={})=>({className:b,role:"separator","data-orientation":o,...h,...c,...e}),[b,o,h,c])}}({...e});return(0,n.jsx)(a,{ref:s,...i()})});d.displayName="HeroUI.Divider";var o=d}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,7443,390,110,7358],()=>s(64991)),_N_E=e.O()}]);