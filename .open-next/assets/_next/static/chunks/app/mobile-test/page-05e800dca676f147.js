(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4500],{39546:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});var a=l(19605),t=l(66700),i=l(9585);function r(e){var s,l,r,n;let{preventZoom:c=!0,...d}=e,x=(0,i.useRef)(null);return(0,i.useEffect)(()=>{if(!c)return;let e=()=>{let e=document.querySelector("meta[name=viewport]");e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover"),x.current&&16>parseFloat(window.getComputedStyle(x.current).fontSize)&&(x.current.style.fontSize="16px")},s=()=>{setTimeout(()=>{let e=document.querySelector("meta[name=viewport]");e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover")},300)},l=x.current;if(l)return l.addEventListener("focus",e),l.addEventListener("blur",s),()=>{l.removeEventListener("focus",e),l.removeEventListener("blur",s)}},[c]),(0,a.jsx)(t.r,{...d,ref:x,classNames:{input:["text-base","sm:text-sm",...Array.isArray(null==(s=d.classNames)?void 0:s.input)?d.classNames.input:[null==(l=d.classNames)?void 0:l.input].filter(Boolean)],inputWrapper:[...Array.isArray(null==(r=d.classNames)?void 0:r.inputWrapper)?d.classNames.inputWrapper:[null==(n=d.classNames)?void 0:n.inputWrapper].filter(Boolean)]},style:{fontSize:"16px",...d.style}})}},49038:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>u});var a=l(19605),t=l(9585),i=l(56995),r=l(19864),n=l(21598),c=l(66700),d=l(49731),x=l(57059),o=l(5230),h=l(54110),m=l(39546);function u(){let[e,s]=(0,t.useState)({text:"",email:"",password:"",number:"",textarea:"",select:""});return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 px-4",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto space-y-4",children:[(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h1",{className:"text-xl font-bold text-center w-full",children:"移动端输入框测试"})}),(0,a.jsx)(n.U,{className:"space-y-4",children:(0,a.jsx)("div",{className:"text-center text-sm text-gray-600",children:(0,a.jsx)("p",{children:"在移动设备上测试输入框是否会导致页面缩放"})})})]}),(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"标准输入框"})}),(0,a.jsxs)(n.U,{className:"space-y-4",children:[(0,a.jsx)(c.r,{label:"文本输入",placeholder:"点击测试是否缩放",value:e.text,onChange:l=>s({...e,text:l.target.value})}),(0,a.jsx)(c.r,{label:"邮箱输入",type:"email",placeholder:"输入邮箱地址",value:e.email,onChange:l=>s({...e,email:l.target.value})}),(0,a.jsx)(c.r,{label:"密码输入",type:"password",placeholder:"输入密码",value:e.password,onChange:l=>s({...e,password:l.target.value})}),(0,a.jsx)(c.r,{label:"数字输入",type:"number",placeholder:"输入数字",value:e.number,onChange:l=>s({...e,number:l.target.value})})]})]}),(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"移动端优化输入框"})}),(0,a.jsxs)(n.U,{className:"space-y-4",children:[(0,a.jsx)(m.A,{label:"优化文本输入",placeholder:"防缩放输入框",value:e.text,onChange:l=>s({...e,text:l.target.value})}),(0,a.jsx)(m.A,{label:"优化邮箱输入",type:"email",placeholder:"防缩放邮箱输入",value:e.email,onChange:l=>s({...e,email:l.target.value})}),(0,a.jsx)(m.A,{label:"优化密码输入",type:"password",placeholder:"防缩放密码输入",value:e.password,onChange:l=>s({...e,password:l.target.value})})]})]}),(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"其他表单元素"})}),(0,a.jsxs)(n.U,{className:"space-y-4",children:[(0,a.jsx)(d.P,{label:"文本域",placeholder:"输入多行文本",value:e.textarea,onChange:l=>s({...e,textarea:l.target.value}),classNames:{input:"text-base sm:text-sm"}}),(0,a.jsxs)(x.d,{label:"选择框",placeholder:"选择一个选项",selectedKeys:e.select?[e.select]:[],onSelectionChange:l=>{let a=Array.from(l)[0];s({...e,select:a})},children:[(0,a.jsx)(o.y,{children:"选项1"},"option1"),(0,a.jsx)(o.y,{children:"选项2"},"option2"),(0,a.jsx)(o.y,{children:"选项3"},"option3")]}),(0,a.jsx)(h.T,{color:"primary",className:"w-full",onPress:()=>alert("按钮点击测试"),children:"测试按钮"})]})]}),(0,a.jsxs)(i.Z,{className:"bg-blue-50",children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-blue-800",children:"测试说明"})}),(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"移动端测试步骤："}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,a.jsx)("li",{children:"在移动设备上打开此页面"}),(0,a.jsx)("li",{children:"点击各种输入框"}),(0,a.jsx)("li",{children:"观察页面是否发生缩放"}),(0,a.jsx)("li",{children:"测试输入和编辑功能"}),(0,a.jsx)("li",{children:"测试按钮和选择框"})]}),(0,a.jsx)("h3",{className:"font-semibold mt-4",children:"期望结果："}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"点击输入框时页面不缩放"}),(0,a.jsx)("li",{children:"输入框可以正常输入文字"}),(0,a.jsx)("li",{children:"键盘弹出时布局正常"}),(0,a.jsx)("li",{children:"所有交互功能正常工作"})]})]})})]}),(0,a.jsxs)(i.Z,{className:"bg-green-50",children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-green-800",children:"技术实现"})}),(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"text-sm text-green-700 space-y-2",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"防缩放措施："}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"viewport meta标签设置user-scalable=no"}),(0,a.jsx)("li",{children:"输入框字体大小设为16px（iOS要求）"}),(0,a.jsx)("li",{children:"CSS防缩放样式"}),(0,a.jsx)("li",{children:"JavaScript事件监听和处理"}),(0,a.jsx)("li",{children:"专门的移动端输入框组件"})]}),(0,a.jsx)("h3",{className:"font-semibold mt-4",children:"兼容性："}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"iOS Safari"}),(0,a.jsx)("li",{children:"Android Chrome"}),(0,a.jsx)("li",{children:"移动端其他浏览器"}),(0,a.jsx)("li",{children:"桌面端正常显示"})]})]})})]}),(0,a.jsxs)(i.Z,{className:"bg-gray-50",children:[(0,a.jsx)(r.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"当前表单数据"})}),(0,a.jsx)(n.U,{children:(0,a.jsx)("pre",{className:"text-xs bg-white p-2 rounded overflow-auto",children:JSON.stringify(e,null,2)})})]})]})})}},92593:(e,s,l)=>{Promise.resolve().then(l.bind(l,49038))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,7443,4273,8688,201,6029,7740,390,110,7358],()=>s(92593)),_N_E=e.O()}]);