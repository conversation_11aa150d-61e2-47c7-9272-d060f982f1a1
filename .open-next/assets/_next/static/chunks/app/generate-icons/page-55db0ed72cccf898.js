(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8839],{3351:(e,t,l)=>{"use strict";l.d(t,{C:()=>g,Y:()=>b});let r=new Set(["Arab","<PERSON>yrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),s=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function a(e){if(Intl.Locale){let t=new Intl.Locale(e).maximize(),l="function"==typeof t.getTextInfo?t.getTextInfo():t.textInfo;if(l)return"rtl"===l.direction;if(t.script)return r.has(t.script)}let t=e.split("-")[0];return s.has(t)}var n=l(9585),c=l(37285);let i=Symbol.for("react-aria.i18n.locale");function o(){let e="undefined"!=typeof window&&window[i]||"undefined"!=typeof navigator&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:a(e)?"rtl":"ltr"}}let d=o(),u=new Set;function h(){for(let e of(d=o(),u))e(d)}function m(){let e=(0,c.wR)(),[t,l]=(0,n.useState)(d);return((0,n.useEffect)(()=>(0===u.size&&window.addEventListener("languagechange",h),u.add(l),()=>{u.delete(l),0===u.size&&window.removeEventListener("languagechange",h)}),[]),e)?{locale:"en-US",direction:"ltr"}:t}let x=n.createContext(null);function g(e){let{locale:t,children:l}=e,r=m(),s=n.useMemo(()=>t?{locale:t,direction:a(t)?"rtl":"ltr"}:r,[r,t]);return n.createElement(x.Provider,{value:s},l)}function b(){let e=m();return(0,n.useContext)(x)||e}},15805:(e,t,l)=>{"use strict";l.d(t,{a:()=>s});var r=l(9585);function s(e={}){let{rerender:t=!1,delay:l=0}=e,a=(0,r.useRef)(!1),[n,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{a.current=!0;let e=null;return t&&(l>0?e=setTimeout(()=>{c(!0)},l):c(!0)),()=>{a.current=!1,t&&c(!1),e&&clearTimeout(e)}},[t]),[(0,r.useCallback)(()=>a.current,[]),n]}},19864:(e,t,l)=>{"use strict";l.d(t,{d:()=>o});var r=l(31663),s=l(3208),a=l(23883),n=l(18884),c=l(19605),i=(0,s.Rf)((e,t)=>{var l;let{as:s,className:i,children:o,...d}=e,u=(0,a.zD)(t),{slots:h,classNames:m}=(0,r.f)(),x=(0,n.$z)(null==m?void 0:m.header,i);return(0,c.jsx)(s||"div",{ref:u,className:null==(l=h.header)?void 0:l.call(h,{class:x}),...d,children:o})});i.displayName="HeroUI.CardHeader";var o=i},28443:(e,t,l)=>{"use strict";function r(e,t=-1/0,l=1/0){return Math.min(Math.max(e,t),l)}function s(e,t){let l=e,r=t.toString(),s=r.indexOf("."),a=s>=0?r.length-s:0;if(a>0){let e=Math.pow(10,a);l=Math.round(l*e)/e}return l}function a(e,t,l,r){t=Number(t),l=Number(l);let a=(e-(isNaN(t)?0:t))%r,n=s(2*Math.abs(a)>=r?e+Math.sign(a)*(r-Math.abs(a)):e-a,r);return isNaN(t)?!isNaN(l)&&n>l&&(n=Math.floor(s(l/r,r))*r):n<t?n=t:!isNaN(l)&&n>l&&(n=t+Math.floor(s((l-t)/r,r))*r),n=s(n,r)}l.d(t,{BU:()=>a,qE:()=>r})},52114:(e,t,l)=>{Promise.resolve().then(l.bind(l,57836))},57836:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>f});var r=l(19605),s=l(9585),a=l(56995),n=l(19864),c=l(21598),i=l(63707),o=l(88865),d=l(54110);let u=[16,32,57,60,72,76,96,114,120,128,144,152,180,192,384,512];async function h(e,t){let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"transparent",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return new Promise((s,a)=>{try{let n=new Blob([e],{type:"image/svg+xml"}),c=URL.createObjectURL(n),i=new Image;i.onload=()=>{try{let e=document.createElement("canvas"),n=e.getContext("2d");if(!n)return void a(Error("无法获取Canvas上下文"));e.width=t,e.height=t,"transparent"!==l&&(n.fillStyle=l,n.fillRect(0,0,t,t));let o=t-2*r;n.drawImage(i,r,r,o,o),e.toBlob(e=>{URL.revokeObjectURL(c),e?s(e):a(Error("Canvas转换为Blob失败"))},"image/png",1)}catch(e){URL.revokeObjectURL(c),a(e)}},i.onerror=()=>{URL.revokeObjectURL(c),a(Error("SVG图像加载失败"))},i.src=c}catch(e){a(e)}})}function m(e,t){let l=URL.createObjectURL(e),r=document.createElement("a");r.href=l,r.download=t,r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(l)}async function x(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{backgroundColor:l="transparent",padding:r=0,downloadAll:s=!1}=t,a=[];for(let t of(console.log("\uD83D\uDE80 开始生成PWA图标..."),u))try{console.log("⏳ 生成 ".concat(t,"x").concat(t," 图标..."));let n=await h(e,t,l,r),c="icon-".concat(t,"x").concat(t,".png");a.push({size:t,blob:n,filename:c}),s&&m(n,c),console.log("✅ ".concat(c," 生成成功 (").concat(Math.round(n.size/1024),"KB)"))}catch(e){console.error("❌ 生成 ".concat(t,"x").concat(t," 图标失败:"),e)}return console.log("\uD83C\uDF89 完成！共生成 ".concat(a.length," 个图标")),a}async function g(e){try{let t=await fetch(e);if(!t.ok)throw Error("获取SVG失败: ".concat(t.statusText));return await t.text()}catch(e){throw console.error("获取SVG内容失败:",e),e}}async function b(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{console.log("\uD83D\uDCE5 获取SVG内容...");let l=await g(e);if(console.log("\uD83D\uDD0D 验证SVG格式..."),!function(e){try{let t=new DOMParser().parseFromString(e,"image/svg+xml"),l=t.querySelector("parsererror");if(l)return console.error("SVG解析错误:",l.textContent),!1;if(!t.querySelector("svg"))return console.error("未找到SVG元素"),!1;return!0}catch(e){return console.error("SVG验证失败:",e),!1}}(l))throw Error("SVG格式无效");return console.log("✅ SVG验证通过"),await x(l,t)}catch(e){throw console.error("生成图标失败:",e),e}}function f(){let[e,t]=(0,s.useState)(!1),[l,h]=(0,s.useState)(0),[x,g]=(0,s.useState)([]),[f,j]=(0,s.useState)(null),p=async()=>{t(!0),h(0),j(null),g([]);try{console.log("\uD83D\uDE80 开始生成图标...");let e=(await b("/icons/logo.svg",{backgroundColor:"transparent",padding:0,downloadAll:!1})).map(e=>({...e,url:URL.createObjectURL(e.blob)}));g(e),h(100),console.log("\uD83C\uDF89 所有图标生成完成！")}catch(e){console.error("生成图标失败:",e),j(e.message||"生成图标失败")}finally{t(!1)}},v=e=>{m(e.blob,e.filename)},y=e=>"".concat(Math.round(e/1024),"KB");return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsx)(a.Z,{children:(0,r.jsx)(n.d,{children:(0,r.jsxs)("div",{className:"text-center w-full",children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"PWA 图标生成器"}),(0,r.jsx)("p",{className:"text-gray-600",children:"从SVG生成所有PWA所需的PNG图标尺寸"})]})})}),(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"源SVG图标"})}),(0,r.jsxs)(c.U,{className:"text-center",children:[(0,r.jsxs)("div",{className:"inline-block p-4 bg-gray-100 rounded-lg",children:[(0,r.jsx)("img",{src:"/icons/logo.svg",alt:"Logo SVG",className:"w-24 h-24",onError:e=>{var t;e.currentTarget.style.display="none",null==(t=e.currentTarget.nextElementSibling)||t.classList.remove("hidden")}}),(0,r.jsx)("div",{className:"hidden text-gray-500",children:"SVG文件未找到"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"源文件: /icons/logo.svg"})]})]}),(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"生成图标"})}),(0,r.jsx)(c.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"将生成以下尺寸的PNG图标："}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:u.map(e=>(0,r.jsxs)(i.R,{size:"sm",variant:"flat",children:[e,"\xd7",e]},e))})]}),e&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.o,{value:l,color:"primary",showValueLabel:!0,label:"生成进度"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"正在生成图标..."})]}),f&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-red-800 font-medium",children:"生成失败"}),(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:f})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(d.T,{color:"primary",onPress:p,isLoading:e,disabled:e,children:e?"生成中...":"生成所有图标"}),x.length>0&&(0,r.jsx)(d.T,{color:"success",variant:"bordered",onPress:()=>{x.forEach(e=>{setTimeout(()=>{m(e.blob,e.filename)},100*e.size)})},children:"下载所有图标"})]})]})})]}),x.length>0&&(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(n.d,{children:(0,r.jsxs)("h2",{className:"text-lg font-semibold",children:["生成结果 (",x.length," 个图标)"]})}),(0,r.jsx)(c.U,{children:(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4",children:x.map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"bg-gray-100 rounded-lg p-2 mb-2",children:e.url&&(0,r.jsx)("img",{src:e.url,alt:"".concat(e.size,"x").concat(e.size),className:"w-full h-auto max-w-16 mx-auto"})}),(0,r.jsxs)("div",{className:"text-xs space-y-1",children:[(0,r.jsxs)("p",{className:"font-medium",children:[e.size,"\xd7",e.size]}),(0,r.jsx)("p",{className:"text-gray-500",children:y(e.blob.size)}),(0,r.jsx)(d.T,{size:"sm",variant:"bordered",onPress:()=>v(e),className:"text-xs h-6",children:"下载"})]})]},e.size))})})]}),(0,r.jsxs)(a.Z,{className:"bg-blue-50",children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-blue-800",children:"使用说明"})}),(0,r.jsx)(c.U,{children:(0,r.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,r.jsx)("h3",{className:"font-semibold",children:"生成步骤："}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,r.jsx)("li",{children:"确保 /icons/logo.svg 文件存在"}),(0,r.jsx)("li",{children:'点击"生成所有图标"按钮'}),(0,r.jsx)("li",{children:"等待生成完成"}),(0,r.jsx)("li",{children:"下载需要的图标文件"}),(0,r.jsx)("li",{children:"将图标文件放到 public/icons/ 目录"})]}),(0,r.jsx)("h3",{className:"font-semibold mt-4",children:"图标用途："}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,r.jsx)("li",{children:"PWA应用图标 (192\xd7192, 512\xd7512)"}),(0,r.jsx)("li",{children:"Apple Touch图标 (180\xd7180)"}),(0,r.jsx)("li",{children:"Favicon (16\xd716, 32\xd732)"}),(0,r.jsx)("li",{children:"各种设备适配图标"})]})]})})]})]})})}},61276:(e,t,l)=>{"use strict";l.d(t,{M:()=>a});var r=l(24215),s=l(74116);function a(e){let{id:t,label:l,"aria-labelledby":a,"aria-label":n,labelElementType:c="label"}=e;t=(0,r.Bi)(t);let i=(0,r.Bi)(),o={};return l&&(a=a?`${i} ${a}`:i,o={id:i,htmlFor:"label"===c?t:void 0}),{labelProps:o,fieldProps:(0,s.b)({id:t,"aria-label":n,"aria-labelledby":a})}}},74116:(e,t,l)=>{"use strict";l.d(t,{b:()=>s});var r=l(24215);function s(e,t){let{id:l,"aria-label":s,"aria-labelledby":a}=e;return l=(0,r.Bi)(l),a&&s?a=[...new Set([l,...a.trim().split(/\s+/)])].join(" "):a&&(a=a.trim().split(/\s+/).join(" ")),s||a||!t||(s=t),{id:l,"aria-label":s,"aria-labelledby":a}}}},e=>{var t=t=>e(e.s=t);e.O(0,[8031,4110,1358,8865,390,110,7358],()=>t(52114)),_N_E=e.O()}]);