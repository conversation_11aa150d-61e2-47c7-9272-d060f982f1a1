(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{18072:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>j});var a=l(19605),r=l(9585),c=l(72031),t=l(55935),i=l(46762),n=l.n(i),x=l(13983),d=l(54110),h=l(56995),m=l(21598),o=l(63707);function j(){let{user:e,loading:s}=(0,c.h)(),l=(0,t.useRouter)();return((0,r.useEffect)(()=>{!s&&e&&l.push("/wallet")},[e,s,l]),s)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(x.o,{size:"lg"})}):e?null:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:[(0,a.jsx)("nav",{className:"bg-white/80 backdrop-blur-md border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"TRON钱包"})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(d.T,{as:n(),href:"/auth/signin",color:"primary",variant:"solid",children:"登录"}),(0,a.jsx)(d.T,{as:n(),href:"/auth/register",color:"primary",variant:"bordered",children:"注册"})]})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsxs)("h1",{className:"text-5xl font-bold text-gray-900 mb-6",children:["安全便捷的",(0,a.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:"TRON钱包"})]}),(0,a.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款。 安全可靠，操作简单，让数字资产管理变得轻松。"}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsx)(d.T,{as:n(),href:"/auth/register",color:"primary",size:"lg",className:"px-8",children:"立即开始"}),(0,a.jsx)(d.T,{as:n(),href:"/auth/signin",color:"default",variant:"bordered",size:"lg",className:"px-8",children:"已有账号"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:[(0,a.jsx)(h.Z,{className:"p-6",children:(0,a.jsxs)(m.U,{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD10"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"安全可靠"}),(0,a.jsx)("p",{className:"text-gray-600",children:"采用先进的加密技术保护您的私钥和资产安全，支持MFA双重验证"})]})}),(0,a.jsx)(h.Z,{className:"p-6",children:(0,a.jsxs)(m.U,{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"⚡"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"快速转账"}),(0,a.jsx)("p",{className:"text-gray-600",children:"基于TRON网络的快速转账，低手续费，支持TRX和USDT等主流币种"})]})}),(0,a.jsx)(h.Z,{className:"p-6",children:(0,a.jsxs)(m.U,{className:"text-center",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCF1"}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"简单易用"}),(0,a.jsx)("p",{className:"text-gray-600",children:"直观的用户界面，支持二维码收款，让数字资产管理变得简单"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-center mb-8",children:"核心功能"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"创建和导入TRON钱包"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"TRX和USDT转账收款"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"实时余额查询"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"交易记录查看"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"二维码收款功能"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"多钱包管理"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"安全的私钥加密存储"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(o.R,{color:"success",variant:"flat",children:"✓"}),(0,a.jsx)("span",{children:"邀请码注册系统"})]})]})]})]}),(0,a.jsxs)("div",{className:"text-center mt-16",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"准备开始了吗？"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"注册账号，创建您的第一个TRON钱包"}),(0,a.jsx)(d.T,{as:n(),href:"/auth/register",color:"primary",size:"lg",className:"px-12",children:"立即注册"})]})]}),(0,a.jsx)("footer",{className:"bg-gray-900 text-white py-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsx)("p",{children:"\xa9 2024 TRON钱包. 保留所有权利."})})})]})}},32893:(e,s,l)=>{Promise.resolve().then(l.bind(l,18072))},72031:(e,s,l)=>{"use strict";l.d(s,{A:()=>t,h:()=>i});var a=l(19605),r=l(9585);let c=(0,r.createContext)(void 0);function t(e){let{children:s}=e,[l,t]=(0,r.useState)(null),[i,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{x()},[]);let x=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let s=await e.json();t(s.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{n(!1)}};return(0,a.jsx)(c.Provider,{value:{user:l,loading:i,login:e=>{t(e)},logout:()=>{t(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:s})}function i(){let e=(0,r.useContext)(c);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,7436,390,110,7358],()=>s(32893)),_N_E=e.O()}]);