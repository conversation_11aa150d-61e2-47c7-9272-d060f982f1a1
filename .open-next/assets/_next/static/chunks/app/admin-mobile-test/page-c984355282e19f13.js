(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4042],{19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>d});var r=l(31663),i=l(3208),a=l(23883),n=l(18884),t=l(19605),c=(0,i.Rf)((e,s)=>{var l;let{as:i,className:c,children:d,...o}=e,x=(0,a.zD)(s),{slots:h,classNames:m}=(0,r.f)(),u=(0,n.$z)(null==m?void 0:m.header,c);return(0,t.jsx)(i||"div",{ref:x,className:null==(l=h.header)?void 0:l.call(h,{class:u}),...o,children:d})});c.displayName="HeroUI.CardHeader";var d=c},51756:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>m});var r=l(19605),i=l(9585),a=l(56995),n=l(19864),t=l(21598),c=l(63707),d=l(54110),o=l(89213),x=l(72031),h=l(55935);function m(){let{user:e}=(0,x.h)(),s=(0,h.useRouter)(),[l,m]=(0,i.useState)({width:0,height:0,isMobile:!1,isTablet:!1,isDesktop:!1});return(0,i.useEffect)(()=>{let e=()=>{let e=window.innerWidth;m({width:e,height:window.innerHeight,isMobile:e<768,isTablet:e>=768&&e<1024,isDesktop:e>=1024})};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-4 px-2 sm:py-8 sm:px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-4",children:[(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-center w-full",children:"管理后台移动端适配测试"})}),(0,r.jsx)(t.U,{children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"测试管理后台在不同设备上的显示效果和角色跳转功能"})})]}),(0,r.jsxs)(a.Z,{className:"bg-blue-50",children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-blue-800",children:"当前用户信息"})}),(0,r.jsx)(t.U,{children:e?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"用户名:"}),(0,r.jsx)("span",{children:e.username})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"角色:"}),(0,r.jsx)(c.R,{size:"sm",color:"super_admin"===e.role?"danger":"agent"===e.role?"primary":"default",variant:"flat",children:"super_admin"===e.role?"超级管理员":"agent"===e.role?"代理":"普通用户"})]}),e.email&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"邮箱:"}),(0,r.jsx)("span",{className:"text-sm",children:e.email})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2",children:[(0,r.jsx)("span",{className:"font-medium",children:"期望跳转:"}),(0,r.jsx)("span",{className:"text-sm",children:"super_admin"===e.role||"agent"===e.role?"管理后台 (/admin)":"钱包页面 (/wallet)"})]})]}):(0,r.jsx)("p",{className:"text-gray-500",children:"未登录"})})]}),(0,r.jsxs)(a.Z,{className:"bg-green-50",children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-green-800",children:"屏幕信息"})}),(0,r.jsx)(t.U,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"屏幕尺寸"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsxs)("div",{children:["宽度: ",l.width,"px"]}),(0,r.jsxs)("div",{children:["高度: ",l.height,"px"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"设备类型"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(c.R,{size:"sm",color:l.isMobile?"success":"default",variant:l.isMobile?"solid":"bordered",children:"移动端 (<768px)"}),(0,r.jsx)(c.R,{size:"sm",color:l.isTablet?"success":"default",variant:l.isTablet?"solid":"bordered",className:"ml-2",children:"平板 (768-1024px)"}),(0,r.jsx)(c.R,{size:"sm",color:l.isDesktop?"success":"default",variant:l.isDesktop?"solid":"bordered",className:"ml-2",children:"桌面 (>1024px)"})]})]})]})})]}),(0,r.jsxs)(a.Z,{className:"bg-yellow-50",children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-yellow-800",children:"功能测试"})}),(0,r.jsxs)(t.U,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,r.jsx)(d.T,{color:"primary",onPress:()=>s.push("/admin"),className:"w-full",children:"访问管理后台"}),(0,r.jsx)(d.T,{color:"secondary",onPress:()=>s.push("/wallet"),className:"w-full",children:"访问钱包页面"})]}),(0,r.jsx)(o.y,{}),(0,r.jsx)(d.T,{color:"success",onPress:()=>{(null==e?void 0:e.role)==="super_admin"||(null==e?void 0:e.role)==="agent"?s.push("/admin"):s.push("/wallet")},className:"w-full",variant:"bordered",children:"测试角色跳转逻辑"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-700",children:[(0,r.jsx)("p",{children:"• 管理员和代理应该跳转到管理后台"}),(0,r.jsx)("p",{children:"• 普通用户应该跳转到钱包页面"})]})]})]}),(0,r.jsxs)(a.Z,{children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"移动端适配说明"})}),(0,r.jsx)(t.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800",children:"布局适配"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"• 响应式头部布局：移动端垂直排列，桌面端水平排列"}),(0,r.jsx)("li",{children:"• 表格横向滚动：移动端可以左右滑动查看完整表格"}),(0,r.jsx)("li",{children:"• 隐藏非关键列：移动端隐藏邮箱和注册时间列"}),(0,r.jsx)("li",{children:"• 按钮自适应：移动端全宽按钮，桌面端固定宽度"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"角色跳转"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"• 登录API返回用户角色信息"}),(0,r.jsx)("li",{children:"• 前端根据角色自动跳转到对应页面"}),(0,r.jsx)("li",{children:"• 超级管理员和代理 → 管理后台"}),(0,r.jsx)("li",{children:"• 普通用户 → 钱包页面"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-purple-800",children:"技术实现"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"• Tailwind CSS响应式类名"}),(0,r.jsx)("li",{children:"• HeroUI组件自适应"}),(0,r.jsx)("li",{children:"• 条件渲染优化显示"}),(0,r.jsx)("li",{children:"• 角色信息持久化存储"})]})]})]})})]}),(0,r.jsxs)(a.Z,{className:"bg-gray-50",children:[(0,r.jsx)(n.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"测试检查清单"})}),(0,r.jsx)(t.U,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-4 h-4 rounded-full ".concat(l.isMobile?"bg-green-500":"bg-gray-300")}),(0,r.jsx)("span",{children:"移动端布局正常显示"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-4 h-4 rounded-full ".concat(e?"bg-green-500":"bg-red-500")}),(0,r.jsx)("span",{children:"用户登录状态正常"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-4 h-4 rounded-full ".concat((null==e?void 0:e.role)?"bg-green-500":"bg-red-500")}),(0,r.jsx)("span",{children:"用户角色信息正确"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-4 h-4 rounded-full bg-blue-500"}),(0,r.jsx)("span",{children:"角色跳转逻辑待测试"})]})]})})]})]})})}},55935:(e,s,l)=>{"use strict";var r=l(85383);l.o(r,"useRouter")&&l.d(s,{useRouter:function(){return r.useRouter}}),l.o(r,"useSearchParams")&&l.d(s,{useSearchParams:function(){return r.useSearchParams}})},72031:(e,s,l)=>{"use strict";l.d(s,{A:()=>n,h:()=>t});var r=l(19605),i=l(9585);let a=(0,i.createContext)(void 0);function n(e){let{children:s}=e,[l,n]=(0,i.useState)(null),[t,c]=(0,i.useState)(!0);(0,i.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let s=await e.json();n(s.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{c(!1)}};return(0,r.jsx)(a.Provider,{value:{user:l,loading:t,login:e=>{n(e)},logout:()=>{n(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:s})}function t(){let e=(0,i.useContext)(a);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}},89213:(e,s,l)=>{"use strict";l.d(s,{y:()=>d});var r=l(9733),i=(0,l(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),a=l(9585),n=l(3208),t=l(19605),c=(0,n.Rf)((e,s)=>{let{Component:l,getDividerProps:n}=function(e){var s;let l,n,{as:t,className:c,orientation:d,...o}=e,x=t||"hr";"hr"===x&&"vertical"===d&&(x="div");let{separatorProps:h}=(s={elementType:"string"==typeof x?x:"hr",orientation:d},n=(0,r.$)(s,{enabled:"string"==typeof s.elementType}),("vertical"===s.orientation&&(l="vertical"),"hr"!==s.elementType)?{separatorProps:{...n,role:"separator","aria-orientation":l}}:{separatorProps:n}),m=(0,a.useMemo)(()=>i({orientation:d,className:c}),[d,c]);return{Component:x,getDividerProps:(0,a.useCallback)((e={})=>({className:m,role:"separator","data-orientation":d,...h,...o,...e}),[m,d,h,o])}}({...e});return(0,t.jsx)(l,{ref:s,...n()})});c.displayName="HeroUI.Divider";var d=c},95799:(e,s,l)=>{Promise.resolve().then(l.bind(l,51756))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,390,110,7358],()=>s(95799)),_N_E=e.O()}]);