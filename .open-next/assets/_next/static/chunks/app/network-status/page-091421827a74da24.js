(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5424],{19864:(e,s,t)=>{"use strict";t.d(s,{d:()=>d});var a=t(31663),l=t(3208),r=t(23883),i=t(18884),c=t(19605),n=(0,l.Rf)((e,s)=>{var t;let{as:l,className:n,children:d,...x}=e,m=(0,r.zD)(s),{slots:h,classNames:o}=(0,a.f)(),j=(0,i.$z)(null==o?void 0:o.header,n);return(0,c.jsx)(l||"div",{ref:m,className:null==(t=h.header)?void 0:t.call(h,{class:j}),...x,children:d})});n.displayName="HeroUI.CardHeader";var d=n},55655:(e,s,t)=>{Promise.resolve().then(t.bind(t,71610))},71610:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(19605),l=t(9585),r=t(56995),i=t(19864),c=t(54110),n=t(21598),d=t(63707),x=t(89213);function m(){let[e,s]=(0,l.useState)(!1),[t,m]=(0,l.useState)(null),h=async()=>{s(!0);try{let e=await fetch("/api/test-network-config"),s=await e.json();m(s)}catch(e){console.error("网络配置测试失败:",e)}finally{s(!1)}};(0,l.useEffect)(()=>{h()},[]);let o=e=>{switch(e){case"mainnet":return"主网 (Mainnet)";case"nile":return"Nile测试网";case"shasta":return"Shasta测试网";default:return e}},j=e=>e?"success":"danger";return(0,a.jsx)("div",{className:"container mx-auto p-4 max-w-6xl",children:(0,a.jsxs)(r.Z,{children:[(0,a.jsxs)(i.d,{className:"flex justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"TRON网络配置状态"}),(0,a.jsx)(c.T,{color:"primary",onPress:h,isLoading:e,children:"刷新测试"})]}),(0,a.jsx)(n.U,{className:"space-y-6",children:t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r.Z,{className:"bg-blue-50",children:[(0,a.jsx)(i.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"测试总览"})}),(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.summary.totalNetworks}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"总网络数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.summary.successfulNetworks}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"成功连接"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.summary.failedNetworks}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"连接失败"})]})]})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object.entries(t.results).map(e=>{let[s,t]=e;return(0,a.jsxs)(r.Z,{className:t.success?"border-green-200":"border-red-200",children:[(0,a.jsxs)(i.d,{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"font-semibold",children:o(s)}),(0,a.jsx)(d.R,{color:j(t.success),variant:"flat",size:"sm",children:t.success?"正常":"异常"})]}),(0,a.jsxs)(n.U,{className:"space-y-3",children:[t.success&&t.config&&t.testResults?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"网络配置"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["节点: ",t.config.fullHost]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["API密钥: ",t.config.hasApiKey?"已配置":"未配置"]}),t.config.hasApiKey&&(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["密钥前缀: ",t.config.apiKeyPrefix]})]}),(0,a.jsx)(x.y,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"测试结果"}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["最新区块: ",t.testResults.latestBlock.toLocaleString()]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["测试余额: ",parseFloat(t.testResults.trxBalance).toLocaleString()," TRX"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["账户状态: ",t.testResults.accountExists?"存在":"不存在"]})]})]}):(0,a.jsxs)("div",{className:"text-red-600 text-sm",children:["错误: ",t.error]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["更新时间: ",new Date(t.timestamp).toLocaleString()]})]})]},s)})}),(0,a.jsxs)(r.Z,{className:"bg-yellow-50",children:[(0,a.jsx)(i.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"配置建议"})}),(0,a.jsx)(n.U,{children:(0,a.jsx)("div",{className:"space-y-2",children:Object.entries(t.recommendations).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("span",{className:"font-medium",children:["note"===s?"注意":o(s),":"]}),(0,a.jsx)("span",{className:"ml-2 text-gray-600",children:t})]},s)})})})]}),(0,a.jsxs)(r.Z,{children:[(0,a.jsx)(i.d,{children:(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"API密钥配置说明"})}),(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-green-800",children:"✅ 当前配置"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• 主网 (Mainnet): 使用API密钥提高请求限制"}),(0,a.jsx)("li",{children:"• Nile测试网: 无需API密钥，直接连接"}),(0,a.jsx)("li",{children:"• Shasta测试网: 无需API密钥，直接连接"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800",children:"\uD83D\uDD27 环境变量"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• TRON_API_KEY: 主网API密钥"}),(0,a.jsx)("li",{children:"• TRON_NILE_API_KEY: Nile测试网API密钥 (可选)"}),(0,a.jsx)("li",{children:"• TRON_SHASTA_API_KEY: Shasta测试网API密钥 (可选)"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-orange-800",children:"⚠️ 注意事项"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• 测试网通常有更宽松的请求限制"}),(0,a.jsx)("li",{children:"• 如果测试网出现限制，可申请专用API密钥"}),(0,a.jsx)("li",{children:"• 生产环境建议使用主网API密钥"})]})]})]})})]})]})})]})})}},89213:(e,s,t)=>{"use strict";t.d(s,{y:()=>d});var a=t(9733),l=(0,t(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),r=t(9585),i=t(3208),c=t(19605),n=(0,i.Rf)((e,s)=>{let{Component:t,getDividerProps:i}=function(e){var s;let t,i,{as:c,className:n,orientation:d,...x}=e,m=c||"hr";"hr"===m&&"vertical"===d&&(m="div");let{separatorProps:h}=(s={elementType:"string"==typeof m?m:"hr",orientation:d},i=(0,a.$)(s,{enabled:"string"==typeof s.elementType}),("vertical"===s.orientation&&(t="vertical"),"hr"!==s.elementType)?{separatorProps:{...i,role:"separator","aria-orientation":t}}:{separatorProps:i}),o=(0,r.useMemo)(()=>l({orientation:d,className:n}),[d,n]);return{Component:m,getDividerProps:(0,r.useCallback)((e={})=>({className:o,role:"separator","data-orientation":d,...h,...x,...e}),[o,d,h,x])}}({...e});return(0,c.jsx)(t,{ref:s,...i()})});n.displayName="HeroUI.Divider";var d=n}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,390,110,7358],()=>s(55655)),_N_E=e.O()}]);