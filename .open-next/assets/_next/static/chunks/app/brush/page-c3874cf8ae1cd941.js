(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425],{11894:(e,s,t)=>{Promise.resolve().then(t.bind(t,28419))},28419:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var a=t(19605),r=t(27458),n=t.n(r),l=t(9585),c=t(72031),d=t(55935),i=t(13983),o=t(54110),x=t(56995),m=t(19864),h=t(21598),f=t(1632),j=t(70601),u=t(71413),p=t(90536),g=t(91102),y=t(63833),b=t(93072),N=t(89213),w=t(94661),v=t(66700),T=t(63707);function S(){let[e,s]=(0,l.useState)([]),[t,r]=(0,l.useState)([]),[n,c]=(0,l.useState)([]),[d,o]=(0,l.useState)(!0),[m,f]=(0,l.useState)("orders");(0,l.useEffect)(()=>{p()},[]);let p=async()=>{try{o(!0),await Promise.all([g(),y(),b()])}catch(e){console.error("加载数据失败:",e)}finally{o(!1)}},g=async()=>{try{let e=await fetch("/api/brush/orders");if(e.ok){let t=await e.json();t.success&&s(t.data)}}catch(e){console.error("加载订单失败:",e)}},y=async()=>{try{let e=await fetch("/api/brush/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logs"})});if(e.ok){let s=await e.json();s.success&&r(s.data)}}catch(e){console.error("加载余额记录失败:",e)}},b=async()=>{try{let e=await fetch("/api/brush/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"recharge_records"})});if(e.ok){let s=await e.json();s.success&&c(s.data)}}catch(e){console.error("加载充值记录失败:",e)}},N=e=>{switch(e){case 0:return"warning";case 1:return"primary";case 2:return"success";case 3:default:return"default";case 4:return"danger"}},w=e=>new Date(e).toLocaleString("zh-CN");return d?(0,a.jsx)("div",{className:"flex justify-center py-8",children:(0,a.jsx)(i.o,{size:"lg"})}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(j.r,{selectedKey:m,onSelectionChange:e=>f(e),className:"w-full",children:[(0,a.jsx)(u.i,{title:"刷单订单 (".concat(e.length,")"),children:(0,a.jsx)("div",{className:"space-y-4 mt-4",children:0===e.length?(0,a.jsx)(x.Z,{children:(0,a.jsx)(h.U,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无刷单订单"})})}):e.map(e=>(0,a.jsx)(x.Z,{children:(0,a.jsxs)(h.U,{children:[(0,a.jsxs)("div",{className:"flex gap-4 mb-3",children:[(0,a.jsxs)("div",{className:"flex-shrink-0",children:[e.product_image?(0,a.jsx)("img",{src:e.product_image,alt:e.product_name,className:"w-16 h-16 object-cover rounded-lg border border-gray-200",onError:e=>{var s;let t=e.target;t.style.display="none",null==(s=t.nextElementSibling)||s.classList.remove("hidden")}}):null,(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg border border-gray-200 flex items-center justify-center ".concat(e.product_image?"hidden":""),children:(0,a.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE6"})})]}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-lg",children:e.product_name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["订单号: ",e.order_no]})]}),(0,a.jsx)(T.R,{color:N(e.status),variant:"flat",size:"sm",children:e.status_name})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"商品价格: "}),(0,a.jsxs)("span",{children:["\xa5",e.product_price.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"购买数量: "}),(0,a.jsx)("span",{children:e.quantity})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"订单金额: "}),(0,a.jsxs)("span",{className:"font-semibold",children:["\xa5",e.total_amount.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"佣金: "}),(0,a.jsxs)("span",{className:"text-green-600",children:["\xa5",e.commission_amount.toFixed(2)]})]})]}),e.is_burst&&e.burst_reason&&(0,a.jsx)("div",{className:"mt-3 p-2 bg-red-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-red-700",children:["\uD83D\uDCA5 爆单原因: ",e.burst_reason]})}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["创建时间: ",w(e.created_at),e.completed_at&&(0,a.jsxs)("span",{className:"ml-4",children:["完成时间: ",w(e.completed_at)]})]})})]})},e.id))})},"orders"),(0,a.jsx)(u.i,{title:"余额记录 (".concat(t.length,")"),children:(0,a.jsx)("div",{className:"space-y-4 mt-4",children:0===t.length?(0,a.jsx)(x.Z,{children:(0,a.jsx)(h.U,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无余额变动记录"})})}):t.map(e=>(0,a.jsx)(x.Z,{children:(0,a.jsx)(h.U,{children:(0,a.jsx)("div",{className:"flex justify-between items-start",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(T.R,{color:1===e.type?"success":3===e.type?"primary":"warning",variant:"flat",size:"sm",children:e.type_name}),(0,a.jsxs)("span",{className:"font-semibold ".concat(e.amount>0?"text-green-600":"text-red-600"),children:[e.amount>0?"+":"","\xa5",e.amount.toFixed(2)]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["余额: \xa5",e.balance_before.toFixed(2)," → \xa5",e.balance_after.toFixed(2)]}),(0,a.jsx)("span",{className:"ml-4",children:w(e.created_at)})]})]})})})},e.id))})},"balance"),(0,a.jsx)(u.i,{title:"充值记录 (".concat(n.length,")"),children:(0,a.jsx)("div",{className:"space-y-4 mt-4",children:0===n.length?(0,a.jsx)(x.Z,{children:(0,a.jsx)(h.U,{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无充值记录"})})}):n.map(e=>(0,a.jsx)(x.Z,{children:(0,a.jsxs)(h.U,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold",children:"USDT充值"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 font-mono",children:[e.tx_hash.slice(0,10),"...",e.tx_hash.slice(-8)]})]}),(0,a.jsx)(T.R,{color:1===e.status?"success":2===e.status?"danger":"warning",variant:"flat",size:"sm",children:e.status_name})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"USDT金额: "}),(0,a.jsxs)("span",{children:[e.usdt_amount," USDT"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"内部金额: "}),(0,a.jsxs)("span",{className:"font-semibold",children:["\xa5",e.internal_amount.toFixed(2)]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"汇率: "}),(0,a.jsxs)("span",{children:["1:",e.exchange_rate]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"网络: "}),(0,a.jsx)("span",{children:e.network})]})]}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["提交时间: ",w(e.created_at),e.confirmed_at&&(0,a.jsxs)("span",{className:"ml-4",children:["确认时间: ",w(e.confirmed_at)]})]})})]})},e.id))})},"recharge")]})})}var D=t(35733);let _={mainnet:"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",nile:"TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf",shasta:"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"},k={mainnet:{name:"TRON Mainnet",fullHost:"https://api.trongrid.io",solidityNode:"https://api.trongrid.io",eventServer:"https://api.trongrid.io",explorer:"https://tronscan.org"},nile:{name:"Nile Testnet",fullHost:"https://api.nileex.io",solidityNode:"https://api.nileex.io",eventServer:"https://api.nileex.io",explorer:"https://nile.tronscan.org"},shasta:{name:"Shasta Testnet",fullHost:"https://api.shasta.trongrid.io",solidityNode:"https://api.shasta.trongrid.io",eventServer:"https://api.shasta.trongrid.io",explorer:"https://shasta.tronscan.org"}};var E=t(35733),U=t(34105).Buffer;let{TronWeb:A}=t(15900);class R{getApiKeyForNetwork(){switch(this.network){case"mainnet":return E.env.TRON_API_KEY||"3f67c2cc-9119-468b-a336-f3f95b7bfec3";case"nile":return E.env.TRON_NILE_API_KEY||E.env.NEXT_PUBLIC_TRON_NILE_API_KEY;case"shasta":return E.env.TRON_SHASTA_API_KEY||E.env.NEXT_PUBLIC_TRON_SHASTA_API_KEY;default:return}}initializeTronWeb(){let e=k[this.network],s=this.getApiKeyForNetwork();console.log("初始化TronWeb - 网络: ".concat(this.network,", API密钥: ").concat(s?"已设置":"未设置"));let t={"Content-Type":"application/json"};s&&(t["TRON-PRO-API-KEY"]=s),this.tronWeb=new A({fullHost:e.fullHost,headers:t,privateKey:"01"})}async createWallet(e){try{let s=await this.tronWeb.createAccount();return{address:s.address.base58,privateKey:s.privateKey,name:e.name,network:e.network||this.network}}catch(e){throw Error("创建钱包失败: ".concat(e.message))}}async importWallet(e){try{if(!e.privateKey||64!==e.privateKey.length)throw Error("私钥格式无效，必须是64位十六进制字符串");let s=this.tronWeb.address.fromPrivateKey(e.privateKey);if(!this.tronWeb.isAddress(s))throw Error("生成的地址无效");return{address:s,name:e.name,network:e.network||this.network}}catch(e){throw Error("导入钱包失败: ".concat(e.message))}}isValidAddress(e){return this.tronWeb.isAddress(e)}async getWalletBalance(e){try{if(console.log("开始获取钱包余额 - 地址: ".concat(e,", 网络: ").concat(this.network)),!this.isValidAddress(e))throw Error("无效的TRON地址");console.log("获取TRX余额...");let s=await this.tronWeb.trx.getBalance(e),t=this.tronWeb.fromSun(s);console.log("TRX余额: ".concat(t));let a=0;try{console.log("获取USDT余额...");let s=_[this.network];console.log("USDT合约地址: ".concat(s));let t="mainnet"===this.network?"https://api.trongrid.io":"https://api.nileex.io",r=await fetch("".concat(t,"/wallet/triggerconstantcontract"),{method:"POST",headers:{"Content-Type":"application/json",...E.env.TRON_API_KEY?{"TRON-PRO-API-KEY":E.env.TRON_API_KEY}:{}},body:JSON.stringify({owner_address:this.tronWeb.address.toHex(e),contract_address:this.tronWeb.address.toHex(s),function_selector:"balanceOf(address)",parameter:this.tronWeb.address.toHex(e).substring(2).padStart(64,"0")})});if(r.ok){let e=await r.json();if(e.result&&e.result.result&&e.constant_result&&e.constant_result[0]){let s=e.constant_result[0];a=parseInt(s,16)/1e6}}console.log("USDT余额: ".concat(a))}catch(e){console.warn("获取USDT余额失败:",e.message)}let r={TRX:parseFloat(t),USDT:a};return console.log("余额获取完成:",r),r}catch(e){throw console.error("获取余额失败:",e),Error("获取余额失败: ".concat(e.message))}}async transfer(e){try{let s;if(!this.isValidAddress(e.fromAddress))throw Error("发送地址无效");if(!this.isValidAddress(e.toAddress))throw Error("接收地址无效");if(e.amount<=0)throw Error("转账金额必须大于0");if(e.privateKey&&this.tronWeb.setPrivateKey(e.privateKey),"TRX"===e.currency){let t=await this.tronWeb.transactionBuilder.sendTrx(e.toAddress,this.tronWeb.toSun(e.amount),e.fromAddress),a=await this.tronWeb.trx.sign(t),r=await this.tronWeb.trx.sendRawTransaction(a);if(!r.result)throw Error(r.message||"TRX转账失败");s=r.txid}else if("USDT"===e.currency){let t=_[this.network],a=await this.tronWeb.contract().at(t),r=Math.floor(1e6*e.amount);s=await a.transfer(e.toAddress,r).send({from:e.fromAddress})}else throw Error("不支持的币种");return{success:!0,txHash:s,amount:e.amount,currency:e.currency,fromAddress:e.fromAddress,toAddress:e.toAddress}}catch(s){return{success:!1,error:s.message||"转账失败",amount:e.amount,currency:e.currency,fromAddress:e.fromAddress,toAddress:e.toAddress}}}async transferUSDT(e,s,t,a,r){try{let n;if(!this.isValidAddress(e))throw Error("发送地址无效");if(!this.isValidAddress(t))throw Error("接收地址无效");if(a<=0)throw Error("转账金额必须大于0");if(!s)throw Error("钱包密码不能为空");if(!_[this.network])throw Error("当前网络不支持USDT转账");if(!r)throw Error("需要数据库连接来获取私钥，请使用钱包页面的转账功能");let l=await r.prepare("\n        SELECT id, private_key_encrypted, user_id\n        FROM wallets\n        WHERE address = ? AND is_active = TRUE\n      ").bind(e).first();if(!l)throw Error("钱包不存在或已禁用");try{n=C.decrypt(l.private_key_encrypted,s)}catch(e){throw Error("钱包密码错误")}return await this.transfer({fromAddress:e,toAddress:t,amount:a,currency:"USDT",privateKey:n})}catch(s){return{success:!1,error:s.message||"USDT转账失败",amount:a,currency:"USDT",fromAddress:e,toAddress:t}}}async getTransactionStatus(e){try{let s=await this.tronWeb.trx.getTransaction(e);if(!s)return"pending";if(s.ret&&s.ret[0]&&"SUCCESS"===s.ret[0].contractRet)return"confirmed";return"failed"}catch(e){return"pending"}}async waitForConfirmation(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6e4,t=Date.now();for(;Date.now()-t<s;){let s=await this.getTransactionStatus(e);if("confirmed"===s)return!0;if("failed"===s)break;await new Promise(e=>setTimeout(e,3e3))}return!1}generateReceiveQRData(e,s,t){let a="tron:".concat(e);return s&&t&&(a+="?amount=".concat(s,"&token=").concat(t)),a}constructor(e="mainnet"){this.network=e,this.initializeTronWeb()}}class C{static encrypt(e,s){return U.from(e+"|"+s).toString("base64")}static decrypt(e,s){try{let[t,a]=U.from(e,"base64").toString().split("|");if(a!==s)throw Error("密码错误");return t}catch(e){throw Error("解密失败")}}}function P(){var e,s,t;let{user:r,loading:T,logout:D}=(0,c.h)(),_=(0,d.useRouter)(),[k,E]=(0,l.useState)(!0),[U,A]=(0,l.useState)([]),[C,P]=(0,l.useState)(null),[F,O]=(0,l.useState)(null),[z,W]=(0,l.useState)("get-order"),[K,I]=(0,l.useState)([]),[B,L]=(0,l.useState)(null),[Y,H]=(0,l.useState)(!1),[X,Z]=(0,l.useState)(!1),[J,V]=(0,l.useState)(null),[q,M]=(0,l.useState)(null),[G,Q]=(0,l.useState)({amount:"",password:""}),[$,ee]=(0,l.useState)(!1),[es,et]=(0,l.useState)(!1),ea=(0,l.useRef)(null),er=(0,l.useRef)(null),en=(0,l.useRef)(null),[el,ec]=(0,l.useState)(!1);(0,l.useEffect)(()=>{if(!T){if(!r)return void _.push("/auth/signin");eo()}},[r,T]),(0,l.useEffect)(()=>{if(k||!C||!F)return;let e=setTimeout(()=>{ej()},1e3);return()=>clearTimeout(e)},[C,K,F,k]);let[ed,ei]=(0,l.useState)(0);(0,l.useEffect)(()=>{let e=()=>{let e=en.current||ea.current;return!!e&&(ei(e.getBoundingClientRect().top+window.scrollY),!0)},s=0,t=()=>{!e()&&++s<10&&setTimeout(t,200)},a=null;return e()||(a=setTimeout(t,1e3)),window.addEventListener("resize",e),()=>{a&&clearTimeout(a),window.removeEventListener("resize",e)}},[]),(0,l.useEffect)(()=>{!k&&C&&0===ed&&setTimeout(()=>{(en.current||ea.current)&&ei((en.current||ea.current).getBoundingClientRect().top+window.scrollY)},100)},[k,C,ed]),(0,l.useEffect)(()=>{if(0===ed)return;let e=!1,s=()=>{e||(requestAnimationFrame(()=>{let s=window.scrollY>ed-20;if(s!==es)if(et(s),s&&ea.current&&er.current){let e=ea.current.offsetHeight;er.current.style.height="".concat(e,"px")}else er.current&&(er.current.style.height="0px");e=!1}),e=!0)};return window.addEventListener("scroll",s,{passive:!0}),()=>{window.removeEventListener("scroll",s)}},[ed,es]);let eo=async()=>{try{E(!0),await Promise.all([ex(),em(),eh(),ef()])}catch(e){console.error("加载数据失败:",e)}finally{E(!1)}},ex=async()=>{try{let e=await fetch("/api/products");if(e.ok){let s=await e.json();if(s.success){let e=s.data.map(e=>({...e,price:e.price/100}));A(e)}}}catch(e){console.error("加载商品失败:",e)}},em=async()=>{try{let e=await fetch("/api/brush/balance");if(e.ok){let s=await e.json();s.success&&P(s.data)}}catch(e){console.error("加载用户余额失败:",e)}},eh=async()=>{try{let e=await fetch("/api/brush/recharge?network=mainnet");if(e.ok){let s=await e.json();s.success&&O(s.data)}}catch(e){console.error("加载充值信息失败:",e)}},ef=async()=>{try{let e=await fetch("/api/wallets");if(e.ok){let s=await e.json();if(s.success&&s.data){let e=await Promise.all(s.data.map(async e=>{try{let s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mainnet";return new R(e)}(e.network||"mainnet"),t=await s.getWalletBalance(e.address);return{...e,balance:t}}catch(s){return console.error("获取钱包 ".concat(e.address," 余额失败:"),s),e}}));I(e),console.log("\uD83D\uDCB0 钱包余额加载完成:",e);let t=e.find(e=>e.is_default)||e[0];t&&L(t)}}}catch(e){console.error("加载用户钱包失败:",e)}},ej=async()=>{console.log("\uD83D\uDD0D 检查自动充值弹窗条件:"),console.log("- userBalance:",C),console.log("- userWallets:",K),console.log("- rechargeInfo:",F),C&&C.balance<10?(console.log("✅ 余额不足，需要充值"),K.length>0&&F?(console.log("� 显示充值弹窗"),setTimeout(()=>{K.find(e=>e.balance&&e.balance.USDT>=10)?console.log("✅ 检测到可用钱包，可进行智能充值"):confirm("检测到钱包余额不足，是否前往钱包页面充值？")&&_.push("/wallet")},1500)):0===K.length?(console.log("\uD83D\uDC5B 提示创建钱包"),setTimeout(()=>{confirm("检测到您还没有钱包，是否前往创建钱包？")&&_.push("/wallet")},1500)):console.log("⏳ 等待充值信息加载")):console.log("✅ 余额充足，无需充值")},eu=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;if(!B||!F)return void alert("请先选择钱包");if(!B.balance||B.balance.USDT<e)return void alert("钱包USDT余额不足，需要至少 ".concat(e," USDT"));let s=prompt("请输入钱包密码以确认充值：");if(s)try{ec(!0);let a=await fetch("/api/brush/transfer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fromAddress:B.address,password:s,amount:e,network:B.network||"mainnet"})});if(a.ok){let s=await a.json();if(s.success){var t;alert("\uD83C\uDF89 充值成功！已转账 ".concat(e," USDT，系统将自动为您充值到内部账户\n交易哈希: ").concat(null==(t=s.data)?void 0:t.txHash)),Z(!1),Q({amount:"",password:""}),await em(),await ef()}else s.error&&s.error.includes("尚未激活")?confirm("".concat(s.error,"\n\n是否查看账户激活指南？"))&&window.open("/account-activation","_blank"):alert("充值失败: ".concat(s.error))}else{let e=await a.json();e.error&&e.error.includes("尚未激活")?confirm("".concat(e.error,"\n\n是否查看账户激活指南？"))&&window.open("/account-activation","_blank"):alert("充值失败: ".concat(e.error||"网络错误"))}}catch(e){console.error("快速充值失败:",e),alert("快速充值失败，请重试")}finally{ec(!1)}},ep=async()=>{if(!B||!F)return void alert("请先选择钱包");if(!G.amount||!G.password)return void alert("请填写充值金额和钱包密码");let e=parseFloat(G.amount);if(isNaN(e)||e<=0)return void alert("请输入有效的充值金额");if(e<F.minRecharge)return void alert("充值金额不能少于 ".concat(F.minRecharge," USDT"));if(!B.balance||B.balance.USDT<e)return void alert("钱包USDT余额不足");try{ec(!0);let t=await fetch("/api/brush/transfer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fromAddress:B.address,password:G.password,amount:e,network:B.network||"mainnet"})});if(t.ok){let a=await t.json();if(a.success){var s;alert("\uD83C\uDF89 自动充值成功！已转账 ".concat(e," USDT，系统将自动为您充值到内部账户\n交易哈希: ").concat(null==(s=a.data)?void 0:s.txHash)),Z(!1),Q({amount:"",password:""}),await em(),await ef()}else alert("充值失败: ".concat(a.error))}else{let e=await t.json();alert("充值失败: ".concat(e.error||"网络错误"))}}catch(e){console.error("自动充值失败:",e),alert("自动充值失败，请重试")}finally{ec(!1)}},eg=async()=>{if(!C||C.balance<10)return void alert("余额不足，请先充值");try{ee(!0);let e=await fetch("/api/brush/get-order",{method:"POST",headers:{"Content-Type":"application/json"}});if(e.ok){let s=await e.json();s.success&&s.data?(M(s.data.order.id),V({id:s.data.product.id,name:s.data.product.name,description:s.data.product.description,image:s.data.product.image,price:s.data.product.price,status:1}),H(!0),alert(s.data.message)):alert("获取订单失败: ".concat(s.error))}else{let s=await e.json();alert("获取订单失败: ".concat(s.error||"网络错误"))}}catch(e){console.error("获取订单失败:",e),alert("获取订单失败，请重试")}finally{ee(!1)}},ey=async()=>{if(!J||!q)return void alert("订单信息不完整，请重新获取订单");if(!C||C.balance<J.price)return void alert("余额不足，请先充值");try{ee(!0);let s=await fetch("/api/brush/submit-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:q})});if(s.ok){let t=await s.json();if(t.success){var e;alert((null==(e=t.data)?void 0:e.message)||"订单提交成功"),H(!1),V(null),M(null),await em()}else alert("订单提交失败: ".concat(t.error))}else{let e=await s.json();alert("订单提交失败: ".concat(e.error||"网络错误"))}}catch(e){console.error("订单提交失败:",e),alert("订单提交失败，请重试")}finally{ee(!1)}},eb=async()=>{if(confirm("确定要退出登录吗？"))try{await fetch("/api/auth/logout",{method:"POST"})}catch(e){console.error("退出登录失败:",e)}finally{D(),_.push("/login")}};return T||k?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(i.o,{size:"lg"})}):r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{id:"8dd39236fc281002",children:"@-webkit-keyframes slideInFromTop{from{-webkit-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideInFromTop{from{-moz-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideInFromTop{from{-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideInFromTop{from{-webkit-transform:translatey(-100%);-moz-transform:translatey(-100%);-o-transform:translatey(-100%);transform:translatey(-100%);opacity:0}to{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}"}),(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 min-h-screen bg-gradient-to-br from-orange-50 to-red-100 py-8 px-4",children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 mb-8",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex-1",children:[(0,a.jsx)("h1",{className:"jsx-8dd39236fc281002 text-2xl md:text-3xl font-bold text-gray-900",children:"\uD83C\uDFAF 刷单赚钱"}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-gray-600 mt-1 text-sm md:text-base",children:"轻松刷单，稳定收益"})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex items-center gap-2 ml-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-right",children:[(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-xs md:text-sm font-medium text-gray-900",children:["\uD83D\uDC4B ",(null==r?void 0:r.name)||(null==r?void 0:r.username)]}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-xs text-gray-500",children:(null==r?void 0:r.role)==="super_admin"?"超管":(null==r?void 0:r.role)==="agent"?"代理":"用户"})]}),(0,a.jsx)(o.T,{color:"danger",variant:"light",size:"sm",onPress:eb,className:"min-w-unit-12",children:"�"})]})]}),(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 flex gap-2 md:gap-3",children:(0,a.jsx)(o.T,{color:"primary",variant:"bordered",size:"sm",onPress:()=>_.push("/wallet"),className:"flex-1 md:flex-none",children:"� 钱包管理"})})]}),C&&(0,a.jsxs)(x.Z,{className:"mb-6",children:[(0,a.jsx)(m.d,{children:(0,a.jsx)("h3",{className:"jsx-8dd39236fc281002 text-lg font-semibold",children:"账户余额"})}),(0,a.jsxs)(h.U,{children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center",children:[(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-2xl font-bold text-green-600",children:["\xa5",C.balance.toFixed(2)]}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-gray-500",children:"可用余额"})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center",children:[(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-xl font-semibold text-blue-600",children:C.stats.todayOrders}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-gray-500",children:"今日订单"})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center",children:[(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-xl font-semibold text-purple-600",children:["\xa5",C.stats.todayCommission.toFixed(2)]}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-gray-500",children:"今日佣金"})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center",children:[(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-xl font-semibold text-orange-600",children:C.stats.totalOrders}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-gray-500",children:"总订单数"})]})]}),(0,a.jsx)(f.f,{y:4}),C.balance<10&&(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4 mb-4",children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex items-center",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002 text-2xl mr-3",children:"⚠️"}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002",children:[(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 font-semibold text-orange-800",children:"余额不足"}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-orange-700",children:"建议充值后再进行刷单操作"})]})]}),K.find(e=>e.balance&&e.balance.USDT>=10)&&(0,a.jsx)(o.T,{color:"warning",size:"sm",onPress:()=>{let e=K.find(e=>e.balance&&e.balance.USDT>=10);e&&(L(e),Q(s=>({...s,amount:e.balance.USDT.toString()})),Z(!0))},children:"\uD83D\uDE80 快速充值全部"})]})}),(0,a.jsx)("div",{ref:en,className:"jsx-8dd39236fc281002 h-1 -mb-1"}),(0,a.jsx)("div",{ref:ea,style:es?{paddingTop:"max(env(safe-area-inset-top), 0.5rem)",animation:"slideInFromTop 0.5s ease-out"}:{},className:"jsx-8dd39236fc281002 "+"transition-all duration-500 ease-out ".concat(es?"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-xl":"space-y-4"),children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 "+"".concat(es?"max-w-lg mx-auto px-4 py-3 space-y-3":"space-y-4"),children:[C.balance>=10?(0,a.jsx)(o.T,{color:"primary",size:"lg",className:"w-full py-3 text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",onPress:eg,isLoading:$,children:$?"正在匹配订单...":"\uD83C\uDFAF 获取订单"}):(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-gray-600 mb-3",children:"余额不足，请先充值后再获取订单"}),K.find(e=>e.balance&&e.balance.USDT>=10)?(0,a.jsx)(o.T,{color:"warning",size:"lg",className:"w-full",onPress:()=>{let e=K.find(e=>e.balance&&e.balance.USDT>=10);e&&(L(e),Q(s=>({...s,amount:e.balance.USDT.toString()})),Z(!0))},children:"⚡ 智能充值全部"}):(0,a.jsx)(o.T,{color:"primary",variant:"bordered",size:"lg",className:"w-full",onPress:()=>_.push("/wallet"),children:"前往钱包充值"})]}),(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 flex gap-3",children:C.balance>=10&&K.find(e=>e.balance&&e.balance.USDT>=10)&&(0,a.jsx)(o.T,{color:"warning",variant:"bordered",size:"md",className:"flex-1",onPress:()=>{let e=K.find(e=>e.balance&&e.balance.USDT>=10);e&&(L(e),Q(s=>({...s,amount:e.balance.USDT.toString()})),Z(!0))},children:"\uD83D\uDCB0 充值"})})]})}),(0,a.jsx)("div",{ref:er,style:{height:"0px"},className:"jsx-8dd39236fc281002 transition-all duration-500 ease-out overflow-hidden"})]})]}),(0,a.jsxs)(j.r,{selectedKey:z,onSelectionChange:e=>W(e),className:"w-full",children:[(0,a.jsx)(u.i,{title:"刷单规则",children:(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 max-w-2xl mx-auto mt-6",children:(0,a.jsxs)(x.Z,{children:[(0,a.jsx)(m.d,{children:(0,a.jsx)("h3",{className:"jsx-8dd39236fc281002 text-xl font-semibold text-blue-800",children:"\uD83D\uDCCB 刷单规则说明"})}),(0,a.jsx)(h.U,{children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"jsx-8dd39236fc281002 font-semibold text-blue-800 mb-2",children:"\uD83C\uDFAF 订单获取"}),(0,a.jsxs)("ul",{className:"jsx-8dd39236fc281002 text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 系统随机匹配商品订单"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 每次获取一个订单，确认后扣除余额"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 订单完成后返还本金+佣金"})]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"jsx-8dd39236fc281002 font-semibold text-green-800 mb-2",children:"\uD83D\uDCB0 佣金收益"}),(0,a.jsxs)("ul",{className:"jsx-8dd39236fc281002 text-sm text-green-700 space-y-1",children:[(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 正常订单佣金比例：3%-8%"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 完成订单即可获得佣金收入"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 佣金实时到账，可立即提现"})]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-orange-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"jsx-8dd39236fc281002 font-semibold text-orange-800 mb-2",children:"⚠️ 爆单机制"}),(0,a.jsxs)("ul",{className:"jsx-8dd39236fc281002 text-sm text-orange-700 space-y-1",children:[(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 部分订单可能为爆单（无佣金收入）"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 爆单概率约10%，随机出现"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 爆单也会返还本金，只是无佣金"})]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-purple-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"jsx-8dd39236fc281002 font-semibold text-purple-800 mb-2",children:"� 限制规则"}),(0,a.jsxs)("ul",{className:"jsx-8dd39236fc281002 text-sm text-purple-700 space-y-1",children:[(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 每日最多可完成50单"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 最低余额要求：10元"}),(0,a.jsx)("li",{className:"jsx-8dd39236fc281002",children:"• 订单处理时间：1-5分钟"})]})]})]})})]})})},"rules"),(0,a.jsx)(u.i,{title:"订单记录",children:(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 mt-6",children:(0,a.jsx)(S,{})})},"orders")]}),(0,a.jsx)(p.Y,{isOpen:Y,onClose:()=>{H(!1),V(null),M(null)},size:"md",children:(0,a.jsxs)(g.g,{children:[(0,a.jsx)(y.c,{children:(0,a.jsx)("h3",{className:"jsx-8dd39236fc281002 text-xl font-semibold",children:"\uD83C\uDFAF 确认提交订单"})}),(0,a.jsx)(b.h,{children:J&&(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-center",children:[J.image&&(0,a.jsx)("img",{src:J.image,alt:J.name,className:"jsx-8dd39236fc281002 w-32 h-32 object-cover rounded-lg mx-auto mb-4"}),(0,a.jsx)("h4",{className:"jsx-8dd39236fc281002 text-lg font-semibold",children:J.name}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-gray-600 text-sm",children:J.description})]}),(0,a.jsx)(N.y,{}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-2",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"商品价格："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002 font-semibold",children:["\xa5",J.price.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"购买数量："}),(0,a.jsx)("span",{className:"jsx-8dd39236fc281002 font-semibold",children:"1"})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-lg font-bold",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"总计："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002 text-red-600",children:["\xa5",J.price.toFixed(2)]})]})]}),C&&(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-gray-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"当前余额："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002",children:["\xa5",C.balance.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"下单后余额："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002",children:["\xa5",(C.balance-J.price).toFixed(2)]})]})]}),(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 bg-blue-50 p-3 rounded-lg",children:(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-blue-800",children:"\uD83C\uDFAF 系统已为您匹配此订单，确认提交后将扣除相应金额。 完成订单可获得本金+佣金返还。如需重新获取订单，请关闭此窗口。"})})]})}),(0,a.jsxs)(w.q,{children:[(0,a.jsx)(o.T,{color:"danger",variant:"light",onPress:()=>{H(!1),V(null),M(null)},children:"取消"}),(0,a.jsx)(o.T,{color:"primary",onPress:ey,isLoading:$,children:$?"提交中...":"✅ 确认提交订单"})]})]})}),(0,a.jsx)(p.Y,{isOpen:X,onClose:()=>Z(!1),size:"lg",isDismissable:!1,children:(0,a.jsxs)(g.g,{children:[(0,a.jsx)(y.c,{children:(0,a.jsx)("h3",{className:"jsx-8dd39236fc281002 text-xl font-semibold",children:"\uD83D\uDE80 智能充值"})}),(0,a.jsx)(b.h,{children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-4",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex items-center mb-2",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002 text-lg mr-2",children:"⚡"}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm font-medium text-blue-800",children:"自动检测到余额不足"})]}),(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-sm text-blue-700",children:["当前内部余额：",(0,a.jsx)("span",{className:"jsx-8dd39236fc281002 font-semibold",children:(null==C||null==(e=C.balance)?void 0:e.toFixed(2))||"0.00"})," 元"]}),(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 text-sm text-blue-700",children:"系统已为您选择最佳钱包进行快速充值，只需输入密码即可完成。"})]}),K.length>0&&(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-2",children:[(0,a.jsx)("label",{className:"jsx-8dd39236fc281002 text-sm font-medium text-gray-700",children:"选择钱包："}),(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 grid gap-2",children:K.map(e=>{var s,t,r,n;return(0,a.jsx)("div",{onClick:()=>L(e),className:"jsx-8dd39236fc281002 "+"p-3 border rounded-lg cursor-pointer transition-colors ".concat((null==B?void 0:B.id)===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"),children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002",children:[(0,a.jsx)("p",{className:"jsx-8dd39236fc281002 font-medium",children:e.name}),(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-sm text-gray-500",children:[e.address.slice(0,8),"...",e.address.slice(-6)]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 text-right",children:[(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-sm font-medium",children:[(null==(t=e.balance)||null==(s=t.USDT)?void 0:s.toFixed(2))||"0.00"," USDT"]}),(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-xs text-gray-500",children:[(null==(n=e.balance)||null==(r=n.TRX)?void 0:r.toFixed(2))||"0.00"," TRX"]})]})]})},e.id)})})]}),B&&F&&(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 space-y-4",children:[(0,a.jsx)(N.y,{}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 bg-gray-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"平台地址："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002 font-mono text-xs",children:[F.platformAddress.slice(0,10),"...",F.platformAddress.slice(-8)]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"汇率："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002",children:["1 USDT = ",F.exchangeRate," 内部币"]})]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-8dd39236fc281002",children:"最小充值："}),(0,a.jsxs)("span",{className:"jsx-8dd39236fc281002",children:[F.minRecharge," USDT"]})]})]}),(0,a.jsx)(v.r,{label:"充值金额",placeholder:"最小充值: ".concat(F.minRecharge," USDT"),type:"number",value:G.amount,onChange:e=>Q(s=>({...s,amount:e.target.value})),endContent:(0,a.jsx)("span",{className:"jsx-8dd39236fc281002 text-gray-500",children:"USDT"}),description:"钱包余额: ".concat((null==(t=B.balance)||null==(s=t.USDT)?void 0:s.toFixed(2))||"0.00"," USDT")}),(0,a.jsx)(v.r,{label:"钱包密码",placeholder:"请输入钱包密码以确认转账",type:"password",value:G.password,onChange:e=>Q(s=>({...s,password:e.target.value}))}),G.amount&&(0,a.jsx)("div",{className:"jsx-8dd39236fc281002 bg-green-50 p-3 rounded-lg",children:(0,a.jsxs)("p",{className:"jsx-8dd39236fc281002 text-sm text-green-800",children:["充值 ",G.amount," USDT 将获得 ",(parseFloat(G.amount||"0")*F.exchangeRate).toFixed(2)," 内部币"]})})]})]})}),(0,a.jsx)(w.q,{children:(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex flex-col w-full gap-3",children:[B&&B.balance&&B.balance.USDT>=10&&(0,a.jsxs)(o.T,{color:"success",variant:"solid",onPress:()=>eu(B.balance.USDT),className:"w-full font-semibold",size:"lg",isLoading:el,isDisabled:el,children:["\uD83D\uDE80 一键充值全部 ",B.balance.USDT.toFixed(2)," USDT (推荐)"]}),(0,a.jsxs)("div",{className:"jsx-8dd39236fc281002 flex gap-2",children:[(0,a.jsx)(o.T,{color:"danger",variant:"light",onPress:()=>Z(!1),className:"flex-1",children:"稍后充值"}),(0,a.jsx)(o.T,{color:"primary",onPress:ep,isLoading:el,isDisabled:!B||!G.amount||!G.password,className:"flex-1",children:el?"充值中...":"立即充值"})]})]})})]})})]})})]}):null}},72031:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,h:()=>c});var a=t(19605),r=t(9585);let n=(0,r.createContext)(void 0);function l(e){let{children:s}=e,[t,l]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{i()},[]);let i=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let s=await e.json();l(s.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{d(!1)}};return(0,a.jsx)(n.Provider,{value:{user:t,loading:c,login:e=>{l(e)},logout:()=>{l(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:s})}function c(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}}},e=>{var s=s=>e(e.s=s);e.O(0,[2883,8031,4110,1358,7443,4273,8688,3748,9586,4936,7319,390,110,7358],()=>s(11894)),_N_E=e.O()}]);