(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8004],{40190:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>d});var t=l(19605),r=l(55935),a=l(46762),c=l.n(a),n=l(9585);let i={Configuration:"服务器配置错误",AccessDenied:"访问被拒绝",Verification:"验证失败",Default:"登录过程中出现错误"};function x(){let e=i[(0,r.useSearchParams)().get("error")||"Default"]||i.De<PERSON>ult;return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100",children:(0,t.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,t.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"登录失败"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:e})]})}),(0,t.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsx)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请尝试以下解决方案："}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 text-left space-y-2",children:[(0,t.jsx)("li",{children:"• 检查网络连接"}),(0,t.jsx)("li",{children:"• 清除浏览器缓存"}),(0,t.jsx)("li",{children:"• 尝试使用其他登录方式"}),(0,t.jsx)("li",{children:"• 联系技术支持"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(c(),{href:"/auth/signin",className:"flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-center hover:bg-blue-700 transition-colors",children:"重新登录"}),(0,t.jsx)(c(),{href:"/",className:"flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-center hover:bg-gray-700 transition-colors",children:"返回首页"})]})]})})})]})}function d(){return(0,t.jsx)(n.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"})}),(0,t.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"加载中..."})]})})}),children:(0,t.jsx)(x,{})})}},94933:(e,s,l)=>{Promise.resolve().then(l.bind(l,40190))}},e=>{var s=s=>e(e.s=s);e.O(0,[7436,390,110,7358],()=>s(94933)),_N_E=e.O()}]);