(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{1632:(e,r,a)=>{"use strict";a.d(r,{f:()=>c});var s={px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},t=a(3208),i=(0,a(92610).tv)({base:"w-px h-px inline-block",variants:{isInline:{true:"inline-block",false:"block"}},defaultVariants:{isInline:!1}}),l=a(18884),n=a(9585),d=e=>{var r;return null!=(r=s[e])?r:e},m=a(19605),o=(0,t.Rf)((e,r)=>{let{Component:a,getSpacerProps:s}=function(e){let[r,a]=(0,t.rE)(e,i.variantKeys),{as:s,className:m,x:o=1,y:c=1,...u}=r,p=(0,n.useMemo)(()=>i({...a,className:m}),[(0,l.t6)(a),m]),x=d(o),h=d(c);return{Component:s||"span",getSpacerProps:(e={})=>({...e,...u,"aria-hidden":(0,l.sE)(!0),className:(0,l.$z)(p,e.className),style:{...e.style,...u.style,marginLeft:x,marginTop:h}})}}({...e});return(0,m.jsx)(a,{ref:r,...s()})});o.displayName="HeroUI.Spacer";var c=o},22445:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>h});var s=a(19605),t=a(9585),i=a(55935),l=a(46762),n=a.n(l),d=a(56995),m=a(19864),o=a(21598),c=a(66700),u=a(63707),p=a(1632),x=a(54110);function h(){let e=(0,i.useRouter)(),[r,a]=(0,t.useState)({username:"",email:"",password:"",confirmPassword:"",inviteCode:"",name:""}),[l,h]=(0,t.useState)(!1),[v,b]=(0,t.useState)(""),f=async a=>{if(a.preventDefault(),b(""),!r.username.trim())return void b("请输入用户名");if(!r.password.trim())return void b("请输入密码");if(r.password!==r.confirmPassword)return void b("两次输入的密码不一致");if(r.password.length<6)return void b("密码长度至少6位");if(!r.inviteCode.trim())return void b("请输入邀请码");try{h(!0);let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:r.username.trim(),email:r.email.trim()||void 0,password:r.password,inviteCode:r.inviteCode.trim(),name:r.name.trim()||void 0})}),s=await a.json();a.ok?(alert("注册成功！请登录您的账户。"),e.push("/auth/signin")):b(s.error||"注册失败")}catch(e){console.error("注册失败:",e),b("注册失败，请重试")}finally{h(!1)}},j=(e,r)=>{a(a=>({...a,[e]:r})),v&&b("")};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full",children:[(0,s.jsxs)(d.Z,{className:"shadow-2xl",children:[(0,s.jsx)(m.d,{className:"text-center pb-2",children:(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"注册账户"}),(0,s.jsx)("p",{className:"text-gray-600",children:"创建您的TRON钱包账户"})]})}),(0,s.jsxs)(o.U,{className:"px-8 py-6",children:[(0,s.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[v&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:v})}),(0,s.jsx)(c.r,{label:"用户名",placeholder:"输入用户名",value:r.username,onChange:e=>j("username",e.target.value),isRequired:!0,variant:"bordered",description:"用于登录的唯一标识"}),(0,s.jsx)(c.r,{label:"邮箱",type:"email",placeholder:"输入邮箱地址（可选）",value:r.email,onChange:e=>j("email",e.target.value),variant:"bordered",description:"用于找回密码和接收通知"}),(0,s.jsx)(c.r,{label:"显示名称",placeholder:"输入显示名称（可选）",value:r.name,onChange:e=>j("name",e.target.value),variant:"bordered",description:"在应用中显示的名称"}),(0,s.jsx)(c.r,{label:"密码",type:"password",placeholder:"输入密码",value:r.password,onChange:e=>j("password",e.target.value),isRequired:!0,variant:"bordered",description:"至少6位字符"}),(0,s.jsx)(c.r,{label:"确认密码",type:"password",placeholder:"再次输入密码",value:r.confirmPassword,onChange:e=>j("confirmPassword",e.target.value),isRequired:!0,variant:"bordered"}),(0,s.jsx)(c.r,{label:"邀请码",placeholder:"输入邀请码",value:r.inviteCode,onChange:e=>j("inviteCode",e.target.value),isRequired:!0,variant:"bordered",description:"需要有效的邀请码才能注册",startContent:(0,s.jsx)(u.R,{size:"sm",color:"primary",variant:"flat",children:"\uD83C\uDFAB"})}),(0,s.jsx)(p.f,{y:2}),(0,s.jsx)(x.T,{type:"submit",color:"primary",size:"lg",className:"w-full",isLoading:l,disabled:l,children:l?"注册中...":"注册账户"})]}),(0,s.jsx)(p.f,{y:6}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-gray-600",children:["已有账户？"," ",(0,s.jsx)(n(),{href:"/auth/signin",className:"text-blue-600 hover:text-blue-500 font-medium",children:"立即登录"})]})}),(0,s.jsx)(p.f,{y:4}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(u.R,{size:"sm",color:"primary",variant:"flat",children:"\uD83D\uDCA1"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900 mb-1",children:"关于邀请码"}),(0,s.jsx)("p",{className:"text-blue-700 text-sm",children:"邀请码用于确保平台安全性。如果您没有邀请码，请联系现有用户或管理员获取。"})]})]})})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)(n(),{href:"/",className:"text-gray-500 hover:text-gray-700",children:"← 返回首页"})})]})})}},24322:(e,r,a)=>{Promise.resolve().then(a.bind(a,22445))}},e=>{var r=r=>e(e.s=r);e.O(0,[8031,4110,1358,7443,7436,390,110,7358],()=>r(24322)),_N_E=e.O()}]);