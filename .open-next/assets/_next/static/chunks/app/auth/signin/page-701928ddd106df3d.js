(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{1632:(e,r,s)=>{"use strict";s.d(r,{f:()=>c});var a={px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},t=s(3208),l=(0,s(92610).tv)({base:"w-px h-px inline-block",variants:{isInline:{true:"inline-block",false:"block"}},defaultVariants:{isInline:!1}}),n=s(18884),i=s(9585),o=e=>{var r;return null!=(r=a[e])?r:e},d=s(19605),m=(0,t.Rf)((e,r)=>{let{Component:s,getSpacerProps:a}=function(e){let[r,s]=(0,t.rE)(e,l.variantKeys),{as:a,className:d,x:m=1,y:c=1,...u}=r,h=(0,i.useMemo)(()=>l({...s,className:d}),[(0,n.t6)(s),d]),x=o(m),p=o(c);return{Component:a||"span",getSpacerProps:(e={})=>({...e,...u,"aria-hidden":(0,n.sE)(!0),className:(0,n.$z)(h,e.className),style:{...e.style,...u.style,marginLeft:x,marginTop:p}})}}({...e});return(0,d.jsx)(s,{ref:r,...a()})});m.displayName="HeroUI.Spacer";var c=m},2370:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var a=s(19605),t=s(9585),l=s(55935),n=s(72031),i=s(46762),o=s.n(i),d=s(56995),m=s(19864),c=s(21598),u=s(66700),h=s(63707),x=s(11524),p=s(1632),f=s(54110);function v(){let e=(0,l.useRouter)(),{login:r}=(0,n.h)(),[s,i]=(0,t.useState)({username:"",password:"",mfaCode:""}),[v,b]=(0,t.useState)(!1),[j,g]=(0,t.useState)(""),[y,N]=(0,t.useState)(!1),[w,C]=(0,t.useState)(!1),S=async a=>{if(a.preventDefault(),g(""),!s.username.trim())return void g("请输入用户名");if(!s.password.trim())return void g("请输入密码");if(y&&!s.mfaCode.trim())return void g("请输入MFA验证码");try{b(!0);let a=await fetch("/api/auth/signin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:s.username.trim(),password:s.password,mfaCode:s.mfaCode.trim()||void 0,rememberMe:w})}),t=await a.json();a.ok?t.requireMFA&&!y?(N(!0),g("")):(r(t.user),"super_admin"===t.user.role||"agent"===t.user.role?e.push("/admin"):e.push("/wallet")):g(t.error||"登录失败")}catch(e){console.error("登录失败:",e),g("登录失败，请重试")}finally{b(!1)}},k=(e,r)=>{i(s=>({...s,[e]:r})),j&&g("")};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)(d.Z,{className:"shadow-2xl",children:[(0,a.jsx)(m.d,{className:"text-center pb-2",children:(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"登录账户"}),(0,a.jsx)("p",{className:"text-gray-600",children:"访问您的TRON钱包"})]})}),(0,a.jsxs)(c.U,{className:"px-8 py-6",children:[(0,a.jsxs)("form",{onSubmit:S,className:"space-y-6",children:[j&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:j})}),(0,a.jsx)(u.r,{label:"用户名",placeholder:"输入用户名",value:s.username,onChange:e=>k("username",e.target.value),isRequired:!0,variant:"bordered",disabled:y}),(0,a.jsx)(u.r,{label:"密码",type:"password",placeholder:"输入密码",value:s.password,onChange:e=>k("password",e.target.value),isRequired:!0,variant:"bordered",disabled:y}),y&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(h.R,{size:"sm",color:"primary",variant:"flat",children:"\uD83D\uDD10"}),(0,a.jsx)("span",{className:"font-medium text-blue-900",children:"需要MFA验证"})]}),(0,a.jsx)("p",{className:"text-blue-700 text-sm",children:"请打开您的身份验证器应用，输入6位验证码"})]}),(0,a.jsx)(u.r,{label:"MFA验证码",placeholder:"输入6位验证码",value:s.mfaCode,onChange:e=>k("mfaCode",e.target.value),isRequired:!0,variant:"bordered",maxLength:6,description:"来自Google Authenticator或其他验证器应用"})]}),!y&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(x.A,{isSelected:w,onValueChange:C,size:"sm",children:"记住我"}),(0,a.jsx)(o(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"忘记密码？"})]}),(0,a.jsx)(p.f,{y:2}),(0,a.jsx)(f.T,{type:"submit",color:"primary",size:"lg",className:"w-full",isLoading:v,disabled:v,children:v?"登录中...":y?"验证并登录":"登录"}),y&&(0,a.jsx)(f.T,{type:"button",variant:"light",size:"lg",className:"w-full",onPress:()=>{N(!1),i(e=>({...e,mfaCode:""})),g("")},children:"返回重新登录"})]}),(0,a.jsx)(p.f,{y:6}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["还没有账户？"," ",(0,a.jsx)(o(),{href:"/auth/register",className:"text-blue-600 hover:text-blue-500 font-medium",children:"立即注册"})]})})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsx)(o(),{href:"/",className:"text-gray-500 hover:text-gray-700",children:"← 返回首页"})})]})})}},70468:(e,r,s)=>{"use strict";s.d(r,{P:()=>t});var a=s(18884);function t(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return e=>{r.forEach(r=>(function(e,r){if(null!=e){if((0,a.Tn)(e))return void e(r);try{e.current=r}catch(s){throw Error("Cannot assign value '".concat(r,"' to ref '").concat(e,"'"))}}})(r,e))}}},72031:(e,r,s)=>{"use strict";s.d(r,{A:()=>n,h:()=>i});var a=s(19605),t=s(9585);let l=(0,t.createContext)(void 0);function n(e){let{children:r}=e,[s,n]=(0,t.useState)(null),[i,o]=(0,t.useState)(!0);(0,t.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let r=await e.json();n(r.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{o(!1)}};return(0,a.jsx)(l.Provider,{value:{user:s,loading:i,login:e=>{n(e)},logout:()=>{n(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:r})}function i(){let e=(0,t.useContext)(l);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}},79127:(e,r,s)=>{Promise.resolve().then(s.bind(s,2370))}},e=>{var r=r=>e(e.s=r);e.O(0,[8031,4110,1358,7443,7436,1524,390,110,7358],()=>r(79127)),_N_E=e.O()}]);