(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2158],{21598:(e,s,l)=>{"use strict";l.d(s,{U:()=>d});var a=l(31663),r=l(3208),t=l(23883),i=l(18884),o=l(19605),n=(0,r.Rf)((e,s)=>{var l;let{as:r,className:n,children:d,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:h}=(0,a.f)(),x=(0,i.$z)(null==h?void 0:h.body,n);return(0,o.jsx)(r||"div",{ref:u,className:null==(l=b.body)?void 0:l.call(b,{class:x}),...c,children:d})});n.displayName="HeroUI.CardBody";var d=n},31663:(e,s,l)=>{"use strict";l.d(s,{f:()=>r,u:()=>a});var[a,r]=(0,l(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},47314:(e,s,l)=>{"use strict";l.d(s,{P:()=>r});var a=l(9585);function r(e,s,l){let[r,t]=(0,a.useState)(e||s),i=(0,a.useRef)(void 0!==e),o=void 0!==e;(0,a.useEffect)(()=>{i.current,i.current=o},[o]);let n=o?e:r,d=(0,a.useCallback)((e,...s)=>{let a=(e,...s)=>{l&&!Object.is(n,e)&&l(e,...s),o||(n=e)};"function"==typeof e?t((l,...r)=>{let t=e(o?n:l,...r);return(a(t,...s),o)?l:t}):(o||t(e),a(e,...s))},[o,n,l]);return[n,d]}},52701:(e,s,l)=>{Promise.resolve().then(l.bind(l,68472))},56995:(e,s,l)=>{"use strict";l.d(s,{Z:()=>y});var a=l(31663),r=l(92610),t=l(56457),i=(0,r.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),o=l(9585),n=l(26423),d=l(96539),c=l(7484),u=l(85823),b=l(90890),h=l(31081),x=l(3208),f=l(18884),m=l(9733),g=l(23883),p=l(32965),j=l(14171),v=l(19605),w=(0,x.Rf)((e,s)=>{let{children:l,context:r,Component:t,isPressable:w,disableAnimation:y,disableRipple:N,getCardProps:k,getRippleProps:P}=function(e){var s,l,a,r;let t=(0,h.o)(),[j,v]=(0,x.rE)(e,i.variantKeys),{ref:w,as:y,children:N,onClick:k,onPress:P,autoFocus:C,className:D,classNames:E,allowTextSelectionOnPress:S=!0,...B}=j,H=(0,g.zD)(w),z=y||(e.isPressable?"button":"div"),U="string"==typeof z,O=null!=(l=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&l,W=null!=(r=null!=(a=e.disableRipple)?a:null==t?void 0:t.disableRipple)&&r,_=(0,f.$z)(null==E?void 0:E.base,D),{onClear:F,onPress:R,ripples:T}=(0,p.k)(),Z=(0,o.useCallback)(e=>{W||O||H.current&&R(e)},[W,O,H,R]),{buttonProps:I,isPressed:M}=(0,b.l)({onPress:(0,n.c)(P,Z),elementType:y,isDisabled:!e.isPressable,onClick:k,allowTextSelectionOnPress:S,...B},H),{hoverProps:$,isHovered:A}=(0,u.M)({isDisabled:!e.isHoverable,...B}),{isFocusVisible:J,isFocused:V,focusProps:q}=(0,c.o)({autoFocus:C}),K=(0,o.useMemo)(()=>i({...v,disableAnimation:O}),[(0,f.t6)(v),O]),Y=(0,o.useMemo)(()=>({slots:K,classNames:E,disableAnimation:O,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[K,E,e.isDisabled,e.isFooterBlurred,O,e.fullWidth]),G=(0,o.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:H,className:K.base({class:_}),tabIndex:e.isPressable?0:-1,"data-hover":(0,f.sE)(A),"data-pressed":(0,f.sE)(M),"data-focus":(0,f.sE)(V),"data-focus-visible":(0,f.sE)(J),"data-disabled":(0,f.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{...I,...q,role:"button"}:{},e.isHoverable?$:{},(0,m.$)(B,{enabled:U}),(0,m.$)(s))}},[H,K,_,U,e.isPressable,e.isHoverable,e.isDisabled,A,M,J,I,q,$,B]),L=(0,o.useCallback)(()=>({ripples:T,onClear:F}),[T,F]);return{context:Y,domRef:H,Component:z,classNames:E,children:N,isHovered:A,isPressed:M,disableAnimation:O,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:W,handlePress:Z,isFocusVisible:J,getCardProps:G,getRippleProps:L}}({...e,ref:s});return(0,v.jsxs)(t,{...k(),children:[(0,v.jsx)(a.u,{value:r,children:l}),w&&!y&&!N&&(0,v.jsx)(j.j,{...P()})]})});w.displayName="HeroUI.Card";var y=w},68472:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>b});var a=l(19605),r=l(9585),t=l(56995),i=l(21598),o=l(54110),n=l(90536),d=l(91102),c=l(63833),u=l(93072);function b(){let[e,s]=(0,r.useState)(null),[l,b]=(0,r.useState)([]),[h,x]=(0,r.useState)(null),[f,m]=(0,r.useState)(!1),[g,p]=(0,r.useState)(!0);(0,r.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/brush/balance");if(e.ok){let l=await e.json();s(l.data),console.log("余额数据:",l.data)}let l=await fetch("/api/wallets");if(l.ok){let e=await l.json();e.success&&e.data&&(b(e.data),console.log("钱包数据:",e.data))}let a=await fetch("/api/brush/recharge?network=mainnet");if(a.ok){let e=await a.json();x(e.data),console.log("充值信息:",e.data)}p(!1)}catch(e){console.error("加载数据失败:",e),p(!1)}},v=()=>{console.log("检查弹窗条件:"),console.log("- userBalance:",e),console.log("- userWallets:",l),console.log("- rechargeInfo:",h),e&&e.balance<10?(console.log("余额不足，应该显示弹窗"),m(!0)):console.log("余额充足")};return(0,r.useEffect)(()=>{!g&&e&&h&&setTimeout(v,1e3)},[g,e,h]),(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"测试刷单弹窗"}),(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsx)(t.Z,{children:(0,a.jsxs)(i.U,{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"数据状态"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"加载状态:"})," ",g?"加载中...":"已完成"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"用户余额:"})," ",e?"".concat(e.balance," 元"):"未加载"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"钱包数量:"})," ",l.length]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"充值信息:"})," ",h?"已加载":"未加载"]})]})]})}),(0,a.jsx)(t.Z,{children:(0,a.jsxs)(i.U,{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"测试按钮"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(o.T,{color:"primary",onPress:v,children:"手动检查弹窗"}),(0,a.jsx)(o.T,{color:"success",onPress:()=>m(!0),children:"强制显示弹窗"}),(0,a.jsx)(o.T,{color:"warning",onPress:j,children:"重新加载数据"})]})]})}),e&&(0,a.jsx)(t.Z,{children:(0,a.jsxs)(i.U,{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"余额详情"}),(0,a.jsx)("pre",{className:"text-xs bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)})]})}),l.length>0&&(0,a.jsx)(t.Z,{children:(0,a.jsxs)(i.U,{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"钱包详情"}),(0,a.jsx)("pre",{className:"text-xs bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(l,null,2)})]})})]}),(0,a.jsx)(n.Y,{isOpen:f,onClose:()=>m(!1),size:"lg",children:(0,a.jsxs)(d.g,{children:[(0,a.jsx)(c.c,{children:(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"\uD83D\uDE80 测试弹窗"})}),(0,a.jsx)(u.h,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-blue-800",children:"这是测试弹窗，用于验证弹窗显示逻辑是否正常。"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"当前余额:"})," ",(null==e?void 0:e.balance)||0," 元"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"是否应该显示:"})," ",(null==e?void 0:e.balance)<10?"是":"否"]})]})]})})]})})]})})}},74116:(e,s,l)=>{"use strict";l.d(s,{b:()=>r});var a=l(24215);function r(e,s){let{id:l,"aria-label":r,"aria-labelledby":t}=e;return l=(0,a.Bi)(l),t&&r?t=[...new Set([l,...t.trim().split(/\s+/)])].join(" "):t&&(t=t.trim().split(/\s+/).join(" ")),r||t||!s||(r=s),{id:l,"aria-label":r,"aria-labelledby":t}}}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,4273,3748,390,110,7358],()=>s(52701)),_N_E=e.O()}]);