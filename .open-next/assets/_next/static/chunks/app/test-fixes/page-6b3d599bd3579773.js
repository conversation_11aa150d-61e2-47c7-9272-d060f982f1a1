(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2297],{21598:(e,s,a)=>{"use strict";a.d(s,{U:()=>o});var r=a(31663),l=a(3208),i=a(23883),t=a(18884),d=a(19605),n=(0,l.Rf)((e,s)=>{var a;let{as:l,className:n,children:o,...c}=e,u=(0,i.zD)(s),{slots:h,classNames:x}=(0,r.f)(),m=(0,t.$z)(null==x?void 0:x.body,n);return(0,d.jsx)(l||"div",{ref:u,className:null==(a=h.body)?void 0:a.call(h,{class:m}),...c,children:o})});n.displayName="HeroUI.CardBody";var o=n},28817:(e,s,a)=>{"use strict";a.d(s,{o:()=>l});var r=a(19605),l=e=>(0,r.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,r.jsx)("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})})},28863:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>h});var r=a(19605),l=a(9585),i=a(56995),t=a(19864),d=a(21598),n=a(66700),o=a(54110),c=a(89213),u=a(76562);function h(){let[e,s]=(0,l.useState)("TAjz662ivGjK792yaiTaeJcthDD4wTtX4m"),[a,h]=(0,l.useState)(null),x=async()=>{try{let e=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"testuser_"+Date.now(),password:"test123456",email:"<EMAIL>",name:"测试用户",inviteCode:"NORMAL_CODE"})}),s=await e.json(),a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"admin_"+Date.now(),password:"admin123456",email:"<EMAIL>",name:"管理员用户",inviteCode:"SUPER_ADMIN_INIT"})}),r=await a.json();h({registrationTest:{normal:{success:e.ok,result:s,expectedRole:"user"},admin:{success:a.ok,result:r,expectedRole:"super_admin"}}})}catch(e){h({registrationTest:{error:e}})}};return(0,r.jsx)("div",{className:"container mx-auto p-4 max-w-6xl",children:(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"修复验证测试"})}),(0,r.jsxs)(d.U,{className:"space-y-6",children:[(0,r.jsxs)(i.Z,{className:"bg-blue-50",children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"问题1: 收款二维码显示测试"})}),(0,r.jsxs)(d.U,{className:"space-y-4",children:[(0,r.jsx)(n.r,{label:"钱包地址",value:e,onChange:e=>s(e.target.value),placeholder:"输入TRON地址"}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"bg-white p-4 rounded-lg border",children:(0,r.jsx)(u.A,{value:e,size:200,className:"mx-auto"})})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["地址: ",e]})}),(0,r.jsx)(o.T,{color:"primary",onPress:()=>{h({qrTest:{success:!0,message:"QR码组件测试成功",value:e}})},className:"w-full",children:"测试QR码生成"})]})]}),(0,r.jsxs)(i.Z,{className:"bg-green-50",children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"问题2: 注册用户角色测试"})}),(0,r.jsxs)(d.U,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h3",{className:"font-semibold",children:"普通邀请码"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 邀请码: NORMAL_CODE"}),(0,r.jsx)("li",{children:"• 期望角色: user (普通用户)"}),(0,r.jsx)("li",{children:"• 不应创建管理员记录"})]})})]}),(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h3",{className:"font-semibold",children:"超级管理员邀请码"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 邀请码: SUPER_ADMIN_INIT"}),(0,r.jsx)("li",{children:"• 期望角色: super_admin"}),(0,r.jsx)("li",{children:"• 应创建管理员记录"})]})})]})]}),(0,r.jsx)(o.T,{color:"secondary",onPress:x,className:"w-full",children:"测试注册逻辑"})]})]}),a&&(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"测试结果"})}),(0,r.jsx)(d.U,{children:(0,r.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(a,null,2)})})]}),(0,r.jsx)(c.y,{}),(0,r.jsxs)(i.Z,{className:"bg-yellow-50",children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"修复说明"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800",children:"问题1: 收款二维码没有显示"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"• 安装了 qrcode 和 @types/qrcode 库"}),(0,r.jsx)("li",{children:"• 创建了 QRCodeComponent 组件"}),(0,r.jsx)("li",{children:"• 更新了钱包页面使用真实的QR码"}),(0,r.jsx)("li",{children:"• 支持自定义大小和样式"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-800",children:"问题2: 邀请码注册角色错误"}),(0,r.jsxs)("ul",{className:"text-sm text-green-700 space-y-1 mt-2",children:[(0,r.jsx)("li",{children:"• 修复了注册逻辑，区分普通用户和管理员"}),(0,r.jsx)("li",{children:"• 只有 SUPER_ADMIN_INIT 邀请码创建超级管理员"}),(0,r.jsx)("li",{children:"• 其他邀请码注册的都是普通用户"}),(0,r.jsx)("li",{children:"• 添加了角色信息到注册响应"})]})]})]})})]}),(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(t.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"使用说明"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"收款二维码"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:'在钱包页面点击"收款"按钮，现在会显示真实的二维码， 其他人可以扫描此二维码获取您的钱包地址进行转账。'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"用户注册"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"使用 SUPER_ADMIN_INIT 邀请码注册的用户会成为超级管理员， 使用其他邀请码注册的用户都是普通用户，不会获得管理员权限。"})]})]})})]})]})]})})}},31663:(e,s,a)=>{"use strict";a.d(s,{f:()=>l,u:()=>r});var[r,l]=(0,a(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},36996:(e,s,a)=>{Promise.resolve().then(a.bind(a,28863))},56995:(e,s,a)=>{"use strict";a.d(s,{Z:()=>y});var r=a(31663),l=a(92610),i=a(56457),t=(0,l.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...i.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),d=a(9585),n=a(26423),o=a(96539),c=a(7484),u=a(85823),h=a(90890),x=a(31081),m=a(3208),b=a(18884),j=a(9733),f=a(23883),p=a(32965),v=a(14171),g=a(19605),N=(0,m.Rf)((e,s)=>{let{children:a,context:l,Component:i,isPressable:N,disableAnimation:y,disableRipple:w,getCardProps:C,getRippleProps:k}=function(e){var s,a,r,l;let i=(0,x.o)(),[v,g]=(0,m.rE)(e,t.variantKeys),{ref:N,as:y,children:w,onClick:C,onPress:k,autoFocus:P,className:D,classNames:T,allowTextSelectionOnPress:_=!0,...R}=v,E=(0,f.zD)(N),I=y||(e.isPressable?"button":"div"),U="string"==typeof I,A=null!=(a=null!=(s=e.disableAnimation)?s:null==i?void 0:i.disableAnimation)&&a,z=null!=(l=null!=(r=e.disableRipple)?r:null==i?void 0:i.disableRipple)&&l,M=(0,b.$z)(null==T?void 0:T.base,D),{onClear:O,onPress:S,ripples:F}=(0,p.k)(),H=(0,d.useCallback)(e=>{z||A||E.current&&S(e)},[z,A,E,S]),{buttonProps:Z,isPressed:B}=(0,h.l)({onPress:(0,n.c)(k,H),elementType:y,isDisabled:!e.isPressable,onClick:C,allowTextSelectionOnPress:_,...R},E),{hoverProps:W,isHovered:$}=(0,u.M)({isDisabled:!e.isHoverable,...R}),{isFocusVisible:q,isFocused:J,focusProps:Q}=(0,c.o)({autoFocus:P}),V=(0,d.useMemo)(()=>t({...g,disableAnimation:A}),[(0,b.t6)(g),A]),K=(0,d.useMemo)(()=>({slots:V,classNames:T,disableAnimation:A,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[V,T,e.isDisabled,e.isFooterBlurred,A,e.fullWidth]),L=(0,d.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:E,className:V.base({class:M}),tabIndex:e.isPressable?0:-1,"data-hover":(0,b.sE)($),"data-pressed":(0,b.sE)(B),"data-focus":(0,b.sE)(J),"data-focus-visible":(0,b.sE)(q),"data-disabled":(0,b.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...Z,...Q,role:"button"}:{},e.isHoverable?W:{},(0,j.$)(R,{enabled:U}),(0,j.$)(s))}},[E,V,M,U,e.isPressable,e.isHoverable,e.isDisabled,$,B,q,Z,Q,W,R]),G=(0,d.useCallback)(()=>({ripples:F,onClear:O}),[F,O]);return{context:K,domRef:E,Component:I,classNames:T,children:w,isHovered:$,isPressed:B,disableAnimation:A,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:z,handlePress:H,isFocusVisible:q,getCardProps:L,getRippleProps:G}}({...e,ref:s});return(0,g.jsxs)(i,{...C(),children:[(0,g.jsx)(r.u,{value:l,children:a}),N&&!y&&!w&&(0,g.jsx)(v.j,{...k()})]})});N.displayName="HeroUI.Card";var y=N},76562:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});var r=a(19605),l=a(9585),i=a(84884);function t(e){let{value:s,size:a=200,className:t=""}=e,d=(0,l.useRef)(null);return((0,l.useEffect)(()=>{d.current&&s&&i.toCanvas(d.current,s,{width:a,margin:2,color:{dark:"#000000",light:"#FFFFFF"}}).catch(e=>{console.error("生成二维码失败:",e)})},[s,a]),s)?(0,r.jsx)("canvas",{ref:d,className:"border rounded-lg ".concat(t),style:{maxWidth:"100%",height:"auto"}}):(0,r.jsx)("div",{className:"bg-gray-100 flex items-center justify-center ".concat(t),style:{width:a,height:a},children:(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"无效地址"})})}},89213:(e,s,a)=>{"use strict";a.d(s,{y:()=>o});var r=a(9733),l=(0,a(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),i=a(9585),t=a(3208),d=a(19605),n=(0,t.Rf)((e,s)=>{let{Component:a,getDividerProps:t}=function(e){var s;let a,t,{as:d,className:n,orientation:o,...c}=e,u=d||"hr";"hr"===u&&"vertical"===o&&(u="div");let{separatorProps:h}=(s={elementType:"string"==typeof u?u:"hr",orientation:o},t=(0,r.$)(s,{enabled:"string"==typeof s.elementType}),("vertical"===s.orientation&&(a="vertical"),"hr"!==s.elementType)?{separatorProps:{...t,role:"separator","aria-orientation":a}}:{separatorProps:t}),x=(0,i.useMemo)(()=>l({orientation:o,className:n}),[o,n]);return{Component:u,getDividerProps:(0,i.useCallback)((e={})=>({className:x,role:"separator","data-orientation":o,...h,...c,...e}),[x,o,h,c])}}({...e});return(0,d.jsx)(a,{ref:s,...t()})});n.displayName="HeroUI.Divider";var o=n}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,7443,4884,390,110,7358],()=>s(36996)),_N_E=e.O()}]);