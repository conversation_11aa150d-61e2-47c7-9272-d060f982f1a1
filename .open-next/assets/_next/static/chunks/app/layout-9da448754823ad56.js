(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{638:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,72345,23)),Promise.resolve().then(n.bind(n,85599)),Promise.resolve().then(n.bind(n,77081)),Promise.resolve().then(n.bind(n,44132)),Promise.resolve().then(n.bind(n,14384))},3351:(e,t,n)=>{"use strict";n.d(t,{C:()=>h,Y:()=>g});let o=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),r=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function i(e){if(Intl.Locale){let t=new Intl.Locale(e).maximize(),n="function"==typeof t.getTextInfo?t.getTextInfo():t.textInfo;if(n)return"rtl"===n.direction;if(t.script)return o.has(t.script)}let t=e.split("-")[0];return r.has(t)}var l=n(9585),s=n(37285);let a=Symbol.for("react-aria.i18n.locale");function u(){let e="undefined"!=typeof window&&window[a]||"undefined"!=typeof navigator&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:i(e)?"rtl":"ltr"}}let c=u(),d=new Set;function v(){for(let e of(c=u(),d))e(c)}function m(){let e=(0,s.wR)(),[t,n]=(0,l.useState)(c);return((0,l.useEffect)(()=>(0===d.size&&window.addEventListener("languagechange",v),d.add(n),()=>{d.delete(n),0===d.size&&window.removeEventListener("languagechange",v)}),[]),e)?{locale:"en-US",direction:"ltr"}:t}let f=l.createContext(null);function h(e){let{locale:t,children:n}=e,o=m(),r=l.useMemo(()=>t?{locale:t,direction:i(t)?"rtl":"ltr"}:o,[o,t]);return l.createElement(f.Provider,{value:r},n)}function g(){let e=m();return(0,l.useContext)(f)||e}},14384:(e,t,n)=>{"use strict";n.d(t,{default:()=>p});var o=n(19605),r=n(31081),i=n(3351),l=n(56542),s=n(39576),a=n(9585),u=n(84953),c=n(39098),d=n(47535),v=n(56411);function m(e){let{children:t,isValidProp:n,...r}=e;n&&(0,d.D)(n),(r={...(0,a.useContext)(c.Q),...r}).isStatic=(0,v.M)(()=>r.isStatic);let i=(0,a.useMemo)(()=>r,[JSON.stringify(r.transition),r.transformPagePoint,r.reducedMotion]);return(0,o.jsx)(c.Q.Provider,{value:i,children:t})}var f=e=>{let{children:t,navigate:n,disableAnimation:c,useHref:d,disableRipple:v=!1,skipFramerMotionAnimations:f=c,reducedMotion:h="never",validationBehavior:g,locale:p="en-US",labelPlacement:w,defaultDates:E,createCalendar:y,spinnerVariant:x,...b}=e,L=t;n&&(L=(0,o.jsx)(l.pg,{navigate:n,useHref:d,children:L}));let S=(0,a.useMemo)(()=>(c&&f&&(u.W.skipAnimations=!0),{createCalendar:y,defaultDates:E,disableAnimation:c,disableRipple:v,validationBehavior:g,labelPlacement:w,spinnerVariant:x}),[y,null==E?void 0:E.maxDate,null==E?void 0:E.minDate,c,v,g,w,x]);return(0,o.jsx)(r.n,{value:S,children:(0,o.jsx)(i.C,{locale:p,children:(0,o.jsx)(m,{reducedMotion:h,children:(0,o.jsx)(s.so,{...b,children:L})})})})},h=n(72031);function g(){let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{t(!0),"serviceWorker"in navigator&&(navigator.serviceWorker.register("/sw.js",{scope:"/"}).then(e=>{console.log("Service Worker registered successfully:",e),e.addEventListener("updatefound",()=>{let t=e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&(console.log("New version available"),confirm("发现新版本，是否立即更新？")&&(t.postMessage({type:"SKIP_WAITING"}),window.location.reload()))})})}).catch(e=>{console.error("Service Worker registration failed:",e)}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("Service Worker controller changed"),window.location.reload()})),window.addEventListener("appinstalled",()=>{console.log("PWA was installed"),"undefined"!=typeof gtag&&gtag("event","pwa_install",{event_category:"engagement",event_label:"PWA安装成功"})});let e=()=>{console.log("App is online")},n=()=>{console.log("App is offline")};return window.addEventListener("online",e),window.addEventListener("offline",n),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}},[]),null}function p(e){let{children:t}=e;return(0,o.jsx)(f,{children:(0,o.jsxs)(h.A,{children:[(0,o.jsx)(g,{}),t]})})}},39576:(e,t,n)=>{"use strict";n.d(t,{Sf:()=>d,so:()=>c});var o=n(65481),r=n(9585),i=n(23220),l=n(37285);let s=r.createContext(null);function a(e){let{children:t}=e,n=(0,r.useContext)(s),[o,i]=(0,r.useState)(0),l=(0,r.useMemo)(()=>({parent:n,modalCount:o,addModal(){i(e=>e+1),n&&n.addModal()},removeModal(){i(e=>e-1),n&&n.removeModal()}}),[n,o]);return r.createElement(s.Provider,{value:l},t)}function u(e){let t,{modalProviderProps:n}={modalProviderProps:{"aria-hidden":!!(t=(0,r.useContext)(s))&&t.modalCount>0||void 0}};return r.createElement("div",{"data-overlay-container":!0,...e,...n})}function c(e){return r.createElement(a,null,r.createElement(u,e))}function d(e){let t=(0,l.wR)(),{portalContainer:n=t?null:document.body,...s}=e,{getContainer:a}=(0,o.gX)();if(!e.portalContainer&&a&&(n=a()),r.useEffect(()=>{if(null==n?void 0:n.closest("[data-overlay-container]"))throw Error("An OverlayContainer must not be inside another container. Please change the portalContainer prop.")},[n]),!n)return null;let u=r.createElement(c,s);return i.createPortal(u,n)}},44132:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var o=n(9585);function r(){return(0,o.useEffect)(()=>{let e=e=>{e.touches.length>1&&e.preventDefault()},t=()=>{let e=document.querySelector("meta[name=viewport]");e&&e.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover")},n=0,o=e=>{let t=e.target;if(t&&("INPUT"===t.tagName||"TEXTAREA"===t.tagName||"BUTTON"===t.tagName||"SELECT"===t.tagName||t.closest("input")||t.closest("textarea")||t.closest("button")||t.closest('[role="button"]')||t.closest('[role="textbox"]')||t.closest(".heroui-input")||t.closest(".heroui-button")))return;let o=new Date().getTime();o-n<=300&&e.preventDefault(),n=o},r=e=>{e.ctrlKey&&("+"===e.key||"-"===e.key||"0"===e.key)&&e.preventDefault()},i=e=>{e.ctrlKey&&e.preventDefault()},l=e=>{let n=e.target;n&&("INPUT"===n.tagName||"TEXTAREA"===n.tagName||n.closest("input")||n.closest("textarea"))&&(t(),setTimeout(()=>{t()},100))},s=()=>{setTimeout(()=>{t()},300)};return t(),document.addEventListener("touchstart",e,{passive:!1}),document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",o,{passive:!1}),document.addEventListener("keydown",r),document.addEventListener("wheel",i,{passive:!1}),document.addEventListener("focusin",l,{passive:!0}),document.addEventListener("focusout",s,{passive:!0}),()=>{document.removeEventListener("touchstart",e),document.removeEventListener("touchmove",e),document.removeEventListener("touchend",o),document.removeEventListener("keydown",r),document.removeEventListener("wheel",i),document.removeEventListener("focusin",l),document.removeEventListener("focusout",s)}},[]),null}},65481:(e,t,n)=>{"use strict";n.d(t,{gX:()=>i});var o=n(9585);let r=(0,o.createContext)({});function i(){var e;return null!=(e=(0,o.useContext)(r))?e:{}}},72031:(e,t,n)=>{"use strict";n.d(t,{A:()=>l,h:()=>s});var o=n(19605),r=n(9585);let i=(0,r.createContext)(void 0);function l(e){let{children:t}=e,[n,l]=(0,r.useState)(null),[s,a]=(0,r.useState)(!0);(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let t=await e.json();l(t.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{a(!1)}};return(0,o.jsx)(i.Provider,{value:{user:n,loading:s,login:e=>{l(e)},logout:()=>{l(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:t})}function s(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}},72345:()=>{},77081:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var o=n(9585);function r(){return(0,o.useEffect)(()=>{setTimeout(()=>{let e=document.createElement("div");e.style.fontFamily="Geist, system-ui",e.style.position="absolute",e.style.visibility="hidden",e.style.fontSize="16px",e.textContent="Test",document.body.appendChild(e),setTimeout(()=>{window.getComputedStyle(e).fontFamily.includes("Geist")||(console.warn("Geist font failed to load, using system fonts"),document.documentElement.style.setProperty("--font-geist-sans",'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'),document.documentElement.style.setProperty("--font-geist-mono",'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Inconsolata, "Roboto Mono", monospace')),document.body.removeChild(e)},1e3)},2e3)},[]),null}},85599:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var o=n(19605),r=n(9585),i=n(54110);function l(){let[e,t]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{let e=()=>{t((window.pageYOffset||document.documentElement.scrollTop)>300)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),e)?(0,o.jsx)(i.T,{isIconOnly:!0,color:"primary",variant:"shadow",className:"fixed bottom-6 right-6 z-50 w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-300",onPress:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"返回顶部",children:(0,o.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[8048,8031,4110,390,110,7358],()=>t(638)),_N_E=e.O()}]);