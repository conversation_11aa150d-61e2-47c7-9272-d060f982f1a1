(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{67922:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>k});var i=a(19605),l=a(9585),r=a(55935),n=a(13983),t=a(63707),c=a(54110),d=a(70601),o=a(71413),m=a(56995),x=a(19864),h=a(21598),j=a(37751),u=a(52649),p=a(32299),w=a(71084),f=a(32078),v=a(19211),g=a(90536),y=a(91102),N=a(63833),_=a(93072),b=a(66700),S=a(94661),T=a(89213);function z(e){let{users:s,currentUser:a,onToggleStatus:l}=e,r=e=>{switch(e){case"super_admin":return"danger";case"agent":return"primary";default:return"default"}},n=e=>{switch(e){case"super_admin":return"超级管理员";case"agent":return"代理";default:return"普通用户"}};return(0,i.jsxs)("div",{className:"space-y-3",children:[s.map(e=>(0,i.jsx)(m.Z,{className:"w-full",children:(0,i.jsxs)(h.U,{className:"p-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,i.jsx)("h3",{className:"font-semibold text-lg",children:e.username}),(0,i.jsx)(t.R,{size:"sm",color:r(e.role),variant:"flat",children:n(e.role)})]}),e.name&&(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:e.name}),e.email&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]}),(0,i.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,i.jsx)(t.R,{size:"sm",color:e.is_active?"success":"default",variant:"flat",children:e.is_active?"活跃":"禁用"}),"super_admin"===a.role&&e.id!==a.id&&(0,i.jsx)(c.T,{size:"sm",color:e.is_active?"danger":"success",variant:"light",onPress:()=>l(e.id,e.is_active),className:"min-w-16",children:e.is_active?"禁用":"启用"})]})]}),(0,i.jsx)(T.y,{className:"my-3"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-500",children:"注册时间:"}),(0,i.jsx)("p",{className:"font-medium",children:new Date(e.created_at).toLocaleDateString("zh-CN")})]}),e.invite_code&&(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-500",children:"邀请码:"}),(0,i.jsx)("p",{className:"font-medium font-mono text-xs",children:e.invite_code})]}),e.invited_by_username&&(0,i.jsxs)("div",{className:"col-span-2",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"邀请人:"}),(0,i.jsx)("p",{className:"font-medium",children:e.invited_by_username})]})]})]})},e.id)),0===s.length&&(0,i.jsx)(m.Z,{children:(0,i.jsx)(h.U,{className:"text-center py-8",children:(0,i.jsx)("p",{className:"text-gray-500",children:"暂无用户数据"})})})]})}function k(){let e=(0,r.useRouter)(),[s,a]=(0,l.useState)(!0),[T,k]=(0,l.useState)(null),[C,P]=(0,l.useState)([]),[D,U]=(0,l.useState)([]),[O,R]=(0,l.useState)([]),[E,I]=(0,l.useState)(!1),[L,A]=(0,l.useState)(!1),[J,Z]=(0,l.useState)("users"),[X,q]=(0,l.useState)(!1),[F,Y]=(0,l.useState)({username:"",email:"",password:"",name:"",permissions:[]}),[G,H]=(0,l.useState)({maxUses:"1",expiresIn:"30"}),[K,M]=(0,l.useState)(!1);(0,l.useEffect)(()=>{W();let e=()=>{q(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let W=async()=>{try{let s=await fetch("/api/admin/check");if(s.ok){let a=await s.json();a.isAdmin?(k(a.user),await B()):e.push("/auth/signin")}else e.push("/auth/signin")}catch(s){console.error("检查管理员权限失败:",s),e.push("/auth/signin")}finally{a(!1)}},B=async()=>{try{let e=await fetch("/api/admin/users");if(e.ok){let s=await e.json();s.success&&s.data&&P(s.data)}let s=await fetch("/api/admin/wallets");if(s.ok){let e=await s.json();e.success&&e.data&&U(e.data)}let a=await fetch("/api/admin/invite-codes");if(a.ok){let e=await a.json();e.success&&e.data&&R(e.data)}}catch(e){console.error("加载数据失败:",e)}},Q=async()=>{if(!F.username.trim()||!F.password.trim())return void alert("请填写用户名和密码");try{M(!0);let e=await fetch("/api/admin/create-agent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(F)});if(e.ok)alert("代理创建成功！"),I(!1),Y({username:"",email:"",password:"",name:"",permissions:[]}),await B();else{let s=await e.json();alert("创建失败: ".concat(s.message))}}catch(e){console.error("创建代理失败:",e),alert("创建代理失败，请重试")}finally{M(!1)}},V=async(e,s)=>{try{let a=await fetch("/api/admin/wallets",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({walletId:e,isActive:!s})});if(a.ok)await B(),alert("钱包已".concat(s?"禁用":"启用"));else{let e=await a.json();alert("操作失败: ".concat(e.message))}}catch(e){console.error("切换钱包状态失败:",e),alert("操作失败，请重试")}},$=async()=>{try{M(!0);let e=await fetch("/api/admin/create-invite-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({maxUses:parseInt(G.maxUses),expiresIn:parseInt(G.expiresIn)})});if(e.ok){let s=await e.json();alert("邀请码创建成功！\n邀请码: ".concat(s.code)),A(!1),H({maxUses:"1",expiresIn:"30"}),await B()}else{let s=await e.json();alert("创建失败: ".concat(s.message))}}catch(e){console.error("创建邀请码失败:",e),alert("创建邀请码失败，请重试")}finally{M(!1)}},ee=async(e,s)=>{try{let a=await fetch("/api/admin/toggle-user-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,isActive:!s})});if(a.ok)await B();else{let e=await a.json();alert("操作失败: ".concat(e.message))}}catch(e){console.error("切换用户状态失败:",e),alert("操作失败，请重试")}};return s?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsx)(n.o,{size:"lg"})}):T?(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-4 px-2 sm:py-8 sm:px-4",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 space-y-4 sm:space-y-0",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-xl sm:text-3xl font-bold text-gray-900",children:"管理后台"}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center mt-1",children:[(0,i.jsxs)("p",{className:"text-gray-600",children:["欢迎，",T.name||T.username]}),(0,i.jsx)(t.R,{size:"sm",color:"primary",variant:"flat",className:"mt-1 sm:mt-0 sm:ml-2 w-fit",children:"super_admin"===T.role?"超级管理员":"代理"})]})]}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[(0,i.jsx)(c.T,{color:"warning",variant:"solid",onPress:()=>e.push("/admin/brush"),size:"sm",className:"w-full sm:w-auto font-semibold",children:"\uD83C\uDFAF 刷单管理"}),(0,i.jsx)(c.T,{color:"primary",variant:"bordered",onPress:()=>e.push("/wallet"),size:"sm",className:"w-full sm:w-auto",children:"返回钱包"}),(0,i.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>{document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/"},size:"sm",className:"w-full sm:w-auto",children:"退出登录"})]})]}),(0,i.jsxs)(d.r,{selectedKey:J,onSelectionChange:e=>Z(e),className:"w-full",children:[(0,i.jsx)(o.i,{title:"用户管理",children:(0,i.jsxs)(m.Z,{children:[(0,i.jsxs)(x.d,{className:"flex justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"用户列表"}),"super_admin"===T.role&&(0,i.jsx)(c.T,{color:"primary",onPress:()=>I(!0),children:"创建代理"})]}),(0,i.jsx)(h.U,{children:X?(0,i.jsx)(z,{users:C,currentUser:T,onToggleStatus:ee}):(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)(j.j,{"aria-label":"用户列表",className:"min-w-full",children:[(0,i.jsxs)(u.X,{children:[(0,i.jsx)(p.e,{className:"min-w-[100px]",children:"用户名"}),(0,i.jsx)(p.e,{className:"min-w-[150px]",children:"邮箱"}),(0,i.jsx)(p.e,{className:"min-w-[100px]",children:"角色"}),(0,i.jsx)(p.e,{className:"min-w-[80px]",children:"状态"}),(0,i.jsx)(p.e,{className:"min-w-[100px]",children:"注册时间"}),(0,i.jsx)(p.e,{className:"min-w-[80px]",children:"操作"})]}),(0,i.jsx)(w.E,{children:C.map(e=>(0,i.jsxs)(f.s,{children:[(0,i.jsx)(v.w,{children:e.username}),(0,i.jsx)(v.w,{children:e.email||"-"}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:"super_admin"===e.role?"danger":"agent"===e.role?"primary":"default",variant:"flat",children:"super_admin"===e.role?"超级管理员":"agent"===e.role?"代理":"普通用户"})}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:e.is_active?"success":"default",variant:"flat",children:e.is_active?"活跃":"禁用"})}),(0,i.jsx)(v.w,{children:new Date(e.created_at).toLocaleDateString()}),(0,i.jsx)(v.w,{children:"super_admin"===T.role&&e.id!==T.id&&(0,i.jsx)(c.T,{size:"sm",color:e.is_active?"danger":"success",variant:"light",onPress:()=>ee(e.id,e.is_active),children:e.is_active?"禁用":"启用"})})]},e.id))})]})})})]})},"users"),(0,i.jsx)(o.i,{title:"钱包管理",children:(0,i.jsxs)(m.Z,{children:[(0,i.jsx)(x.d,{children:(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"钱包列表"})}),(0,i.jsx)(h.U,{children:(0,i.jsxs)(j.j,{"aria-label":"钱包列表",children:[(0,i.jsxs)(u.X,{children:[(0,i.jsx)(p.e,{children:"钱包名称"}),(0,i.jsx)(p.e,{children:"地址"}),(0,i.jsx)(p.e,{children:"用户"}),(0,i.jsx)(p.e,{children:"类型"}),(0,i.jsx)(p.e,{children:"网络"}),(0,i.jsx)(p.e,{children:"状态"}),(0,i.jsx)(p.e,{children:"创建时间"}),(0,i.jsx)(p.e,{children:"操作"})]}),(0,i.jsx)(w.E,{children:D.map(e=>(0,i.jsxs)(f.s,{children:[(0,i.jsx)(v.w,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.name}),e.is_default&&(0,i.jsx)(t.R,{size:"sm",color:"warning",variant:"flat",children:"默认"})]})}),(0,i.jsx)(v.w,{children:(0,i.jsxs)("div",{className:"font-mono text-sm",children:[e.address.slice(0,8),"...",e.address.slice(-6)]})}),(0,i.jsx)(v.w,{children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.username}),e.user_name&&(0,i.jsx)("div",{className:"text-sm text-gray-500",children:e.user_name})]})}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:"created"===e.wallet_type?"primary":"secondary",variant:"flat",children:"created"===e.wallet_type?"创建":"导入"})}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:"mainnet"===e.network?"success":"nile"===e.network?"primary":"warning",variant:"flat",children:"mainnet"===e.network?"主网":"nile"===e.network?"Nile":"Shasta"})}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:e.is_active?"success":"danger",variant:"flat",children:e.is_active?"正常":"禁用"})}),(0,i.jsx)(v.w,{children:new Date(e.created_at).toLocaleDateString()}),(0,i.jsx)(v.w,{children:"super_admin"===T.role&&(0,i.jsx)(c.T,{size:"sm",color:e.is_active?"danger":"success",variant:"light",onPress:()=>V(e.id,e.is_active),children:e.is_active?"禁用":"启用"})})]},e.id))})]})})]})},"wallets"),(0,i.jsx)(o.i,{title:"邀请码管理",children:(0,i.jsxs)(m.Z,{children:[(0,i.jsxs)(x.d,{className:"flex justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"邀请码列表"}),(0,i.jsx)(c.T,{color:"primary",onPress:()=>A(!0),children:"创建邀请码"})]}),(0,i.jsx)(h.U,{children:(0,i.jsxs)(j.j,{"aria-label":"邀请码列表",children:[(0,i.jsxs)(u.X,{children:[(0,i.jsx)(p.e,{children:"邀请码"}),(0,i.jsx)(p.e,{children:"最大使用次数"}),(0,i.jsx)(p.e,{children:"已使用次数"}),(0,i.jsx)(p.e,{children:"状态"}),(0,i.jsx)(p.e,{children:"过期时间"}),(0,i.jsx)(p.e,{children:"创建时间"})]}),(0,i.jsx)(w.E,{children:O.map(e=>(0,i.jsxs)(f.s,{children:[(0,i.jsx)(v.w,{children:(0,i.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded text-sm",children:e.code})}),(0,i.jsx)(v.w,{children:-1===e.max_uses?"无限制":e.max_uses}),(0,i.jsx)(v.w,{children:e.used_count}),(0,i.jsx)(v.w,{children:(0,i.jsx)(t.R,{size:"sm",color:e.is_active?"success":"default",variant:"flat",children:e.is_active?"活跃":"已失效"})}),(0,i.jsx)(v.w,{children:e.expires_at?new Date(e.expires_at).toLocaleDateString():"永不过期"}),(0,i.jsx)(v.w,{children:new Date(e.created_at).toLocaleDateString()})]},e.id))})]})})]})},"invites")]}),(0,i.jsx)(g.Y,{isOpen:E,onClose:()=>I(!1),children:(0,i.jsxs)(y.g,{children:[(0,i.jsx)(N.c,{children:"创建代理"}),(0,i.jsxs)(_.h,{children:[(0,i.jsx)(b.r,{label:"用户名",placeholder:"输入用户名",value:F.username,onChange:e=>Y({...F,username:e.target.value})}),(0,i.jsx)(b.r,{label:"邮箱",type:"email",placeholder:"输入邮箱（可选）",value:F.email,onChange:e=>Y({...F,email:e.target.value})}),(0,i.jsx)(b.r,{label:"显示名称",placeholder:"输入显示名称（可选）",value:F.name,onChange:e=>Y({...F,name:e.target.value})}),(0,i.jsx)(b.r,{label:"密码",type:"password",placeholder:"输入密码",value:F.password,onChange:e=>Y({...F,password:e.target.value})})]}),(0,i.jsxs)(S.q,{children:[(0,i.jsx)(c.T,{variant:"light",onPress:()=>I(!1),children:"取消"}),(0,i.jsx)(c.T,{color:"primary",onPress:Q,isLoading:K,children:"创建代理"})]})]})}),(0,i.jsx)(g.Y,{isOpen:L,onClose:()=>A(!1),children:(0,i.jsxs)(y.g,{children:[(0,i.jsx)(N.c,{children:"创建邀请码"}),(0,i.jsxs)(_.h,{children:[(0,i.jsx)(b.r,{label:"最大使用次数",type:"number",placeholder:"输入最大使用次数",value:G.maxUses,onChange:e=>H({...G,maxUses:e.target.value}),description:"输入-1表示无限制"}),(0,i.jsx)(b.r,{label:"有效期（天）",type:"number",placeholder:"输入有效期天数",value:G.expiresIn,onChange:e=>H({...G,expiresIn:e.target.value}),description:"输入0表示永不过期"})]}),(0,i.jsxs)(S.q,{children:[(0,i.jsx)(c.T,{variant:"light",onPress:()=>A(!1),children:"取消"}),(0,i.jsx)(c.T,{color:"primary",onPress:$,isLoading:K,children:"创建邀请码"})]})]})})]})}):null}},85839:(e,s,a)=>{Promise.resolve().then(a.bind(a,67922))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,7443,4273,8688,201,3748,9586,4936,1524,5093,390,110,7358],()=>s(85839)),_N_E=e.O()}]);