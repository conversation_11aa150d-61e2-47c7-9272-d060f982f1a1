(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2413],{51910:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>q});var l=a(19605),t=a(9585),r=a(72031),i=a(55935),n=a(13983),c=a(54110),d=a(70601),o=a(71413),x=a(57059),m=a(5230),h=a(56995),j=a(21598),u=a(19864),p=a(37751),g=a(52649),v=a(32299),f=a(71084),y=a(32078),N=a(19211),b=a(63707),w=a(88865);function S(){let[e,s]=(0,t.useState)(!0),[a,r]=(0,t.useState)("today"),[i,d]=(0,t.useState)(null),[o,S]=(0,t.useState)([]),[C,T]=(0,t.useState)([]),[D,k]=(0,t.useState)(null);(0,t.useEffect)(()=>{R()},[a]);let R=async()=>{try{s(!0);let e=await fetch("/api/admin/brush-stats?period=".concat(a));if(e.ok){let s=await e.json();s.success&&(d(s.data.overview),S(s.data.userRanking),T(s.data.productStats),k(s.data.rechargeStats))}}catch(e){console.error("加载统计数据失败:",e)}finally{s(!1)}};return e?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"数据统计"}),(0,l.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,l.jsx)(x.d,{label:"统计周期",selectedKeys:[a],onSelectionChange:e=>r(Array.from(e)[0]),className:"w-32",size:"sm",children:[{key:"today",label:"今日"},{key:"week",label:"本周"},{key:"month",label:"本月"},{key:"all",label:"全部"}].map(e=>(0,l.jsx)(m.y,{value:e.key,children:e.label},e.key))}),(0,l.jsx)(c.T,{size:"sm",onPress:R,children:"刷新"})]})]}),i&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:i.orders.total}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"总订单数"}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("p",{className:"text-xs text-green-600",children:["完成率: ",i.orders.completionRate,"%"]}),(0,l.jsxs)("p",{className:"text-xs text-red-600",children:["爆单率: ",i.orders.burstRate,"%"]})]})]})})}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("p",{className:"text-3xl font-bold text-green-600",children:["\xa5",i.revenue.totalAmount.toFixed(2)]}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"总交易额"}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("p",{className:"text-xs text-blue-600",children:["佣金: \xa5",i.revenue.totalCommission.toFixed(2)]}),(0,l.jsxs)("p",{className:"text-xs text-gray-600",children:["平均佣金率: ",i.revenue.avgCommissionRate]})]})]})})}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-3xl font-bold text-purple-600",children:i.users.activeUsers}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"活跃用户"}),(0,l.jsx)("div",{className:"mt-2",children:(0,l.jsxs)("p",{className:"text-xs text-gray-600",children:["平均日订单: ",i.users.avgDailyOrders.toFixed(1)]})})]})})}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("p",{className:"text-3xl font-bold text-orange-600",children:["\xa5",i.balance.totalBalance.toFixed(2)]}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"用户总余额"}),(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("p",{className:"text-xs text-green-600",children:["总充值: \xa5",i.balance.totalRecharged.toFixed(2)]}),(0,l.jsxs)("p",{className:"text-xs text-red-600",children:["总消费: \xa5",i.balance.totalSpent.toFixed(2)]})]})]})})})]}),D&&(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"充值统计"})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:D.totalRecharges}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"总充值次数"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-green-600",children:D.confirmedRecharges}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"已确认"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:D.confirmationRate}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"确认率"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl font-bold text-blue-600",children:D.totalUSDT.toFixed(2)}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"总USDT"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("p",{className:"text-xl font-bold text-purple-600",children:["\xa5",D.totalInternal.toFixed(2)]}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"内部币"})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("p",{className:"text-xl font-bold",children:D.avgRechargeAmount.toFixed(2)}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"平均充值"})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"用户排行榜（按佣金收入）"})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)(p.j,{"aria-label":"用户排行榜",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"用户"}),(0,l.jsx)(v.e,{children:"订单数"}),(0,l.jsx)(v.e,{children:"佣金收入"}),(0,l.jsx)(v.e,{children:"爆单率"})]}),(0,l.jsx)(f.E,{children:o.map((e,s)=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"font-medium",children:["#",s+1," ",e.name]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:["@",e.username]})]})}),(0,l.jsx)(N.w,{children:e.orderCount}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("span",{className:"text-green-600 font-medium",children:["\xa5",e.totalCommission.toFixed(2)]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)(b.R,{color:parseFloat(e.burstRate)>20?"danger":"success",variant:"flat",size:"sm",children:e.burstRate})})]},e.username))})]})})]}),(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"商品销售统计"})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)(p.j,{"aria-label":"商品销售统计",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"商品名称"}),(0,l.jsx)(v.e,{children:"订单数"}),(0,l.jsx)(v.e,{children:"销售额"}),(0,l.jsx)(v.e,{children:"完成率"})]}),(0,l.jsx)(f.E,{children:C.map(e=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsx)("p",{className:"font-medium",children:e.productName})}),(0,l.jsx)(N.w,{children:e.orderCount}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("span",{className:"text-blue-600 font-medium",children:["\xa5",e.totalSales.toFixed(2)]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(w.o,{value:parseFloat(e.completionRate),className:"w-16",size:"sm"}),(0,l.jsx)("span",{className:"text-sm",children:e.completionRate})]})})]},e.productName))})]})})]})]})]})}var C=a(90536),T=a(91102),D=a(63833),k=a(93072),R=a(66700),_=a(49731),P=a(27597),O=a(94661);function A(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[i,d]=(0,t.useState)(!1),[o,x]=(0,t.useState)(null),[m,u]=(0,t.useState)(!1),[w,S]=(0,t.useState)({name:"",image:"",price:"",description:"",status:!0});(0,t.useEffect)(()=>{A()},[]);let A=async()=>{try{r(!0);let e=await fetch("/api/products");if(e.ok){let a=await e.json();if(a.success){let e=a.data.map(e=>({...e,price:e.price/100}));s(e)}}}catch(e){console.error("加载商品失败:",e)}finally{r(!1)}},z=e=>{x(e),S({name:e.name,image:e.image||"",price:e.price.toString(),description:e.description||"",status:1===e.status}),d(!0)},I=async()=>{if(!w.name||!w.price)return void alert("商品名称和价格不能为空");let e=parseFloat(w.price);if(isNaN(e)||e<=0)return void alert("请输入有效的价格");try{u(!0);let s=o?"PUT":"POST",a={name:w.name,image:w.image||void 0,price:e,description:w.description||void 0,status:+!!w.status};o&&(a.id=o.id);let l=await fetch("/api/products",{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(l.ok){let e=await l.json();e.success?(alert(o?"商品更新成功":"商品创建成功"),d(!1),await A()):alert("操作失败: ".concat(e.error))}else{let e=await l.json();alert("操作失败: ".concat(e.error))}}catch(e){console.error("提交失败:",e),alert("操作失败，请重试")}finally{u(!1)}},U=async e=>{if(confirm('确定要删除商品"'.concat(e.name,'"吗？')))try{let s=await fetch("/api/products?id=".concat(e.id),{method:"DELETE"});if(s.ok){let e=await s.json();e.success?(alert("商品删除成功"),await A()):alert("删除失败: ".concat(e.error))}else{let e=await s.json();alert("删除失败: ".concat(e.error))}}catch(e){console.error("删除失败:",e),alert("删除失败，请重试")}},E=e=>new Date(e).toLocaleString("zh-CN");return a?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"商品管理"}),(0,l.jsx)(c.T,{color:"primary",onPress:()=>{x(null),S({name:"",image:"",price:"",description:"",status:!0}),d(!0)},children:"添加商品"})]}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)(p.j,{"aria-label":"商品列表",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"商品信息"}),(0,l.jsx)(v.e,{children:"价格"}),(0,l.jsx)(v.e,{children:"状态"}),(0,l.jsx)(v.e,{children:"创建时间"}),(0,l.jsx)(v.e,{children:"操作"})]}),(0,l.jsx)(f.E,{children:e.map(e=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[e.image&&(0,l.jsx)("img",{src:e.image,alt:e.name,className:"w-12 h-12 object-cover rounded"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.name}),e.description&&(0,l.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:e.description})]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("span",{className:"font-semibold text-red-600",children:["\xa5",e.price.toFixed(2)]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)(b.R,{color:1===e.status?"success":"default",variant:"flat",size:"sm",children:1===e.status?"上架":"下架"})}),(0,l.jsx)(N.w,{children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:E(e.created_at)})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(c.T,{size:"sm",variant:"light",onPress:()=>z(e),children:"编辑"}),(0,l.jsx)(c.T,{size:"sm",color:"danger",variant:"light",onPress:()=>U(e),children:"删除"})]})})]},e.id))})]})})}),(0,l.jsx)(C.Y,{isOpen:i,onClose:()=>d(!1),size:"lg",children:(0,l.jsxs)(T.g,{children:[(0,l.jsx)(D.c,{children:(0,l.jsx)("h3",{className:"text-xl font-semibold",children:o?"编辑商品":"添加商品"})}),(0,l.jsx)(k.h,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(R.r,{label:"商品名称",placeholder:"请输入商品名称",value:w.name,onChange:e=>S(s=>({...s,name:e.target.value})),isRequired:!0}),(0,l.jsx)(R.r,{label:"商品图片",placeholder:"请输入图片URL",value:w.image,onChange:e=>S(s=>({...s,image:e.target.value}))}),(0,l.jsx)(R.r,{label:"商品价格",placeholder:"请输入价格（元）",type:"number",value:w.price,onChange:e=>S(s=>({...s,price:e.target.value})),endContent:(0,l.jsx)("span",{className:"text-gray-500",children:"元"}),isRequired:!0}),(0,l.jsx)(_.P,{label:"商品描述",placeholder:"请输入商品描述",value:w.description,onChange:e=>S(s=>({...s,description:e.target.value})),rows:3}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(P.Z,{isSelected:w.status,onValueChange:e=>S(s=>({...s,status:e}))}),(0,l.jsx)("span",{children:"商品上架"})]})]})}),(0,l.jsxs)(O.q,{children:[(0,l.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>d(!1),children:"取消"}),(0,l.jsx)(c.T,{color:"primary",onPress:I,isLoading:m,children:o?"更新":"创建"})]})]})})]})}var z=a(59758);function I(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[i,d]=(0,t.useState)(0),[o,w]=(0,t.useState)(1),[S]=(0,t.useState)(20),[P,A]=(0,t.useState)(1),[I,U]=(0,t.useState)({status:"",username:"",productName:"",startDate:"",endDate:""}),[E,F]=(0,t.useState)(!1),[L,Z]=(0,t.useState)(null),[q,X]=(0,t.useState)(""),[H,J]=(0,t.useState)(""),[K,M]=(0,t.useState)(!1);(0,t.useEffect)(()=>{Y()},[o,I]);let Y=async()=>{try{r(!0);let e=new URLSearchParams({page:o.toString(),pageSize:S.toString(),...Object.fromEntries(Object.entries(I).filter(e=>{let[s,a]=e;return a}))}),a=await fetch("/api/admin/brush-orders?".concat(e));if(a.ok){let e=await a.json();if(e.success){let a=e.data;s(a.orders),d(a.total),A(a.totalPages)}}}catch(e){console.error("加载订单失败:",e)}finally{r(!1)}},B=e=>{Z(e),X(e.status.toString()),J(""),F(!0)},V=async()=>{if(L&&q)try{M(!0);let e=await fetch("/api/admin/brush-orders",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:L.id,status:parseInt(q),reason:H||void 0})});if(e.ok){let s=await e.json();s.success?(alert("订单状态更新成功"),F(!1),await Y()):alert("更新失败: ".concat(s.error))}else{let s=await e.json();alert("更新失败: ".concat(s.error))}}catch(e){console.error("更新订单状态失败:",e),alert("更新失败，请重试")}finally{M(!1)}},Q=e=>{switch(e){case 0:return"warning";case 1:return"primary";case 2:return"success";case 3:default:return"default";case 4:return"danger"}},G=e=>new Date(e).toLocaleString("zh-CN");return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"订单管理"}),(0,l.jsx)(c.T,{onPress:Y,children:"刷新"})]}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,l.jsx)(x.d,{label:"订单状态",selectedKeys:[I.status],onSelectionChange:e=>U(s=>({...s,status:Array.from(e)[0]})),children:[{key:"",label:"全部状态"},{key:"0",label:"待付款"},{key:"1",label:"已付款"},{key:"2",label:"已完成"},{key:"3",label:"已取消"},{key:"4",label:"爆单"}].map(e=>(0,l.jsx)(m.y,{value:e.key,children:e.label},e.key))}),(0,l.jsx)(R.r,{label:"用户名",placeholder:"搜索用户名",value:I.username,onChange:e=>U(s=>({...s,username:e.target.value}))}),(0,l.jsx)(R.r,{label:"商品名称",placeholder:"搜索商品名称",value:I.productName,onChange:e=>U(s=>({...s,productName:e.target.value}))}),(0,l.jsx)(R.r,{label:"开始日期",type:"date",value:I.startDate,onChange:e=>U(s=>({...s,startDate:e.target.value}))}),(0,l.jsx)(R.r,{label:"结束日期",type:"date",value:I.endDate,onChange:e=>U(s=>({...s,endDate:e.target.value}))})]})})}),(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"订单列表"}),(0,l.jsxs)("span",{className:"text-sm text-gray-500",children:["共 ",i," 条记录"]})]})}),(0,l.jsx)(j.U,{children:a?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(p.j,{"aria-label":"订单列表",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"订单信息"}),(0,l.jsx)(v.e,{children:"用户"}),(0,l.jsx)(v.e,{children:"商品"}),(0,l.jsx)(v.e,{children:"金额"}),(0,l.jsx)(v.e,{children:"状态"}),(0,l.jsx)(v.e,{children:"时间"}),(0,l.jsx)(v.e,{children:"操作"})]}),(0,l.jsx)(f.E,{children:e.map(e=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.order_no}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["ID: ",e.id]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.user_name||e.username}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["@",e.username]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[e.product_image?(0,l.jsx)("img",{src:e.product_image,alt:e.product_name,className:"w-12 h-12 object-cover rounded-lg border border-gray-200",onError:e=>{var s;let a=e.target;a.style.display="none",null==(s=a.nextElementSibling)||s.classList.remove("hidden")}}):null,(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg border border-gray-200 flex items-center justify-center ".concat(e.product_image?"hidden":""),children:(0,l.jsx)("span",{className:"text-lg",children:"\uD83D\uDCE6"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.product_name}),(0,l.jsxs)("p",{className:"text-sm text-gray-500",children:["\xa5",e.product_price.toFixed(2)," \xd7 ",e.quantity]})]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsxs)("p",{className:"font-medium",children:["\xa5",e.total_amount.toFixed(2)]}),(0,l.jsxs)("p",{className:"text-sm text-green-600",children:["佣金: \xa5",e.commission_amount.toFixed(2)]})]})}),(0,l.jsxs)(N.w,{children:[(0,l.jsx)(b.R,{color:Q(e.status),variant:"flat",size:"sm",children:e.status_name}),e.is_burst&&(0,l.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"\uD83D\uDCA5 爆单"})]}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"text-sm",children:[(0,l.jsx)("p",{children:G(e.created_at)}),e.completed_at&&(0,l.jsxs)("p",{className:"text-gray-500",children:["完成: ",G(e.completed_at)]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)(c.T,{size:"sm",variant:"light",onPress:()=>B(e),children:"修改状态"})})]},e.id))})]}),P>1&&(0,l.jsx)("div",{className:"flex justify-center mt-4",children:(0,l.jsx)(z.T,{total:P,page:o,onChange:w})})]})})]}),(0,l.jsx)(C.Y,{isOpen:E,onClose:()=>F(!1),size:"md",children:(0,l.jsxs)(T.g,{children:[(0,l.jsx)(D.c,{children:(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"修改订单状态"})}),(0,l.jsx)(k.h,{children:L&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["订单号: ",L.order_no]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["商品: ",L.product_name]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["用户: ",L.username]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["当前状态: ",L.status_name]})]}),(0,l.jsx)(x.d,{label:"新状态",selectedKeys:[q],onSelectionChange:e=>X(Array.from(e)[0]),children:[{key:"0",label:"待付款"},{key:"1",label:"已付款"},{key:"2",label:"已完成"},{key:"3",label:"已取消"},{key:"4",label:"爆单"}].map(e=>(0,l.jsx)(m.y,{value:e.key,children:e.label},e.key))}),(0,l.jsx)(_.P,{label:"备注原因",placeholder:"请输入修改原因（可选）",value:H,onChange:e=>J(e.target.value),rows:3})]})}),(0,l.jsxs)(O.q,{children:[(0,l.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>F(!1),children:"取消"}),(0,l.jsx)(c.T,{color:"primary",onPress:V,isLoading:K,children:"确认修改"})]})]})})]})}var U=a(98448);function E(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[i,d]=(0,t.useState)(!1),[o,x]=(0,t.useState)(null),[m,u]=(0,t.useState)(!1),[w,S]=(0,t.useState)({name:"",description:"",isActive:!0,burstProbability:.1,burstOrderRange:"3-7",minCommissionRate:.03,maxCommissionRate:.08,dailyOrderLimit:50,minOrderInterval:30,maxOrderInterval:300});(0,t.useEffect)(()=>{A()},[]);let A=async()=>{try{r(!0);let e=await fetch("/api/admin/brush-rules");if(e.ok){let a=await e.json();a.success&&s(a.data)}}catch(e){console.error("加载刷单规则失败:",e)}finally{r(!1)}},z=e=>{x(e),S({name:e.name,description:e.description||"",isActive:e.is_active,burstProbability:e.burst_probability,burstOrderRange:e.burst_order_range,minCommissionRate:e.min_commission_rate,maxCommissionRate:e.max_commission_rate,dailyOrderLimit:e.daily_order_limit,minOrderInterval:e.min_order_interval,maxOrderInterval:e.max_order_interval}),d(!0)},I=async()=>{if(!w.name)return void alert("规则名称不能为空");if(w.minCommissionRate>=w.maxCommissionRate)return void alert("最小佣金比例必须小于最大佣金比例");if(w.minOrderInterval>=w.maxOrderInterval)return void alert("最小下单间隔必须小于最大下单间隔");if(!/^\d+-\d+$/.test(w.burstOrderRange))return void alert('爆单订单范围格式无效，应为 "数字-数字"');try{u(!0);let e=o?"PUT":"POST",s={name:w.name,description:w.description||void 0,burstProbability:w.burstProbability,burstOrderRange:w.burstOrderRange,minCommissionRate:w.minCommissionRate,maxCommissionRate:w.maxCommissionRate,dailyOrderLimit:w.dailyOrderLimit,minOrderInterval:w.minOrderInterval,maxOrderInterval:w.maxOrderInterval};o&&(s.id=o.id,s.isActive=w.isActive);let a=await fetch("/api/admin/brush-rules",{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok){let e=await a.json();e.success?(alert(o?"规则更新成功":"规则创建成功"),d(!1),await A()):alert("操作失败: ".concat(e.error))}else{let e=await a.json();alert("操作失败: ".concat(e.error))}}catch(e){console.error("提交失败:",e),alert("操作失败，请重试")}finally{u(!1)}},E=e=>new Date(e).toLocaleString("zh-CN");return a?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"刷单规则配置"}),(0,l.jsx)(c.T,{color:"primary",onPress:()=>{x(null),S({name:"",description:"",isActive:!0,burstProbability:.1,burstOrderRange:"3-7",minCommissionRate:.03,maxCommissionRate:.08,dailyOrderLimit:50,minOrderInterval:30,maxOrderInterval:300}),d(!0)},children:"添加规则"})]}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)(p.j,{"aria-label":"刷单规则列表",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"规则名称"}),(0,l.jsx)(v.e,{children:"爆单设置"}),(0,l.jsx)(v.e,{children:"佣金比例"}),(0,l.jsx)(v.e,{children:"订单限制"}),(0,l.jsx)(v.e,{children:"状态"}),(0,l.jsx)(v.e,{children:"创建时间"}),(0,l.jsx)(v.e,{children:"操作"})]}),(0,l.jsx)(f.E,{children:e.map(e=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.name}),e.description&&(0,l.jsx)("p",{className:"text-sm text-gray-500",children:e.description})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"text-sm",children:[(0,l.jsxs)("p",{children:["概率: ",(100*e.burst_probability).toFixed(1),"%"]}),(0,l.jsxs)("p",{children:["范围: 第",e.burst_order_range,"单"]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)("div",{className:"text-sm",children:(0,l.jsxs)("p",{children:[(100*e.min_commission_rate).toFixed(1),"% - ",(100*e.max_commission_rate).toFixed(1),"%"]})})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"text-sm",children:[(0,l.jsxs)("p",{children:["每日: ",e.daily_order_limit,"单"]}),(0,l.jsxs)("p",{children:["间隔: ",e.min_order_interval,"-",e.max_order_interval,"秒"]})]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)(b.R,{color:e.is_active?"success":"default",variant:"flat",size:"sm",children:e.is_active?"启用":"禁用"})}),(0,l.jsx)(N.w,{children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:E(e.created_at)})}),(0,l.jsx)(N.w,{children:(0,l.jsx)(c.T,{size:"sm",variant:"light",onPress:()=>z(e),children:"编辑"})})]},e.id))})]})})}),(0,l.jsx)(C.Y,{isOpen:i,onClose:()=>d(!1),size:"2xl",scrollBehavior:"inside",children:(0,l.jsxs)(T.g,{children:[(0,l.jsx)(D.c,{children:(0,l.jsx)("h3",{className:"text-xl font-semibold",children:o?"编辑规则":"添加规则"})}),(0,l.jsx)(k.h,{children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(R.r,{label:"规则名称",placeholder:"请输入规则名称",value:w.name,onChange:e=>S(s=>({...s,name:e.target.value})),isRequired:!0}),o&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(P.Z,{isSelected:w.isActive,onValueChange:e=>S(s=>({...s,isActive:e}))}),(0,l.jsx)("span",{children:"启用规则"})]})]}),(0,l.jsx)(_.P,{label:"规则描述",placeholder:"请输入规则描述",value:w.description,onChange:e=>S(s=>({...s,description:e.target.value})),rows:2}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold",children:"爆单设置"}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"text-sm font-medium",children:["爆单概率: ",(100*w.burstProbability).toFixed(1),"%"]}),(0,l.jsx)(U.Q,{value:100*w.burstProbability,onChange:e=>S(s=>({...s,burstProbability:e/100})),min:0,max:50,step:.1,className:"mt-2"})]}),(0,l.jsx)(R.r,{label:"爆单订单范围",placeholder:"例如: 3-7",value:w.burstOrderRange,onChange:e=>S(s=>({...s,burstOrderRange:e.target.value})),description:"格式: 数字-数字，表示第几单到第几单可能爆单"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold",children:"佣金设置"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"text-sm font-medium",children:["最小佣金比例: ",(100*w.minCommissionRate).toFixed(1),"%"]}),(0,l.jsx)(U.Q,{value:100*w.minCommissionRate,onChange:e=>S(s=>({...s,minCommissionRate:e/100})),min:1,max:20,step:.1,className:"mt-2"})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"text-sm font-medium",children:["最大佣金比例: ",(100*w.maxCommissionRate).toFixed(1),"%"]}),(0,l.jsx)(U.Q,{value:100*w.maxCommissionRate,onChange:e=>S(s=>({...s,maxCommissionRate:e/100})),min:1,max:20,step:.1,className:"mt-2"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"font-semibold",children:"订单限制"}),(0,l.jsx)(R.r,{label:"每日订单限制",type:"number",value:w.dailyOrderLimit.toString(),onChange:e=>S(s=>({...s,dailyOrderLimit:parseInt(e.target.value)||0})),endContent:(0,l.jsx)("span",{className:"text-gray-500",children:"单"})}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsx)(R.r,{label:"最小下单间隔",type:"number",value:w.minOrderInterval.toString(),onChange:e=>S(s=>({...s,minOrderInterval:parseInt(e.target.value)||0})),endContent:(0,l.jsx)("span",{className:"text-gray-500",children:"秒"})}),(0,l.jsx)(R.r,{label:"最大下单间隔",type:"number",value:w.maxOrderInterval.toString(),onChange:e=>S(s=>({...s,maxOrderInterval:parseInt(e.target.value)||0})),endContent:(0,l.jsx)("span",{className:"text-gray-500",children:"秒"})})]})]})]})}),(0,l.jsxs)(O.q,{children:[(0,l.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>d(!1),children:"取消"}),(0,l.jsx)(c.T,{color:"primary",onPress:I,isLoading:m,children:o?"更新":"创建"})]})]})})]})}function F(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[i,d]=(0,t.useState)(!1),[o,u]=(0,t.useState)(null),[w,S]=(0,t.useState)(!1),[A,z]=(0,t.useState)({name:"",address:"",network:"mainnet",currency:"USDT",isActive:!0,isDefault:!1,description:""});(0,t.useEffect)(()=>{I()},[]);let I=async()=>{try{r(!0);let e=await fetch("/api/admin/platform-wallets");if(e.ok){let a=await e.json();a.success&&s(a.data)}}catch(e){console.error("加载平台钱包失败:",e)}finally{r(!1)}},U=e=>{u(e),z({name:e.name,address:e.address,network:e.network,currency:e.currency,isActive:e.is_active,isDefault:e.is_default,description:e.description||""}),d(!0)},E=async()=>{if(!A.name||!A.address)return void alert("钱包名称和地址不能为空");try{S(!0);let e=o?"PUT":"POST",s={name:A.name,address:A.address,network:A.network,currency:A.currency,isActive:A.isActive,isDefault:A.isDefault,description:A.description||void 0};o&&(s.id=o.id);let a=await fetch("/api/admin/platform-wallets",{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok){let e=await a.json();e.success?(alert(o?"钱包更新成功":"钱包创建成功"),d(!1),await I()):alert("操作失败: ".concat(e.error))}else{let e=await a.json();alert("操作失败: ".concat(e.error))}}catch(e){console.error("提交失败:",e),alert("操作失败，请重试")}finally{S(!1)}},F=async e=>{if(confirm('确定要删除钱包"'.concat(e.name,'"吗？')))try{let s=await fetch("/api/admin/platform-wallets?id=".concat(e.id),{method:"DELETE"});if(s.ok){let e=await s.json();e.success?(alert("钱包删除成功"),await I()):alert("删除失败: ".concat(e.error))}else{let e=await s.json();alert("删除失败: ".concat(e.error))}}catch(e){console.error("删除失败:",e),alert("删除失败，请重试")}},L=e=>new Date(e).toLocaleString("zh-CN");return a?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"平台钱包管理"}),(0,l.jsx)(c.T,{color:"primary",onPress:()=>{u(null),z({name:"",address:"",network:"mainnet",currency:"USDT",isActive:!0,isDefault:!1,description:""}),d(!0)},children:"添加钱包"})]}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(j.U,{children:(0,l.jsxs)(p.j,{"aria-label":"平台钱包列表",children:[(0,l.jsxs)(g.X,{children:[(0,l.jsx)(v.e,{children:"钱包信息"}),(0,l.jsx)(v.e,{children:"地址"}),(0,l.jsx)(v.e,{children:"网络/币种"}),(0,l.jsx)(v.e,{children:"状态"}),(0,l.jsx)(v.e,{children:"创建时间"}),(0,l.jsx)(v.e,{children:"操作"})]}),(0,l.jsx)(f.E,{children:e.map(e=>(0,l.jsxs)(y.s,{children:[(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"font-medium",children:e.name}),e.description&&(0,l.jsx)("p",{className:"text-sm text-gray-500",children:e.description})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"font-mono text-sm",children:[(0,l.jsxs)("p",{children:[e.address.slice(0,10),"...",e.address.slice(-8)]}),(0,l.jsx)(c.T,{size:"sm",variant:"light",onPress:()=>navigator.clipboard.writeText(e.address),className:"text-xs p-0 h-auto",children:"复制完整地址"})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)(b.R,{size:"sm",variant:"flat",color:"primary",children:e.network}),(0,l.jsx)(b.R,{size:"sm",variant:"flat",color:"secondary",className:"ml-1",children:e.currency})]})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,l.jsx)(b.R,{color:e.is_active?"success":"default",variant:"flat",size:"sm",children:e.is_active?"启用":"禁用"}),e.is_default&&(0,l.jsx)(b.R,{color:"warning",variant:"flat",size:"sm",children:"默认"})]})}),(0,l.jsx)(N.w,{children:(0,l.jsx)("span",{className:"text-sm text-gray-500",children:L(e.created_at)})}),(0,l.jsx)(N.w,{children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)(c.T,{size:"sm",variant:"light",onPress:()=>U(e),children:"编辑"}),(0,l.jsx)(c.T,{size:"sm",color:"danger",variant:"light",onPress:()=>F(e),children:"删除"})]})})]},e.id))})]})})}),(0,l.jsx)(C.Y,{isOpen:i,onClose:()=>d(!1),size:"lg",children:(0,l.jsxs)(T.g,{children:[(0,l.jsx)(D.c,{children:(0,l.jsx)("h3",{className:"text-xl font-semibold",children:o?"编辑钱包":"添加钱包"})}),(0,l.jsx)(k.h,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(R.r,{label:"钱包名称",placeholder:"请输入钱包名称",value:A.name,onChange:e=>z(s=>({...s,name:e.target.value})),isRequired:!0}),(0,l.jsx)(R.r,{label:"钱包地址",placeholder:"请输入TRON钱包地址",value:A.address,onChange:e=>z(s=>({...s,address:e.target.value})),isRequired:!0}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsx)(x.d,{label:"网络",selectedKeys:[A.network],onSelectionChange:e=>z(s=>({...s,network:Array.from(e)[0]})),isDisabled:!!o,children:[{key:"mainnet",label:"主网 (Mainnet)"},{key:"nile",label:"测试网 (Nile)"},{key:"shasta",label:"测试网 (Shasta)"}].map(e=>(0,l.jsx)(m.y,{value:e.key,children:e.label},e.key))}),(0,l.jsx)(x.d,{label:"币种",selectedKeys:[A.currency],onSelectionChange:e=>z(s=>({...s,currency:Array.from(e)[0]})),isDisabled:!!o,children:[{key:"USDT",label:"USDT"},{key:"TRX",label:"TRX"}].map(e=>(0,l.jsx)(m.y,{value:e.key,children:e.label},e.key))})]}),(0,l.jsx)(_.P,{label:"描述",placeholder:"请输入钱包描述（可选）",value:A.description,onChange:e=>z(s=>({...s,description:e.target.value})),rows:2}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(P.Z,{isSelected:A.isActive,onValueChange:e=>z(s=>({...s,isActive:e}))}),(0,l.jsx)("span",{children:"启用钱包"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(P.Z,{isSelected:A.isDefault,onValueChange:e=>z(s=>({...s,isDefault:e}))}),(0,l.jsx)("span",{children:"设为默认钱包"})]})]}),A.isDefault&&(0,l.jsx)("div",{className:"bg-yellow-50 p-3 rounded-lg",children:(0,l.jsx)("p",{className:"text-sm text-yellow-800",children:"⚠️ 设为默认钱包后，该网络和币种的其他默认钱包将被取消默认状态。"})})]})}),(0,l.jsxs)(O.q,{children:[(0,l.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>d(!1),children:"取消"}),(0,l.jsx)(c.T,{color:"primary",onPress:E,isLoading:w,children:o?"更新":"创建"})]})]})})]})}var L=a(89213);function Z(){let[e,s]=(0,t.useState)(null),[a,r]=(0,t.useState)(!0),[i,d]=(0,t.useState)(!1),[o,p]=(0,t.useState)(!1),[g,v]=(0,t.useState)(!1),[f,y]=(0,t.useState)({txHash:"",userId:"",amount:"",fromAddress:"",toAddress:"",network:"mainnet"});(0,t.useEffect)(()=>{N();let e=setInterval(N,3e4);return()=>clearInterval(e)},[]);let N=async()=>{try{r(!0);let e=await fetch("/api/admin/recharge-monitor");if(e.ok){let a=await e.json();a.success&&s(a.data)}}catch(e){console.error("加载监控状态失败:",e)}finally{r(!1)}},w=async(e,s)=>{try{d(!0);let a=await fetch("/api/admin/recharge-monitor",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,network:s})});if(a.ok){let e=await a.json();e.success?(alert(e.message),await N()):alert("操作失败: ".concat(e.error))}else{let e=await a.json();alert("操作失败: ".concat(e.error))}}catch(e){console.error("操作失败:",e),alert("操作失败，请重试")}finally{d(!1)}},S=async()=>{if(!f.txHash||!f.userId||!f.amount||!f.fromAddress||!f.toAddress)return void alert("请填写完整的充值信息");let e=parseFloat(f.amount);if(isNaN(e)||e<=0)return void alert("请输入有效的充值金额");let s=parseInt(f.userId);if(isNaN(s)||s<=0)return void alert("请输入有效的用户ID");try{v(!0);let a=await fetch("/api/admin/recharge-monitor",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({txHash:f.txHash,userId:s,amount:e,fromAddress:f.fromAddress,toAddress:f.toAddress,network:f.network})});if(a.ok){let e=await a.json();e.success?(alert("手动充值处理成功"),p(!1),y({txHash:"",userId:"",amount:"",fromAddress:"",toAddress:"",network:"mainnet"})):alert("处理失败: ".concat(e.error))}else{let e=await a.json();alert("处理失败: ".concat(e.error))}}catch(e){console.error("手动充值失败:",e),alert("处理失败，请重试")}finally{v(!1)}},_=e=>e?new Date(e).toLocaleString("zh-CN"):"-",P=e=>{if(!e)return"-";let s=new Date(e).getTime(),a=Date.now()-s,l=Math.floor(a/36e5),t=Math.floor(a%36e5/6e4);return"".concat(l,"小时").concat(t,"分钟")};return a?(0,l.jsx)("div",{className:"flex justify-center py-8",children:(0,l.jsx)(n.o,{size:"lg"})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold",children:"充值监控管理"}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsx)(c.T,{onPress:N,children:"刷新状态"}),(0,l.jsx)(c.T,{color:"primary",onPress:()=>p(!0),children:"手动处理充值"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"主网监控 (Mainnet)"}),(0,l.jsx)(b.R,{color:(null==e?void 0:e.mainnet.running)?"success":"default",variant:"flat",children:(null==e?void 0:e.mainnet.running)?"运行中":"已停止"})]})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-500",children:"启动时间:"}),(0,l.jsx)("p",{className:"font-medium",children:_((null==e?void 0:e.mainnet.startTime)||null)})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-500",children:"运行时长:"}),(0,l.jsx)("p",{className:"font-medium",children:P((null==e?void 0:e.mainnet.startTime)||null)})]})]}),(0,l.jsx)(L.y,{}),(0,l.jsx)("div",{className:"flex gap-2",children:(null==e?void 0:e.mainnet.running)?(0,l.jsx)(c.T,{color:"danger",size:"sm",onPress:()=>w("stop","mainnet"),isLoading:i,children:"停止监控"}):(0,l.jsx)(c.T,{color:"success",size:"sm",onPress:()=>w("start","mainnet"),isLoading:i,children:"启动监控"})})]})})]}),(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"测试网监控 (Nile)"}),(0,l.jsx)(b.R,{color:(null==e?void 0:e.nile.running)?"success":"default",variant:"flat",children:(null==e?void 0:e.nile.running)?"运行中":"已停止"})]})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-500",children:"启动时间:"}),(0,l.jsx)("p",{className:"font-medium",children:_((null==e?void 0:e.nile.startTime)||null)})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-gray-500",children:"运行时长:"}),(0,l.jsx)("p",{className:"font-medium",children:P((null==e?void 0:e.nile.startTime)||null)})]})]}),(0,l.jsx)(L.y,{}),(0,l.jsx)("div",{className:"flex gap-2",children:(null==e?void 0:e.nile.running)?(0,l.jsx)(c.T,{color:"danger",size:"sm",onPress:()=>w("stop","nile"),isLoading:i,children:"停止监控"}):(0,l.jsx)(c.T,{color:"success",size:"sm",onPress:()=>w("start","nile"),isLoading:i,children:"启动监控"})})]})})]})]}),(0,l.jsxs)(h.Z,{children:[(0,l.jsx)(u.d,{children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"监控说明"})}),(0,l.jsx)(j.U,{children:(0,l.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,l.jsx)("p",{children:"• 充值监控服务会实时监控TRON区块链上的USDT转账交易"}),(0,l.jsx)("p",{children:"• 当检测到转账到平台钱包地址的交易时，会自动为用户充值到内部账户"}),(0,l.jsx)("p",{children:"• 监控服务每30秒检查一次新区块，确保及时处理充值"}),(0,l.jsx)("p",{children:"• 建议在生产环境中保持主网监控运行，测试网监控可按需启动"}),(0,l.jsx)("p",{children:'• 如果自动监控出现问题，可以使用"手动处理充值"功能'})]})})]}),(0,l.jsx)(C.Y,{isOpen:o,onClose:()=>p(!1),size:"lg",children:(0,l.jsxs)(T.g,{children:[(0,l.jsx)(D.c,{children:(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"手动处理充值"})}),(0,l.jsx)(k.h,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"bg-blue-50 p-3 rounded-lg",children:(0,l.jsx)("p",{className:"text-sm text-blue-800",children:"\uD83D\uDCA1 当自动监控未能及时处理充值时，可以使用此功能手动处理用户充值。"})}),(0,l.jsx)(R.r,{label:"交易哈希",placeholder:"请输入USDT转账的交易哈希",value:f.txHash,onChange:e=>y(s=>({...s,txHash:e.target.value})),isRequired:!0}),(0,l.jsx)(R.r,{label:"用户ID",placeholder:"请输入用户ID",type:"number",value:f.userId,onChange:e=>y(s=>({...s,userId:e.target.value})),isRequired:!0}),(0,l.jsx)(R.r,{label:"充值金额",placeholder:"请输入USDT金额",type:"number",value:f.amount,onChange:e=>y(s=>({...s,amount:e.target.value})),endContent:(0,l.jsx)("span",{className:"text-gray-500",children:"USDT"}),isRequired:!0}),(0,l.jsx)(R.r,{label:"发送地址",placeholder:"请输入用户钱包地址",value:f.fromAddress,onChange:e=>y(s=>({...s,fromAddress:e.target.value})),isRequired:!0}),(0,l.jsx)(R.r,{label:"接收地址",placeholder:"请输入平台钱包地址",value:f.toAddress,onChange:e=>y(s=>({...s,toAddress:e.target.value})),isRequired:!0}),(0,l.jsxs)(x.d,{label:"网络",selectedKeys:[f.network],onSelectionChange:e=>y(s=>({...s,network:Array.from(e)[0]})),children:[(0,l.jsx)(m.y,{value:"mainnet",children:"主网 (Mainnet)"},"mainnet"),(0,l.jsx)(m.y,{value:"nile",children:"测试网 (Nile)"},"nile")]})]})}),(0,l.jsxs)(O.q,{children:[(0,l.jsx)(c.T,{color:"danger",variant:"light",onPress:()=>p(!1),children:"取消"}),(0,l.jsx)(c.T,{color:"primary",onPress:S,isLoading:g,children:"处理充值"})]})]})})]})}function q(){let{user:e,loading:s}=(0,r.h)(),a=(0,i.useRouter)(),[x,m]=(0,t.useState)(!0),[h,j]=(0,t.useState)("stats");return((0,t.useEffect)(()=>{if(!s){if(!e)return void a.push("/auth/signin");if(!e.role||!["super_admin","agent"].includes(e.role))return void a.push("/wallet");m(!1)}},[e,s,a]),s||x)?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsx)(n.o,{size:"lg"})}):e&&e.role&&["super_admin","agent"].includes(e.role)?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 px-4",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\uD83C\uDFAF 刷单系统管理"}),(0,l.jsx)("p",{className:"text-gray-600 mt-1",children:"管理刷单商品、订单、规则和统计数据"})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsx)(c.T,{color:"primary",variant:"bordered",onPress:()=>a.push("/admin"),children:"返回管理后台"}),(0,l.jsx)(c.T,{color:"secondary",variant:"bordered",onPress:()=>a.push("/brush"),children:"查看刷单页面"})]})]}),(0,l.jsxs)(d.r,{selectedKey:h,onSelectionChange:e=>j(e),className:"w-full",size:"lg",children:[(0,l.jsx)(o.i,{title:"\uD83D\uDCCA 数据统计",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(S,{})})},"stats"),(0,l.jsx)(o.i,{title:"\uD83D\uDECD️ 商品管理",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(A,{})})},"products"),(0,l.jsx)(o.i,{title:"\uD83D\uDCCB 订单管理",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(I,{})})},"orders"),(0,l.jsx)(o.i,{title:"⚙️ 规则配置",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(E,{})})},"rules"),(0,l.jsx)(o.i,{title:"\uD83D\uDCB0 平台钱包",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(F,{})})},"wallets"),(0,l.jsx)(o.i,{title:"\uD83D\uDD0D 充值监控",children:(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)(Z,{})})},"monitor")]})]})}):null}},72031:(e,s,a)=>{"use strict";a.d(s,{A:()=>i,h:()=>n});var l=a(19605),t=a(9585);let r=(0,t.createContext)(void 0);function i(e){let{children:s}=e,[a,i]=(0,t.useState)(null),[n,c]=(0,t.useState)(!0);(0,t.useEffect)(()=>{d()},[]);let d=async()=>{try{let e=await fetch("/api/auth/me");if(e.ok){let s=await e.json();i(s.user)}}catch(e){console.error("检查登录状态失败:",e)}finally{c(!1)}};return(0,l.jsx)(r.Provider,{value:{user:a,loading:n,login:e=>{i(e)},logout:()=>{i(null),document.cookie="auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;"}},children:s})}function n(){let e=(0,t.useContext)(r);if(void 0===e)throw Error("useAuth must be used within a SessionProvider");return e}},89500:(e,s,a)=>{Promise.resolve().then(a.bind(a,51910))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,7443,4273,8688,201,6029,3748,9586,4936,1524,5093,8865,5247,390,110,7358],()=>s(89500)),_N_E=e.O()}]);