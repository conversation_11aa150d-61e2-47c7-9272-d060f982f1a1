(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9080],{274:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var l=r(19605),a=r(56995),d=r(19864),i=r(21598),t=r(54110),n=r(55935);function o(){let e=(0,n.useRouter)();return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-50 p-4",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto pt-8",children:[(0,l.jsxs)(a.Z,{className:"border-2 border-red-200 shadow-lg",children:[(0,l.jsx)(d.d,{className:"bg-red-100 border-b border-red-200",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"text-3xl",children:"⚠️"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-red-800",children:"安全警告"}),(0,l.jsx)("p",{className:"text-red-600",children:"重要安全问题已被发现并修复"})]})]})}),(0,l.jsxs)(i.U,{className:"space-y-6 p-6",children:[(0,l.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"\uD83D\uDD0D 发现的问题"}),(0,l.jsx)("p",{className:"text-yellow-700",children:"在刷单充值功能中发现了一个严重的安全漏洞：系统生成假的交易哈希， 允许用户在没有真实转账的情况下获得内部余额。"})]}),(0,l.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-red-800 mb-2",children:"⚡ 风险等级"}),(0,l.jsxs)("p",{className:"text-red-700 font-medium",children:[(0,l.jsx)("span",{className:"bg-red-200 px-2 py-1 rounded",children:"高风险"})," - 可能导致资金损失和系统被滥用"]})]}),(0,l.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-blue-800 mb-2",children:"\uD83D\uDEE0️ 已实施的修复"}),(0,l.jsxs)("ul",{className:"text-blue-700 space-y-2",children:[(0,l.jsx)("li",{children:"• ✅ 移除了假交易哈希生成代码"}),(0,l.jsx)("li",{children:"• ✅ 添加了交易哈希格式验证"}),(0,l.jsx)("li",{children:"• ✅ 添加了TRON地址格式验证"}),(0,l.jsx)("li",{children:"• ✅ 集成了真实的区块链交易验证"}),(0,l.jsx)("li",{children:"• ✅ 统一了API响应格式"}),(0,l.jsx)("li",{children:"• ✅ 禁用了不安全的转账功能"})]})]}),(0,l.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-green-800 mb-2",children:"\uD83D\uDD12 安全措施"}),(0,l.jsxs)("ul",{className:"text-green-700 space-y-2",children:[(0,l.jsx)("li",{children:"• 所有充值现在需要真实的区块链交易"}),(0,l.jsx)("li",{children:"• 交易哈希必须是有效的64位十六进制字符串"}),(0,l.jsx)("li",{children:"• 系统会验证交易是否真实存在且已确认"}),(0,l.jsx)("li",{children:"• 发送地址必须是有效的TRON地址"}),(0,l.jsx)("li",{children:"• 交易哈希不能重复使用"})]})]}),(0,l.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-purple-800 mb-2",children:"\uD83D\uDCCB 建议的后续行动"}),(0,l.jsxs)("ul",{className:"text-purple-700 space-y-2",children:[(0,l.jsx)("li",{children:"• 审查所有现有的充值记录"}),(0,l.jsx)("li",{children:"• 检查是否有使用假交易哈希的记录"}),(0,l.jsx)("li",{children:"• 考虑重置所有可疑的用户余额"}),(0,l.jsx)("li",{children:"• 实施更严格的交易验证流程"}),(0,l.jsx)("li",{children:"• 定期进行安全审计"}),(0,l.jsx)("li",{children:"• 在生产环境中删除所有调试API"})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-2",children:"\uD83D\uDD27 技术细节"}),(0,l.jsxs)("div",{className:"text-gray-700 space-y-2",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"问题文件："})," ",(0,l.jsx)("code",{children:"src/lib/wallet-service.ts"})]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"问题函数："})," ",(0,l.jsx)("code",{children:"transferUSDT()"})]}),(0,l.jsx)("p",{children:(0,l.jsx)("strong",{children:"问题代码："})}),(0,l.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-sm overflow-x-auto",children:"// 危险的代码（已移除）\nconst mockTxHash = '0x' + Math.random().toString(16).substring(2, 66);"}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"修复状态："})," ✅ 已修复并添加真实交易验证"]})]})]}),(0,l.jsxs)("div",{className:"flex justify-center space-x-4 pt-4",children:[(0,l.jsx)(t.T,{color:"primary",onClick:()=>e.push("/admin"),className:"px-6",children:"返回管理面板"}),(0,l.jsx)(t.T,{color:"secondary",variant:"bordered",onClick:()=>e.push("/brush"),className:"px-6",children:"查看刷单页面"})]})]})]}),(0,l.jsxs)("div",{className:"mt-6 text-center text-gray-600",children:[(0,l.jsx)("p",{children:"如有任何疑问，请联系系统管理员"}),(0,l.jsxs)("p",{className:"text-sm mt-2",children:["安全修复时间: ",new Date().toLocaleString()]})]})]})})}},19864:(e,s,r)=>{"use strict";r.d(s,{d:()=>o});var l=r(31663),a=r(3208),d=r(23883),i=r(18884),t=r(19605),n=(0,a.Rf)((e,s)=>{var r;let{as:a,className:n,children:o,...c}=e,u=(0,d.zD)(s),{slots:b,classNames:x}=(0,l.f)(),h=(0,i.$z)(null==x?void 0:x.header,n);return(0,t.jsx)(a||"div",{ref:u,className:null==(r=b.header)?void 0:r.call(b,{class:h}),...c,children:o})});n.displayName="HeroUI.CardHeader";var o=n},21598:(e,s,r)=>{"use strict";r.d(s,{U:()=>o});var l=r(31663),a=r(3208),d=r(23883),i=r(18884),t=r(19605),n=(0,a.Rf)((e,s)=>{var r;let{as:a,className:n,children:o,...c}=e,u=(0,d.zD)(s),{slots:b,classNames:x}=(0,l.f)(),h=(0,i.$z)(null==x?void 0:x.body,n);return(0,t.jsx)(a||"div",{ref:u,className:null==(r=b.body)?void 0:r.call(b,{class:h}),...c,children:o})});n.displayName="HeroUI.CardBody";var o=n},31663:(e,s,r)=>{"use strict";r.d(s,{f:()=>a,u:()=>l});var[l,a]=(0,r(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},55935:(e,s,r)=>{"use strict";var l=r(85383);r.o(l,"useRouter")&&r.d(s,{useRouter:function(){return l.useRouter}}),r.o(l,"useSearchParams")&&r.d(s,{useSearchParams:function(){return l.useSearchParams}})},56995:(e,s,r)=>{"use strict";r.d(s,{Z:()=>y});var l=r(31663),a=r(92610),d=r(56457),i=(0,a.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...d.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),t=r(9585),n=r(26423),o=r(96539),c=r(7484),u=r(85823),b=r(90890),x=r(31081),h=r(3208),m=r(18884),p=r(9733),j=r(23883),f=r(32965),g=r(14171),v=r(19605),N=(0,h.Rf)((e,s)=>{let{children:r,context:a,Component:d,isPressable:N,disableAnimation:y,disableRipple:D,getCardProps:w,getRippleProps:k}=function(e){var s,r,l,a;let d=(0,x.o)(),[g,v]=(0,h.rE)(e,i.variantKeys),{ref:N,as:y,children:D,onClick:w,onPress:k,autoFocus:C,className:P,classNames:H,allowTextSelectionOnPress:E=!0,...R}=g,z=(0,j.zD)(N),B=y||(e.isPressable?"button":"div"),S="string"==typeof B,T=null!=(r=null!=(s=e.disableAnimation)?s:null==d?void 0:d.disableAnimation)&&r,I=null!=(a=null!=(l=e.disableRipple)?l:null==d?void 0:d.disableRipple)&&a,U=(0,m.$z)(null==H?void 0:H.base,P),{onClear:_,onPress:A,ripples:F}=(0,f.k)(),M=(0,t.useCallback)(e=>{I||T||z.current&&A(e)},[I,T,z,A]),{buttonProps:W,isPressed:$}=(0,b.l)({onPress:(0,n.c)(k,M),elementType:y,isDisabled:!e.isPressable,onClick:w,allowTextSelectionOnPress:E,...R},z),{hoverProps:O,isHovered:V}=(0,u.M)({isDisabled:!e.isHoverable,...R}),{isFocusVisible:Z,isFocused:q,focusProps:K}=(0,c.o)({autoFocus:C}),L=(0,t.useMemo)(()=>i({...v,disableAnimation:T}),[(0,m.t6)(v),T]),G=(0,t.useMemo)(()=>({slots:L,classNames:H,disableAnimation:T,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[L,H,e.isDisabled,e.isFooterBlurred,T,e.fullWidth]),J=(0,t.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:z,className:L.base({class:U}),tabIndex:e.isPressable?0:-1,"data-hover":(0,m.sE)(V),"data-pressed":(0,m.sE)($),"data-focus":(0,m.sE)(q),"data-focus-visible":(0,m.sE)(Z),"data-disabled":(0,m.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...W,...K,role:"button"}:{},e.isHoverable?O:{},(0,p.$)(R,{enabled:S}),(0,p.$)(s))}},[z,L,U,S,e.isPressable,e.isHoverable,e.isDisabled,V,$,Z,W,K,O,R]),Q=(0,t.useCallback)(()=>({ripples:F,onClear:_}),[F,_]);return{context:G,domRef:z,Component:B,classNames:H,children:D,isHovered:V,isPressed:$,disableAnimation:T,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:I,handlePress:M,isFocusVisible:Z,getCardProps:J,getRippleProps:Q}}({...e,ref:s});return(0,v.jsxs)(d,{...w(),children:[(0,v.jsx)(l.u,{value:a,children:r}),N&&!y&&!D&&(0,v.jsx)(g.j,{...k()})]})});N.displayName="HeroUI.Card";var y=N},93557:(e,s,r)=>{Promise.resolve().then(r.bind(r,274))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,390,110,7358],()=>s(93557)),_N_E=e.O()}]);