(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4548],{55955:(e,l,s)=>{Promise.resolve().then(s.bind(s,92502))},92502:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>n});var t=s(19605),i=s(9585),a=s(54110);function n(){let e=(0,i.useRef)(null),l=(l,s,t,i)=>{let a=e.current;if(!a)return;a.width=l,a.height=s;let n=a.getContext("2d");if(!n)return;let d=n.createLinearGradient(0,0,l,s);d.addColorStop(0,"#667eea"),d.addColorStop(1,"#764ba2"),n.fillStyle=d,n.fillRect(0,0,l,s),n.fillStyle="white",n.textAlign="center",n.font="bold 32px Arial, sans-serif",n.fillText("TRON钱包",l/2,.4*s),n.font="20px Arial, sans-serif",n.fillText(t,l/2,.5*s),n.font="16px Arial, sans-serif",n.fillStyle="rgba(255, 255, 255, 0.8)",n.fillText("".concat(l," \xd7 ").concat(s),l/2,.6*s),a.toBlob(e=>{if(e){let l=URL.createObjectURL(e),s=document.createElement("a");s.href=l,s.download=i,s.click(),URL.revokeObjectURL(l)}},"image/png")};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 px-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-center mb-8",children:"生成应用截图"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"截图生成器"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"点击下面的按钮生成应用截图。生成的图片将自动下载到您的设备。"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[{width:390,height:844,title:"移动端界面",filename:"mobile-1.png"},{width:390,height:844,title:"刷单功能",filename:"mobile-2.png"},{width:1280,height:800,title:"桌面端界面",filename:"desktop-1.png"},{width:1280,height:800,title:"管理后台",filename:"desktop-2.png"}].map((e,s)=>(0,t.jsxs)(a.T,{color:"primary",variant:"bordered",onPress:()=>l(e.width,e.height,e.title,e.filename),className:"h-20 flex flex-col",children:[(0,t.jsx)("div",{className:"font-semibold",children:e.title}),(0,t.jsxs)("div",{className:"text-sm opacity-70",children:[e.width," \xd7 ",e.height]}),(0,t.jsx)("div",{className:"text-xs opacity-50",children:e.filename})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"使用说明"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-gray-600",children:[(0,t.jsx)("li",{children:"点击上方按钮生成对应的截图文件"}),(0,t.jsxs)("li",{children:["将下载的PNG文件放入 ",(0,t.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded",children:"public/screenshots/"})," 目录"]}),(0,t.jsx)("li",{children:"确保文件名与按钮上显示的文件名一致"}),(0,t.jsx)("li",{children:"重启开发服务器以应用更改"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"提示"}),(0,t.jsx)("p",{className:"text-blue-700 text-sm",children:"这些是占位截图。在生产环境中，建议使用实际的应用界面截图来替换这些生成的图片。"})]})]}),(0,t.jsx)("canvas",{ref:e,style:{display:"none"}})]})})}}},e=>{var l=l=>e(e.s=l);e.O(0,[8031,4110,390,110,7358],()=>l(55955)),_N_E=e.O()}]);