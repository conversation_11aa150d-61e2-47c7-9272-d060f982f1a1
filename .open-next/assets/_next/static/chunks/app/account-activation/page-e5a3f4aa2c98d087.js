(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8369],{19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>n});var r=l(31663),d=l(3208),a=l(23883),i=l(18884),c=l(19605),t=(0,d.Rf)((e,s)=>{var l;let{as:d,className:t,children:n,...x}=e,o=(0,a.zD)(s),{slots:m,classNames:h}=(0,r.f)(),u=(0,i.$z)(null==h?void 0:h.header,t);return(0,c.jsx)(d||"div",{ref:o,className:null==(l=m.header)?void 0:l.call(m,{class:u}),...x,children:n})});t.displayName="HeroUI.CardHeader";var n=t},55935:(e,s,l)=>{"use strict";var r=l(85383);l.o(r,"useRouter")&&l.d(s,{useRouter:function(){return r.useRouter}}),l.o(r,"useSearchParams")&&l.d(s,{useSearchParams:function(){return r.useSearchParams}})},80376:(e,s,l)=>{Promise.resolve().then(l.bind(l,84487))},84487:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var r=l(19605),d=l(56995),a=l(19864),i=l(21598),c=l(63707),t=l(54110),n=l(55935),x=l(9585);function o(){let e=(0,n.useRouter)(),[s,l]=(0,x.useState)(null),o="TAjz662ivGjK792yaiTaeJcthDD4wTtX4m",m=e=>{navigator.clipboard.writeText(e),l(e),setTimeout(()=>l(null),2e3)};return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto pt-8",children:[(0,r.jsxs)(d.Z,{className:"border-2 border-blue-200 shadow-lg",children:[(0,r.jsx)(a.d,{className:"bg-blue-100 border-b border-blue-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"text-3xl",children:"\uD83D\uDD10"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-blue-800",children:"TRON账户激活指南"}),(0,r.jsx)("p",{className:"text-blue-600",children:"您的钱包需要先激活才能进行转账"})]})]})}),(0,r.jsxs)(i.U,{className:"space-y-6 p-6",children:[(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"⚠️ 账户未激活"}),(0,r.jsxs)("p",{className:"text-yellow-700",children:["您的钱包地址 ",(0,r.jsx)("code",{className:"bg-yellow-100 px-2 py-1 rounded",children:o}),"尚未在TRON网络上激活。在TRON网络中，新创建的账户需要先接收一笔转账才能被激活。"]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-blue-800 mb-3",children:"\uD83D\uDE80 如何激活账户"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(c.R,{color:"primary",variant:"flat",size:"sm",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-blue-700",children:"获取一些TRX"}),(0,r.jsx)("p",{className:"text-blue-600 text-sm",children:"从交易所、朋友或其他已激活的钱包获取至少1 TRX"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(c.R,{color:"primary",variant:"flat",size:"sm",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-blue-700",children:"转账到您的钱包"}),(0,r.jsx)("p",{className:"text-blue-600 text-sm",children:"将TRX转账到下面的地址来激活账户"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(c.R,{color:"primary",variant:"flat",size:"sm",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-blue-700",children:"等待确认"}),(0,r.jsx)("p",{className:"text-blue-600 text-sm",children:"转账确认后，您的账户就会被激活"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 mb-3",children:"\uD83D\uDCCB 您的钱包地址"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-white p-3 rounded border",children:[(0,r.jsx)("code",{className:"flex-1 text-sm font-mono break-all",children:o}),(0,r.jsx)(t.T,{size:"sm",color:s===o?"success":"primary",variant:"flat",onClick:()=>m(o),children:s===o?"已复制":"复制"})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-2",children:"请将此地址发送给朋友，或在交易所提现时使用此地址"})]}),(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-green-800 mb-3",children:"\uD83D\uDCA1 激活建议"}),(0,r.jsxs)("ul",{className:"text-green-700 space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"最少金额"}),": 建议转入至少 1 TRX 来激活账户"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"推荐金额"}),": 转入 10-20 TRX 以确保有足够的手续费"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"网络选择"}),": 确保使用正确的网络（主网/测试网）"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"确认时间"}),": 通常需要1-3分钟确认"]})]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-purple-800 mb-3",children:"\uD83D\uDD17 获取TRX的方式"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-white p-3 rounded border",children:[(0,r.jsx)("h3",{className:"font-medium text-purple-700 mb-2",children:"交易所购买"}),(0,r.jsxs)("ul",{className:"text-purple-600 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Binance"}),(0,r.jsx)("li",{children:"• Huobi"}),(0,r.jsx)("li",{children:"• OKX"}),(0,r.jsx)("li",{children:"• Gate.io"})]})]}),(0,r.jsxs)("div",{className:"bg-white p-3 rounded border",children:[(0,r.jsx)("h3",{className:"font-medium text-purple-700 mb-2",children:"其他方式"}),(0,r.jsxs)("ul",{className:"text-purple-600 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• 朋友转账"}),(0,r.jsx)("li",{children:"• TRON水龙头（测试网）"}),(0,r.jsx)("li",{children:"• 去中心化交易所"}),(0,r.jsx)("li",{children:"• P2P交易"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-red-800 mb-2",children:"⚠️ 注意事项"}),(0,r.jsxs)("ul",{className:"text-red-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 确保使用正确的网络（主网地址不能在测试网使用）"}),(0,r.jsx)("li",{children:"• 保存好您的私钥和助记词"}),(0,r.jsx)("li",{children:"• 小额测试后再进行大额转账"}),(0,r.jsx)("li",{children:"• 激活后才能进行转出操作"})]})]}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4 pt-4",children:[(0,r.jsx)(t.T,{color:"primary",onClick:()=>e.push("/wallet"),className:"px-6",children:"返回钱包"}),(0,r.jsx)(t.T,{color:"secondary",variant:"bordered",onClick:()=>window.location.reload(),className:"px-6",children:"刷新检查"})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 text-center text-gray-600",children:[(0,r.jsx)("p",{children:"如有任何疑问，请联系客服或查看TRON官方文档"}),(0,r.jsx)("p",{className:"text-sm mt-2",children:(0,r.jsx)("a",{href:"https://developers.tron.network/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"TRON开发者文档"})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,390,110,7358],()=>s(80376)),_N_E=e.O()}]);