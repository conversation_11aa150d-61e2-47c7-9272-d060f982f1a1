(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9865],{19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>o});var a=l(31663),r=l(3208),i=l(23883),d=l(18884),t=l(19605),n=(0,r.Rf)((e,s)=>{var l;let{as:r,className:n,children:o,...c}=e,u=(0,i.zD)(s),{slots:b,classNames:h}=(0,a.f)(),x=(0,d.$z)(null==h?void 0:h.header,n);return(0,t.jsx)(r||"div",{ref:u,className:null==(l=b.header)?void 0:l.call(b,{class:x}),...c,children:o})});n.displayName="HeroUI.CardHeader";var o=n},21598:(e,s,l)=>{"use strict";l.d(s,{U:()=>o});var a=l(31663),r=l(3208),i=l(23883),d=l(18884),t=l(19605),n=(0,r.Rf)((e,s)=>{var l;let{as:r,className:n,children:o,...c}=e,u=(0,i.zD)(s),{slots:b,classNames:h}=(0,a.f)(),x=(0,d.$z)(null==h?void 0:h.body,n);return(0,t.jsx)(r||"div",{ref:u,className:null==(l=b.body)?void 0:l.call(b,{class:x}),...c,children:o})});n.displayName="HeroUI.CardBody";var o=n},31663:(e,s,l)=>{"use strict";l.d(s,{f:()=>r,u:()=>a});var[a,r]=(0,l(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},56995:(e,s,l)=>{"use strict";l.d(s,{Z:()=>y});var a=l(31663),r=l(92610),i=l(56457),d=(0,r.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...i.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),t=l(9585),n=l(26423),o=l(96539),c=l(7484),u=l(85823),b=l(90890),h=l(31081),x=l(3208),m=l(18884),f=l(9733),v=l(23883),p=l(32965),j=l(14171),g=l(19605),N=(0,x.Rf)((e,s)=>{let{children:l,context:r,Component:i,isPressable:N,disableAnimation:y,disableRipple:w,getCardProps:k,getRippleProps:C}=function(e){var s,l,a,r;let i=(0,h.o)(),[j,g]=(0,x.rE)(e,d.variantKeys),{ref:N,as:y,children:w,onClick:k,onPress:C,autoFocus:P,className:D,classNames:H,allowTextSelectionOnPress:z=!0,...E}=j,U=(0,v.zD)(N),B=y||(e.isPressable?"button":"div"),_="string"==typeof B,F=null!=(l=null!=(s=e.disableAnimation)?s:null==i?void 0:i.disableAnimation)&&l,M=null!=(r=null!=(a=e.disableRipple)?a:null==i?void 0:i.disableRipple)&&r,R=(0,m.$z)(null==H?void 0:H.base,D),{onClear:W,onPress:Z,ripples:$}=(0,p.k)(),I=(0,t.useCallback)(e=>{M||F||U.current&&Z(e)},[M,F,U,Z]),{buttonProps:S,isPressed:V}=(0,b.l)({onPress:(0,n.c)(C,I),elementType:y,isDisabled:!e.isPressable,onClick:k,allowTextSelectionOnPress:z,...E},U),{hoverProps:A,isHovered:T}=(0,u.M)({isDisabled:!e.isHoverable,...E}),{isFocusVisible:O,isFocused:q,focusProps:J}=(0,c.o)({autoFocus:P}),K=(0,t.useMemo)(()=>d({...g,disableAnimation:F}),[(0,m.t6)(g),F]),G=(0,t.useMemo)(()=>({slots:K,classNames:H,disableAnimation:F,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[K,H,e.isDisabled,e.isFooterBlurred,F,e.fullWidth]),L=(0,t.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:U,className:K.base({class:R}),tabIndex:e.isPressable?0:-1,"data-hover":(0,m.sE)(T),"data-pressed":(0,m.sE)(V),"data-focus":(0,m.sE)(q),"data-focus-visible":(0,m.sE)(O),"data-disabled":(0,m.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...S,...J,role:"button"}:{},e.isHoverable?A:{},(0,f.$)(E,{enabled:_}),(0,f.$)(s))}},[U,K,R,_,e.isPressable,e.isHoverable,e.isDisabled,T,V,O,S,J,A,E]),Q=(0,t.useCallback)(()=>({ripples:$,onClear:W}),[$,W]);return{context:G,domRef:U,Component:B,classNames:H,children:w,isHovered:T,isPressed:V,disableAnimation:F,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:M,handlePress:I,isFocusVisible:O,getCardProps:L,getRippleProps:Q}}({...e,ref:s});return(0,g.jsxs)(i,{...k(),children:[(0,g.jsx)(a.u,{value:r,children:l}),N&&!y&&!w&&(0,g.jsx)(j.j,{...C()})]})});N.displayName="HeroUI.Card";var y=N},62590:(e,s,l)=>{Promise.resolve().then(l.bind(l,74943))},74943:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>n});var a=l(19605),r=l(56995),i=l(19864),d=l(21598),t=l(54110);function n(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)(r.Z,{children:[(0,a.jsx)(i.d,{children:(0,a.jsx)("h1",{className:"text-2xl font-bold text-center w-full",children:"防缩放测试页面"})}),(0,a.jsxs)(d.U,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"测试说明"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"此页面已启用防缩放功能，请尝试以下操作来验证："})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(r.Z,{className:"bg-blue-50",children:(0,a.jsxs)(d.U,{children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"移动端测试"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• 双指捏合缩放 - 应该被阻止"}),(0,a.jsx)("li",{children:"• 双击缩放 - 应该被阻止"}),(0,a.jsx)("li",{children:"• 长按选择文本 - 应该被阻止"})]})]})}),(0,a.jsx)(r.Z,{className:"bg-green-50",children:(0,a.jsxs)(d.U,{children:[(0,a.jsx)("h3",{className:"font-semibold text-green-800 mb-2",children:"桌面端测试"}),(0,a.jsxs)("ul",{className:"text-sm text-green-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Ctrl + 滚轮缩放 - 应该被阻止"}),(0,a.jsx)("li",{children:"• Ctrl + Plus/Minus 键缩放 - 应该被阻止"}),(0,a.jsx)("li",{children:"• Ctrl + 0 重置缩放 - 应该被阻止"})]})]})}),(0,a.jsx)(r.Z,{className:"bg-yellow-50",children:(0,a.jsxs)(d.U,{children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"正常功能"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"• 输入框文本选择 - 应该正常工作"}),(0,a.jsx)("li",{children:"• 按钮点击 - 应该正常工作"}),(0,a.jsx)("li",{children:"• 页面滚动 - 应该正常工作"})]})]})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold",children:"交互测试"}),(0,a.jsx)("input",{type:"text",placeholder:"测试输入框 - 文本应该可以选择",className:"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",defaultValue:"这是一段可以选择的文本"}),(0,a.jsx)("textarea",{placeholder:"测试文本域 - 文本应该可以选择和编辑",className:"w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none",defaultValue:"这是一段可以选择和编辑的文本内容。"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(t.T,{color:"primary",onPress:()=>alert("按钮点击正常工作！"),children:"测试按钮"}),(0,a.jsx)(t.T,{color:"secondary",onPress:()=>window.location.reload(),children:"刷新页面"})]})]}),(0,a.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"技术实现"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• viewport meta标签设置 user-scalable=no"}),(0,a.jsx)("li",{children:"• CSS touch-action: manipulation"}),(0,a.jsx)("li",{children:"• JavaScript事件监听器阻止缩放手势"}),(0,a.jsx)("li",{children:"• 键盘快捷键拦截"})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"如果以上缩放操作都被成功阻止，说明防缩放功能正常工作。"})})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,390,110,7358],()=>s(62590)),_N_E=e.O()}]);