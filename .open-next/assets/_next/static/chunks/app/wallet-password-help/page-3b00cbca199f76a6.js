(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[256],{9287:(e,s,l)=>{Promise.resolve().then(l.bind(l,52642))},19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>o});var r=l(31663),i=l(3208),a=l(23883),d=l(18884),n=l(19605),t=(0,i.Rf)((e,s)=>{var l;let{as:i,className:t,children:o,...c}=e,h=(0,a.zD)(s),{slots:x,classNames:u}=(0,r.f)(),j=(0,d.$z)(null==u?void 0:u.header,t);return(0,n.jsx)(i||"div",{ref:h,className:null==(l=x.header)?void 0:l.call(x,{class:j}),...c,children:o})});t.displayName="HeroUI.CardHeader";var o=t},21598:(e,s,l)=>{"use strict";l.d(s,{U:()=>o});var r=l(31663),i=l(3208),a=l(23883),d=l(18884),n=l(19605),t=(0,i.Rf)((e,s)=>{var l;let{as:i,className:t,children:o,...c}=e,h=(0,a.zD)(s),{slots:x,classNames:u}=(0,r.f)(),j=(0,d.$z)(null==u?void 0:u.body,t);return(0,n.jsx)(i||"div",{ref:h,className:null==(l=x.body)?void 0:l.call(x,{class:j}),...c,children:o})});t.displayName="HeroUI.CardBody";var o=t},31663:(e,s,l)=>{"use strict";l.d(s,{f:()=>i,u:()=>r});var[r,i]=(0,l(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},52642:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>t});var r=l(19605),i=l(56995),a=l(19864),d=l(21598),n=l(89213);function t(){return(0,r.jsx)("div",{className:"container mx-auto p-4 max-w-4xl",children:(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"钱包密码说明"})}),(0,r.jsxs)(d.U,{className:"space-y-6",children:[(0,r.jsxs)(i.Z,{className:"bg-blue-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-blue-800",children:"\uD83D\uDD10 什么是钱包密码？"})}),(0,r.jsx)(d.U,{children:(0,r.jsx)("p",{className:"text-blue-700",children:"钱包密码是您在创建或导入钱包时设置的密码，用于保护您的私钥安全。 私钥会使用此密码进行加密存储，确保即使数据库被访问，您的私钥也是安全的。"})})]}),(0,r.jsxs)(i.Z,{className:"bg-green-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-green-800",children:"\uD83D\uDEE1️ 密码的作用"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("ul",{className:"text-green-700 space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"保护私钥"}),"：私钥使用密码加密存储，防止泄露"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"转账验证"}),"：每次转账时需要输入密码来解密私钥"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"安全保障"}),"：即使他人获得数据库访问权，也无法使用您的钱包"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"本地验证"}),"：密码验证在本地进行，不会传输到服务器"]})]})})]}),(0,r.jsxs)(i.Z,{className:"bg-yellow-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-yellow-800",children:"⏰ 何时需要输入密码？"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("ul",{className:"text-yellow-700 space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"创建钱包"}),"：设置钱包密码（必需）"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"导入钱包"}),"：设置钱包密码（必需）"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"发送转账"}),"：输入密码验证身份（必需）"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"查看余额"}),"：无需密码"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"接收转账"}),"：无需密码"]})]})})]}),(0,r.jsxs)(i.Z,{className:"bg-purple-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-purple-800",children:"\uD83D\uDCCB 密码要求"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("ul",{className:"text-purple-700 space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"最小长度"}),"：至少6位字符"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"建议长度"}),"：8位或以上"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"字符类型"}),"：支持字母、数字、特殊字符"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"安全建议"}),"：使用大小写字母、数字和符号的组合"]})]})})]}),(0,r.jsx)(n.y,{}),(0,r.jsxs)(i.Z,{className:"bg-red-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-red-800",children:"⚠️ 重要安全提示"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("div",{className:"text-red-700 space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"密码丢失风险"}),(0,r.jsx)("p",{children:"如果忘记钱包密码，将无法进行转账操作。请务必记住您的密码！"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"密码保管"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,r.jsx)("li",{children:"不要与他人分享您的钱包密码"}),(0,r.jsx)("li",{children:"不要在不安全的地方记录密码"}),(0,r.jsx)("li",{children:"建议使用密码管理器安全存储"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold",children:"密码强度"}),(0,r.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,r.jsx)("li",{children:"使用复杂密码，避免简单的数字组合"}),(0,r.jsx)("li",{children:"不要使用生日、姓名等容易猜测的信息"}),(0,r.jsx)("li",{children:"定期更换密码（暂不支持，请重新创建钱包）"})]})]})]})})]}),(0,r.jsxs)(i.Z,{children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"❓ 常见问题"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"Q: 忘记密码怎么办？"}),(0,r.jsx)("p",{className:"text-gray-600",children:"A: 目前无法重置密码。如果忘记密码，您需要使用私钥重新导入钱包并设置新密码。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"Q: 可以修改密码吗？"}),(0,r.jsx)("p",{className:"text-gray-600",children:"A: 目前不支持直接修改密码。如需更换密码，请导出私钥后重新导入钱包。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"Q: 密码是否会被传输到服务器？"}),(0,r.jsx)("p",{className:"text-gray-600",children:"A: 密码仅在本地使用，用于加密/解密私钥，不会传输到服务器。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"Q: 为什么需要设置密码？"}),(0,r.jsx)("p",{className:"text-gray-600",children:"A: 密码是保护您数字资产的重要安全措施，确保只有您本人可以使用钱包进行转账。"})]})]})})]}),(0,r.jsxs)(i.Z,{className:"bg-gray-50",children:[(0,r.jsx)(a.d,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"\uD83D\uDCDD 操作流程"})}),(0,r.jsx)(d.U,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-600",children:"创建新钱包"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:'点击"创建钱包"按钮'}),(0,r.jsx)("li",{children:"输入钱包名称"}),(0,r.jsx)("li",{children:"设置钱包密码（至少6位）"}),(0,r.jsx)("li",{children:"选择网络（建议选择Nile测试网）"}),(0,r.jsx)("li",{children:'点击"创建"完成'})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-600",children:"导入现有钱包"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:'点击"导入钱包"按钮'}),(0,r.jsx)("li",{children:"输入钱包名称"}),(0,r.jsx)("li",{children:"输入64位私钥"}),(0,r.jsx)("li",{children:"设置钱包密码（至少6位）"}),(0,r.jsx)("li",{children:"选择网络"}),(0,r.jsx)("li",{children:'点击"导入"完成'})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-orange-600",children:"发送转账"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside text-gray-600 space-y-1",children:[(0,r.jsx)("li",{children:"选择要转账的钱包"}),(0,r.jsx)("li",{children:'点击"转账"按钮'}),(0,r.jsx)("li",{children:"输入接收地址和金额"}),(0,r.jsx)("li",{children:"输入钱包密码进行验证"}),(0,r.jsx)("li",{children:"确认转账信息"}),(0,r.jsx)("li",{children:"等待交易确认"})]})]})]})})]})]})]})})}},56995:(e,s,l)=>{"use strict";l.d(s,{Z:()=>y});var r=l(31663),i=l(92610),a=l(56457),d=(0,i.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...a.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),n=l(9585),t=l(26423),o=l(96539),c=l(7484),h=l(85823),x=l(90890),u=l(31081),j=l(3208),b=l(18884),m=l(9733),f=l(23883),v=l(32965),g=l(14171),p=l(19605),N=(0,j.Rf)((e,s)=>{let{children:l,context:i,Component:a,isPressable:N,disableAnimation:y,disableRipple:D,getCardProps:w,getRippleProps:k}=function(e){var s,l,r,i;let a=(0,u.o)(),[g,p]=(0,j.rE)(e,d.variantKeys),{ref:N,as:y,children:D,onClick:w,onPress:k,autoFocus:C,className:P,classNames:U,allowTextSelectionOnPress:H=!0,...E}=g,z=(0,f.zD)(N),Z=y||(e.isPressable?"button":"div"),B="string"==typeof Z,A=null!=(l=null!=(s=e.disableAnimation)?s:null==a?void 0:a.disableAnimation)&&l,R=null!=(i=null!=(r=e.disableRipple)?r:null==a?void 0:a.disableRipple)&&i,$=(0,b.$z)(null==U?void 0:U.base,P),{onClear:_,onPress:F,ripples:I}=(0,v.k)(),M=(0,n.useCallback)(e=>{R||A||z.current&&F(e)},[R,A,z,F]),{buttonProps:W,isPressed:Q}=(0,x.l)({onPress:(0,t.c)(k,M),elementType:y,isDisabled:!e.isPressable,onClick:w,allowTextSelectionOnPress:H,...E},z),{hoverProps:T,isHovered:V}=(0,h.M)({isDisabled:!e.isHoverable,...E}),{isFocusVisible:O,isFocused:q,focusProps:K}=(0,c.o)({autoFocus:C}),S=(0,n.useMemo)(()=>d({...p,disableAnimation:A}),[(0,b.t6)(p),A]),G=(0,n.useMemo)(()=>({slots:S,classNames:U,disableAnimation:A,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[S,U,e.isDisabled,e.isFooterBlurred,A,e.fullWidth]),J=(0,n.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:z,className:S.base({class:$}),tabIndex:e.isPressable?0:-1,"data-hover":(0,b.sE)(V),"data-pressed":(0,b.sE)(Q),"data-focus":(0,b.sE)(q),"data-focus-visible":(0,b.sE)(O),"data-disabled":(0,b.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...W,...K,role:"button"}:{},e.isHoverable?T:{},(0,m.$)(E,{enabled:B}),(0,m.$)(s))}},[z,S,$,B,e.isPressable,e.isHoverable,e.isDisabled,V,Q,O,W,K,T,E]),L=(0,n.useCallback)(()=>({ripples:I,onClear:_}),[I,_]);return{context:G,domRef:z,Component:Z,classNames:U,children:D,isHovered:V,isPressed:Q,disableAnimation:A,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:R,handlePress:M,isFocusVisible:O,getCardProps:J,getRippleProps:L}}({...e,ref:s});return(0,p.jsxs)(a,{...w(),children:[(0,p.jsx)(r.u,{value:i,children:l}),N&&!y&&!D&&(0,p.jsx)(g.j,{...k()})]})});N.displayName="HeroUI.Card";var y=N},89213:(e,s,l)=>{"use strict";l.d(s,{y:()=>o});var r=l(9733),i=(0,l(92610).tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),a=l(9585),d=l(3208),n=l(19605),t=(0,d.Rf)((e,s)=>{let{Component:l,getDividerProps:d}=function(e){var s;let l,d,{as:n,className:t,orientation:o,...c}=e,h=n||"hr";"hr"===h&&"vertical"===o&&(h="div");let{separatorProps:x}=(s={elementType:"string"==typeof h?h:"hr",orientation:o},d=(0,r.$)(s,{enabled:"string"==typeof s.elementType}),("vertical"===s.orientation&&(l="vertical"),"hr"!==s.elementType)?{separatorProps:{...d,role:"separator","aria-orientation":l}}:{separatorProps:d}),u=(0,a.useMemo)(()=>i({orientation:o,className:t}),[o,t]);return{Component:h,getDividerProps:(0,a.useCallback)((e={})=>({className:u,role:"separator","data-orientation":o,...x,...c,...e}),[u,o,x,c])}}({...e});return(0,n.jsx)(l,{ref:s,...d()})});t.displayName="HeroUI.Divider";var o=t}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,390,110,7358],()=>s(9287)),_N_E=e.O()}]);