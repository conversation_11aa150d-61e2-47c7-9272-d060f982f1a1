(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5831],{21598:(e,s,a)=>{"use strict";a.d(s,{U:()=>o});var r=a(31663),l=a(3208),t=a(23883),i=a(18884),d=a(19605),n=(0,l.Rf)((e,s)=>{var a;let{as:l,className:n,children:o,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:h}=(0,r.f)(),x=(0,i.$z)(null==h?void 0:h.body,n);return(0,d.jsx)(l||"div",{ref:u,className:null==(a=b.body)?void 0:a.call(b,{class:x}),...c,children:o})});n.displayName="HeroUI.CardBody";var o=n},28817:(e,s,a)=>{"use strict";a.d(s,{o:()=>l});var r=a(19605),l=e=>(0,r.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,r.jsx)("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})})},31663:(e,s,a)=>{"use strict";a.d(s,{f:()=>l,u:()=>r});var[r,l]=(0,a(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},56995:(e,s,a)=>{"use strict";a.d(s,{Z:()=>y});var r=a(31663),l=a(92610),t=a(56457),i=(0,l.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),d=a(9585),n=a(26423),o=a(96539),c=a(7484),u=a(85823),b=a(90890),h=a(31081),x=a(3208),m=a(18884),f=a(9733),j=a(23883),p=a(32965),v=a(14171),g=a(19605),N=(0,x.Rf)((e,s)=>{let{children:a,context:l,Component:t,isPressable:N,disableAnimation:y,disableRipple:w,getCardProps:D,getRippleProps:k}=function(e){var s,a,r,l;let t=(0,h.o)(),[v,g]=(0,x.rE)(e,i.variantKeys),{ref:N,as:y,children:w,onClick:D,onPress:k,autoFocus:T,className:S,classNames:C,allowTextSelectionOnPress:P=!0,...U}=v,z=(0,j.zD)(N),_=y||(e.isPressable?"button":"div"),A="string"==typeof _,E=null!=(a=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&a,H=null!=(l=null!=(r=e.disableRipple)?r:null==t?void 0:t.disableRipple)&&l,I=(0,m.$z)(null==C?void 0:C.base,S),{onClear:O,onPress:B,ripples:R}=(0,p.k)(),M=(0,d.useCallback)(e=>{H||E||z.current&&B(e)},[H,E,z,B]),{buttonProps:W,isPressed:Z}=(0,b.l)({onPress:(0,n.c)(k,M),elementType:y,isDisabled:!e.isPressable,onClick:D,allowTextSelectionOnPress:P,...U},z),{hoverProps:F,isHovered:J}=(0,u.M)({isDisabled:!e.isHoverable,...U}),{isFocusVisible:K,isFocused:X,focusProps:$}=(0,c.o)({autoFocus:T}),L=(0,d.useMemo)(()=>i({...g,disableAnimation:E}),[(0,m.t6)(g),E]),G=(0,d.useMemo)(()=>({slots:L,classNames:C,disableAnimation:E,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[L,C,e.isDisabled,e.isFooterBlurred,E,e.fullWidth]),V=(0,d.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:z,className:L.base({class:I}),tabIndex:e.isPressable?0:-1,"data-hover":(0,m.sE)(J),"data-pressed":(0,m.sE)(Z),"data-focus":(0,m.sE)(X),"data-focus-visible":(0,m.sE)(K),"data-disabled":(0,m.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...W,...$,role:"button"}:{},e.isHoverable?F:{},(0,f.$)(U,{enabled:A}),(0,f.$)(s))}},[z,L,I,A,e.isPressable,e.isHoverable,e.isDisabled,J,Z,K,W,$,F,U]),q=(0,d.useCallback)(()=>({ripples:R,onClear:O}),[R,O]);return{context:G,domRef:z,Component:_,classNames:C,children:w,isHovered:J,isPressed:Z,disableAnimation:E,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:H,handlePress:M,isFocusVisible:K,getCardProps:V,getRippleProps:q}}({...e,ref:s});return(0,g.jsxs)(t,{...D(),children:[(0,g.jsx)(r.u,{value:l,children:a}),N&&!y&&!w&&(0,g.jsx)(v.j,{...k()})]})});N.displayName="HeroUI.Card";var y=N},67773:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var r=a(19605),l=a(9585),t=a(56995),i=a(19864),d=a(21598),n=a(66700),o=a(57059),c=a(5230),u=a(54110);function b(){let[e,s]=(0,l.useState)("TAjz662ivGjK792yaiTaeJcthDD4wTtX4m"),[a,b]=(0,l.useState)("nile"),[h,x]=(0,l.useState)(!1),[m,f]=(0,l.useState)(null),j=async()=>{x(!0);try{let s=await fetch("/api/debug-usdt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({address:e,network:a})}),r=await s.json();f({success:s.ok,data:r,timestamp:new Date().toLocaleString()})}catch(e){f({success:!1,error:e,timestamp:new Date().toLocaleString()})}finally{x(!1)}};return(0,r.jsx)("div",{className:"container mx-auto p-4 max-w-4xl",children:(0,r.jsxs)(t.Z,{children:[(0,r.jsx)(i.d,{children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"USDT余额调试工具"})}),(0,r.jsxs)(d.U,{className:"space-y-4",children:[(0,r.jsx)(n.r,{label:"TRON地址",value:e,onChange:e=>s(e.target.value),placeholder:"输入TRON地址",description:"默认为您提供的Nile测试网地址"}),(0,r.jsxs)(o.d,{label:"网络",selectedKeys:[a],onSelectionChange:e=>{b(Array.from(e)[0])},children:[(0,r.jsx)(c.y,{children:"主网 (Mainnet)"},"mainnet"),(0,r.jsx)(c.y,{children:"测试网 (Nile)"},"nile"),(0,r.jsx)(c.y,{children:"测试网 (Shasta)"},"shasta")]}),(0,r.jsx)(u.T,{color:"primary",onPress:j,isLoading:h,className:"w-full",children:"调试USDT余额"}),m&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"调试结果"}),m.success?(0,r.jsxs)("div",{className:"space-y-4",children:[m.data.results.trx&&(0,r.jsxs)(t.Z,{className:"bg-blue-50",children:[(0,r.jsx)(i.d,{children:(0,r.jsx)("h3",{className:"font-semibold",children:"TRX余额"})}),(0,r.jsx)(d.U,{children:m.data.results.trx.success?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["余额: ",m.data.results.trx.balance," TRX"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["原始值: ",m.data.results.trx.raw]})]}):(0,r.jsxs)("p",{className:"text-red-600",children:["错误: ",m.data.results.trx.error]})})]}),Object.entries(m.data.results).map(e=>{let[s,a]=e;return s.startsWith("usdt_contract_")?(0,r.jsxs)(t.Z,{className:a.success?"bg-green-50":"bg-red-50",children:[(0,r.jsx)(i.d,{children:(0,r.jsxs)("h3",{className:"font-semibold",children:["USDT合约 ",s.replace("usdt_contract_","")]})}),(0,r.jsxs)(d.U,{children:[(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["合约地址: ",a.contractAddress]}),a.success?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-lg font-bold text-green-600",children:["余额: ",a.balance," USDT"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["原始值: ",a.raw]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["数值: ",a.balanceNumber]}),a.contractInfo&&(0,r.jsxs)("div",{className:"mt-2 text-sm",children:[(0,r.jsxs)("p",{children:["名称: ",a.contractInfo.name]}),(0,r.jsxs)("p",{children:["符号: ",a.contractInfo.symbol]}),(0,r.jsxs)("p",{children:["小数位: ",a.contractInfo.decimals]})]})]}):(0,r.jsxs)("p",{className:"text-red-600",children:["错误: ",a.error]})]})]},s):null}),m.data.results.accountInfo&&(0,r.jsxs)(t.Z,{className:"bg-gray-50",children:[(0,r.jsx)(i.d,{children:(0,r.jsx)("h3",{className:"font-semibold",children:"账户信息"})}),(0,r.jsx)(d.U,{children:(0,r.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(m.data.results.accountInfo,null,2)})})]})]}):(0,r.jsx)(t.Z,{className:"bg-red-50",children:(0,r.jsx)(d.U,{children:(0,r.jsxs)("p",{className:"text-red-600",children:["测试失败: ",JSON.stringify(m.error)]})})}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["测试时间: ",m.timestamp]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"说明"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 此工具会测试多个可能的USDT合约地址"}),(0,r.jsx)("li",{children:"• 显示原始余额数据和处理后的结果"}),(0,r.jsx)("li",{children:"• 帮助诊断USDT余额获取问题"}),(0,r.jsx)("li",{children:"• 您的地址: TAjz662ivGjK792yaiTaeJcthDD4wTtX4m"})]})]})]})]})})}},77168:(e,s,a)=>{Promise.resolve().then(a.bind(a,67773))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,7443,4273,8688,201,6029,390,110,7358],()=>s(77168)),_N_E=e.O()}]);