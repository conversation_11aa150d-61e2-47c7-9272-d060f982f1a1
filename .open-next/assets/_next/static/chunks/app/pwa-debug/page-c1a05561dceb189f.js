(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8803],{19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>d});var i=l(31663),r=l(3208),n=l(23883),a=l(18884),t=l(19605),c=(0,r.Rf)((e,s)=>{var l;let{as:r,className:c,children:d,...o}=e,h=(0,n.zD)(s),{slots:m,classNames:x}=(0,i.f)(),j=(0,a.$z)(null==x?void 0:x.header,c);return(0,t.jsx)(r||"div",{ref:h,className:null==(l=m.header)?void 0:l.call(m,{class:j}),...o,children:d})});c.displayName="HeroUI.CardHeader";var d=c},50753:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var i=l(19605),r=l(9585),n=l(56995),a=l(21598),t=l(19864),c=l(63707),d=l(54110);function o(){let[e,s]=(0,r.useState)(null),[l,o]=(0,r.useState)(null),[h,m]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=async()=>{let e=navigator.userAgent,i=/Android/.test(e),r=e.includes("Chrome"),n=document.querySelector('link[rel="manifest"]'),a=(null==n?void 0:n.href)||null,t="Not supported";if("serviceWorker"in navigator)try{let e=await navigator.serviceWorker.getRegistration();t=e?e.active?"Active":"Registered but not active":"Not registered"}catch(e){t="Error checking"}s({isSecure:"https:"===location.protocol||"localhost"===location.hostname,hasServiceWorker:"serviceWorker"in navigator,hasManifest:null!==n,isStandalone:window.matchMedia("(display-mode: standalone)").matches,userAgent:e,isAndroid:i,isChrome:r,manifestUrl:a,serviceWorkerState:t,installPromptAvailable:null!==l})};e();let i=s=>{console.log("beforeinstallprompt event fired"),s.preventDefault(),o(s),e()},r=()=>{console.log("appinstalled event fired"),m("应用安装成功！"),o(null),e()};return window.addEventListener("beforeinstallprompt",i),window.addEventListener("appinstalled",r),()=>{window.removeEventListener("beforeinstallprompt",i),window.removeEventListener("appinstalled",r)}},[l]);let x=async()=>{if(!l)return void m("没有可用的安装提示");try{m("正在显示安装提示..."),await l.prompt();let e=await l.userChoice;"accepted"===e.outcome?m("用户接受了安装"):m("用户拒绝了安装"),o(null)}catch(e){m("安装失败: ".concat(e.message))}},j=async()=>{if(!(null==e?void 0:e.manifestUrl))return void alert("未找到manifest文件");try{var s;let l=await fetch(e.manifestUrl),i=await l.json();console.log("Manifest内容:",i),alert("Manifest检查完成，请查看控制台。\n\n应用名称: ".concat(i.name,"\n图标数量: ").concat((null==(s=i.icons)?void 0:s.length)||0))}catch(e){alert("Manifest检查失败: ".concat(e.message))}},v=async()=>{if(!("serviceWorker"in navigator))return void alert("浏览器不支持Service Worker");try{let e=await navigator.serviceWorker.getRegistration();e?(console.log("Service Worker注册信息:",e),alert("Service Worker状态: ".concat(e.active?"活跃":"已注册但未激活","\n\n请查看控制台获取详细信息。"))):alert("Service Worker未注册")}catch(e){alert("Service Worker检查失败: ".concat(e.message))}};return e?(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,i.jsx)(n.Z,{children:(0,i.jsx)(t.d,{children:(0,i.jsxs)("div",{className:"text-center w-full",children:[(0,i.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:"PWA 安装调试"}),(0,i.jsx)("p",{className:"text-gray-600",children:"检查PWA安装条件和调试安装问题"})]})})}),(0,i.jsxs)(n.Z,{children:[(0,i.jsx)(t.d,{children:(0,i.jsx)("h2",{className:"text-lg font-semibold",children:"PWA安装条件检查"})}),(0,i.jsx)(a.U,{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"HTTPS/本地环境:"}),(0,i.jsx)(c.R,{color:e.isSecure?"success":"danger",size:"sm",children:e.isSecure?"✓ 安全":"✗ 不安全"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"Service Worker:"}),(0,i.jsx)(c.R,{color:e.hasServiceWorker?"success":"danger",size:"sm",children:e.hasServiceWorker?"✓ 支持":"✗ 不支持"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"Manifest文件:"}),(0,i.jsx)(c.R,{color:e.hasManifest?"success":"danger",size:"sm",children:e.hasManifest?"✓ 存在":"✗ 缺失"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"已安装状态:"}),(0,i.jsx)(c.R,{color:e.isStandalone?"success":"default",size:"sm",children:e.isStandalone?"✓ 已安装":"未安装"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"Android设备:"}),(0,i.jsx)(c.R,{color:e.isAndroid?"success":"default",size:"sm",children:e.isAndroid?"✓ 是":"否"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"Chrome浏览器:"}),(0,i.jsx)(c.R,{color:e.isChrome?"success":"warning",size:"sm",children:e.isChrome?"✓ 是":"否"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"安装提示可用:"}),(0,i.jsx)(c.R,{color:e.installPromptAvailable?"success":"warning",size:"sm",children:e.installPromptAvailable?"✓ 可用":"不可用"})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{children:"SW状态:"}),(0,i.jsx)(c.R,{color:"Active"===e.serviceWorkerState?"success":"warning",size:"sm",children:e.serviceWorkerState})]})]})})]}),(0,i.jsxs)(n.Z,{children:[(0,i.jsx)(t.d,{children:(0,i.jsx)("h2",{className:"text-lg font-semibold",children:"设备信息"})}),(0,i.jsx)(a.U,{children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"User Agent:"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1 break-all",children:e.userAgent})]}),e.manifestUrl&&(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:"Manifest URL:"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mt-1 break-all",children:e.manifestUrl})]})]})})]}),(0,i.jsxs)(n.Z,{children:[(0,i.jsx)(t.d,{children:(0,i.jsx)("h2",{className:"text-lg font-semibold",children:"测试功能"})}),(0,i.jsxs)(a.U,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,i.jsx)(d.T,{color:"primary",onPress:x,disabled:!l,className:"w-full",children:"测试安装"}),(0,i.jsx)(d.T,{color:"secondary",variant:"bordered",onPress:j,className:"w-full",children:"检查Manifest"}),(0,i.jsx)(d.T,{color:"secondary",variant:"bordered",onPress:v,className:"w-full",children:"检查SW"})]}),h&&(0,i.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,i.jsx)("p",{className:"text-blue-800 text-sm",children:h})})]})]}),(0,i.jsxs)(n.Z,{className:"bg-yellow-50",children:[(0,i.jsx)(t.d,{children:(0,i.jsx)("h2",{className:"text-lg font-semibold text-yellow-800",children:"常见问题解决方案"})}),(0,i.jsx)(a.U,{children:(0,i.jsxs)("div",{className:"text-sm text-yellow-700 space-y-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold",children:"安装提示不出现:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[(0,i.jsx)("li",{children:"确保使用HTTPS或localhost"}),(0,i.jsx)("li",{children:"确保Service Worker已注册并激活"}),(0,i.jsx)("li",{children:"确保manifest.json文件可访问"}),(0,i.jsx)("li",{children:"确保manifest中有必需的图标"}),(0,i.jsx)("li",{children:"尝试刷新页面或清除缓存"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold",children:"Android Chrome特殊要求:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-1",children:[(0,i.jsx)("li",{children:"需要至少192x192和512x512的图标"}),(0,i.jsx)("li",{children:"manifest中display必须是standalone或fullscreen"}),(0,i.jsx)("li",{children:"需要start_url字段"}),(0,i.jsx)("li",{children:"用户需要与页面有交互"})]})]})]})})]})]})}):(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4",children:(0,i.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,i.jsx)(n.Z,{children:(0,i.jsx)(a.U,{className:"text-center",children:(0,i.jsx)("p",{children:"正在收集PWA调试信息..."})})})})})}},86900:(e,s,l)=>{Promise.resolve().then(l.bind(l,50753))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,1358,390,110,7358],()=>s(86900)),_N_E=e.O()}]);