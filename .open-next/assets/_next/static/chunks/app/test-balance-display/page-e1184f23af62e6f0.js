(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1865],{19864:(e,s,l)=>{"use strict";l.d(s,{d:()=>o});var r=l(31663),a=l(3208),t=l(23883),d=l(18884),i=l(19605),n=(0,a.Rf)((e,s)=>{var l;let{as:a,className:n,children:o,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:x}=(0,r.f)(),h=(0,d.$z)(null==x?void 0:x.header,n);return(0,i.jsx)(a||"div",{ref:u,className:null==(l=b.header)?void 0:l.call(b,{class:h}),...c,children:o})});n.displayName="HeroUI.CardHeader";var o=n},21598:(e,s,l)=>{"use strict";l.d(s,{U:()=>o});var r=l(31663),a=l(3208),t=l(23883),d=l(18884),i=l(19605),n=(0,a.Rf)((e,s)=>{var l;let{as:a,className:n,children:o,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:x}=(0,r.f)(),h=(0,d.$z)(null==x?void 0:x.body,n);return(0,i.jsx)(a||"div",{ref:u,className:null==(l=b.body)?void 0:l.call(b,{class:h}),...c,children:o})});n.displayName="HeroUI.CardBody";var o=n},26671:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>c});var r=l(19605),a=l(9585),t=l(56995),d=l(19864),i=l(21598),n=l(54110);let o=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;if(null==e)return"0."+"0".repeat(s);let l="string"==typeof e?parseFloat(e):e;return isNaN(l)?"0."+"0".repeat(s):l.toFixed(s)};function c(){let[e,s]=(0,a.useState)(null),l=async()=>{try{var e,l;let r=await fetch("/api/test-usdt-simple"),a=await r.json();s({type:"real_api",success:r.ok,data:a,formatted:{TRX:o(null==(e=a.balances)?void 0:e.TRX,6),USDT:o(null==(l=a.balances)?void 0:l.USDT,2)}})}catch(e){s({type:"real_api",success:!1,error:e})}};return(0,r.jsx)("div",{className:"container mx-auto p-4 max-w-4xl",children:(0,r.jsxs)(t.Z,{children:[(0,r.jsx)(d.d,{children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"余额显示测试"})}),(0,r.jsxs)(i.U,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n.T,{color:"primary",onPress:()=>{s([{value:114.935995,type:"number",expected:"114.94"},{value:"114.935995",type:"string",expected:"114.94"},{value:0,type:"zero",expected:"0.00"},{value:"0",type:"string zero",expected:"0.00"},{value:void 0,type:"undefined",expected:"0.00"},{value:null,type:"null",expected:"0.00"},{value:"invalid",type:"invalid string",expected:"0.00"},{value:1234.56789,type:"large number",expected:"1234.57"}].map(e=>({...e,result:o(e.value,2),success:o(e.value,2)===e.expected})))},children:"测试格式化函数"}),(0,r.jsx)(n.T,{color:"secondary",onPress:l,children:"测试真实余额API"})]}),e&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"测试结果"}),Array.isArray(e)?(0,r.jsx)("div",{className:"space-y-2",children:e.map((e,s)=>(0,r.jsx)(t.Z,{className:e.success?"bg-green-50":"bg-red-50",children:(0,r.jsx)(i.U,{children:(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"输入:"})," ",JSON.stringify(e.value)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"类型:"})," ",e.type]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"期望:"})," ",e.expected]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"结果:"})," ",e.result]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"状态:"}),(0,r.jsx)("span",{className:e.success?"text-green-600":"text-red-600",children:e.success?"✅ 通过":"❌ 失败"})]})]})})},s))}):"real_api"===e.type?(0,r.jsxs)(t.Z,{className:e.success?"bg-green-50":"bg-red-50",children:[(0,r.jsx)(d.d,{children:(0,r.jsx)("h3",{className:"font-semibold",children:"真实API测试结果"})}),(0,r.jsx)(i.U,{children:e.success?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"原始数据:"}),(0,r.jsx)("pre",{className:"text-sm bg-gray-100 p-2 rounded mt-1",children:JSON.stringify(e.data.balances,null,2)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"格式化后:"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-blue-100 rounded",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[e.formatted.TRX," TRX"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"TRON"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-green-100 rounded",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[e.formatted.USDT," USDT"]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Tether USD"})]})]})]})]}):(0,r.jsxs)("div",{className:"text-red-600",children:["错误: ",JSON.stringify(e.error)]})})]}):null]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-semibold text-blue-800 mb-2",children:"说明"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,r.jsx)("li",{children:"• 测试格式化函数：验证不同类型的余额数据格式化"}),(0,r.jsx)("li",{children:"• 测试真实API：从实际API获取余额并格式化显示"}),(0,r.jsx)("li",{children:"• 确保所有类型的余额数据都能正确显示"}),(0,r.jsx)("li",{children:"• 处理undefined、null、字符串、数字等各种情况"})]})]})]})]})})}},31663:(e,s,l)=>{"use strict";l.d(s,{f:()=>a,u:()=>r});var[r,a]=(0,l(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},56995:(e,s,l)=>{"use strict";l.d(s,{Z:()=>N});var r=l(31663),a=l(92610),t=l(56457),d=(0,a.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),i=l(9585),n=l(26423),o=l(96539),c=l(7484),u=l(85823),b=l(90890),x=l(31081),h=l(3208),m=l(18884),v=l(9733),p=l(23883),f=l(32965),g=l(14171),j=l(19605),y=(0,h.Rf)((e,s)=>{let{children:l,context:a,Component:t,isPressable:y,disableAnimation:N,disableRipple:w,getCardProps:k,getRippleProps:P}=function(e){var s,l,r,a;let t=(0,x.o)(),[g,j]=(0,h.rE)(e,d.variantKeys),{ref:y,as:N,children:w,onClick:k,onPress:P,autoFocus:D,className:C,classNames:T,allowTextSelectionOnPress:U=!0,...H}=g,z=(0,p.zD)(y),R=N||(e.isPressable?"button":"div"),S="string"==typeof R,A=null!=(l=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&l,E=null!=(a=null!=(r=e.disableRipple)?r:null==t?void 0:t.disableRipple)&&a,_=(0,m.$z)(null==T?void 0:T.base,C),{onClear:I,onPress:B,ripples:F}=(0,f.k)(),O=(0,i.useCallback)(e=>{E||A||z.current&&B(e)},[E,A,z,B]),{buttonProps:W,isPressed:$}=(0,b.l)({onPress:(0,n.c)(P,O),elementType:N,isDisabled:!e.isPressable,onClick:k,allowTextSelectionOnPress:U,...H},z),{hoverProps:M,isHovered:X}=(0,u.M)({isDisabled:!e.isHoverable,...H}),{isFocusVisible:Z,isFocused:J,focusProps:V}=(0,c.o)({autoFocus:D}),q=(0,i.useMemo)(()=>d({...j,disableAnimation:A}),[(0,m.t6)(j),A]),K=(0,i.useMemo)(()=>({slots:q,classNames:T,disableAnimation:A,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[q,T,e.isDisabled,e.isFooterBlurred,A,e.fullWidth]),G=(0,i.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:z,className:q.base({class:_}),tabIndex:e.isPressable?0:-1,"data-hover":(0,m.sE)(X),"data-pressed":(0,m.sE)($),"data-focus":(0,m.sE)(J),"data-focus-visible":(0,m.sE)(Z),"data-disabled":(0,m.sE)(e.isDisabled),...(0,o.v)(e.isPressable?{...W,...V,role:"button"}:{},e.isHoverable?M:{},(0,v.$)(H,{enabled:S}),(0,v.$)(s))}},[z,q,_,S,e.isPressable,e.isHoverable,e.isDisabled,X,$,Z,W,V,M,H]),L=(0,i.useCallback)(()=>({ripples:F,onClear:I}),[F,I]);return{context:K,domRef:z,Component:R,classNames:T,children:w,isHovered:X,isPressed:$,disableAnimation:A,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:E,handlePress:O,isFocusVisible:Z,getCardProps:G,getRippleProps:L}}({...e,ref:s});return(0,j.jsxs)(t,{...k(),children:[(0,j.jsx)(r.u,{value:a,children:l}),y&&!N&&!w&&(0,j.jsx)(g.j,{...P()})]})});y.displayName="HeroUI.Card";var N=y},81912:(e,s,l)=>{Promise.resolve().then(l.bind(l,26671))}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,390,110,7358],()=>s(81912)),_N_E=e.O()}]);