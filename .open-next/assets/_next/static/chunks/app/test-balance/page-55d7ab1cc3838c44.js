(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9250],{15076:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var r=a(19605),l=a(9585),t=a(56995),o=a(19864),i=a(21598),n=a(66700),d=a(57059),u=a(5230),c=a(54110);function b(){let[e,s]=(0,l.useState)("TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf"),[a,b]=(0,l.useState)("nile"),[h,f]=(0,l.useState)(!1),[m,x]=(0,l.useState)(null),p=async s=>{f(!0);try{let r=await fetch("/api/wallets/balance",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({address:e,currency:s,network:a})}),l=await r.json();x(e=>({...e,[s]:{success:r.ok,data:l,timestamp:new Date().toLocaleString()}}))}catch(e){x(a=>({...a,[s]:{success:!1,error:e,timestamp:new Date().toLocaleString()}}))}finally{f(!1)}};return(0,r.jsx)("div",{className:"container mx-auto p-4 max-w-4xl",children:(0,r.jsxs)(t.Z,{children:[(0,r.jsx)(o.d,{children:(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"TRON余额测试"})}),(0,r.jsxs)(i.U,{className:"space-y-4",children:[(0,r.jsx)(n.r,{label:"TRON地址",value:e,onChange:e=>s(e.target.value),placeholder:"输入TRON地址"}),(0,r.jsxs)(d.d,{label:"网络",selectedKeys:[a],onSelectionChange:e=>{b(Array.from(e)[0])},children:[(0,r.jsx)(u.y,{children:"主网 (Mainnet)"},"mainnet"),(0,r.jsx)(u.y,{children:"测试网 (Nile)"},"nile"),(0,r.jsx)(u.y,{children:"测试网 (Shasta)"},"shasta")]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(c.T,{color:"primary",onPress:()=>p("TRX"),isLoading:h,children:"测试TRX余额"}),(0,r.jsx)(c.T,{color:"secondary",onPress:()=>p("USDT"),isLoading:h,children:"测试USDT余额"})]}),m&&(0,r.jsxs)("div",{className:"space-y-4",children:[m.TRX&&(0,r.jsxs)(t.Z,{children:[(0,r.jsx)(o.d,{children:(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"TRX余额结果"})}),(0,r.jsx)(i.U,{children:(0,r.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(m.TRX,null,2)})})]}),m.USDT&&(0,r.jsxs)(t.Z,{children:[(0,r.jsx)(o.d,{children:(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"USDT余额结果"})}),(0,r.jsx)(i.U,{children:(0,r.jsx)("pre",{className:"text-sm bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(m.USDT,null,2)})})]})]})]})]})})}},21598:(e,s,a)=>{"use strict";a.d(s,{U:()=>d});var r=a(31663),l=a(3208),t=a(23883),o=a(18884),i=a(19605),n=(0,l.Rf)((e,s)=>{var a;let{as:l,className:n,children:d,...u}=e,c=(0,t.zD)(s),{slots:b,classNames:h}=(0,r.f)(),f=(0,o.$z)(null==h?void 0:h.body,n);return(0,i.jsx)(l||"div",{ref:c,className:null==(a=b.body)?void 0:a.call(b,{class:f}),...u,children:d})});n.displayName="HeroUI.CardBody";var d=n},28817:(e,s,a)=>{"use strict";a.d(s,{o:()=>l});var r=a(19605),l=e=>(0,r.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,r.jsx)("path",{d:"M12 2a10 10 0 1010 10A10.016 10.016 0 0012 2zm3.36 12.3a.754.754 0 010 1.06.748.748 0 01-1.06 0l-2.3-2.3-2.3 2.3a.748.748 0 01-1.06 0 .754.754 0 010-1.06l2.3-2.3-2.3-2.3A.75.75 0 019.7 8.64l2.3 2.3 2.3-2.3a.75.75 0 011.06 1.06l-2.3 2.3z",fill:"currentColor"})})},29613:(e,s,a)=>{Promise.resolve().then(a.bind(a,15076))},31663:(e,s,a)=>{"use strict";a.d(s,{f:()=>l,u:()=>r});var[r,l]=(0,a(71242).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},56995:(e,s,a)=>{"use strict";a.d(s,{Z:()=>w});var r=a(31663),l=a(92610),t=a(56457),o=(0,l.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),i=a(9585),n=a(26423),d=a(96539),u=a(7484),c=a(85823),b=a(90890),h=a(31081),f=a(3208),m=a(18884),x=a(9733),p=a(23883),v=a(32965),g=a(14171),j=a(19605),y=(0,f.Rf)((e,s)=>{let{children:a,context:l,Component:t,isPressable:y,disableAnimation:w,disableRipple:k,getCardProps:N,getRippleProps:D}=function(e){var s,a,r,l;let t=(0,h.o)(),[g,j]=(0,f.rE)(e,o.variantKeys),{ref:y,as:w,children:k,onClick:N,onPress:D,autoFocus:T,className:C,classNames:S,allowTextSelectionOnPress:P=!0,...R}=g,U=(0,p.zD)(y),B=w||(e.isPressable?"button":"div"),E="string"==typeof B,H=null!=(a=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&a,O=null!=(l=null!=(r=e.disableRipple)?r:null==t?void 0:t.disableRipple)&&l,z=(0,m.$z)(null==S?void 0:S.base,C),{onClear:X,onPress:A,ripples:M}=(0,v.k)(),Z=(0,i.useCallback)(e=>{O||H||U.current&&A(e)},[O,H,U,A]),{buttonProps:_,isPressed:F}=(0,b.l)({onPress:(0,n.c)(D,Z),elementType:w,isDisabled:!e.isPressable,onClick:N,allowTextSelectionOnPress:P,...R},U),{hoverProps:W,isHovered:L}=(0,c.M)({isDisabled:!e.isHoverable,...R}),{isFocusVisible:$,isFocused:I,focusProps:J}=(0,u.o)({autoFocus:T}),V=(0,i.useMemo)(()=>o({...j,disableAnimation:H}),[(0,m.t6)(j),H]),K=(0,i.useMemo)(()=>({slots:V,classNames:S,disableAnimation:H,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[V,S,e.isDisabled,e.isFooterBlurred,H,e.fullWidth]),Y=(0,i.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:U,className:V.base({class:z}),tabIndex:e.isPressable?0:-1,"data-hover":(0,m.sE)(L),"data-pressed":(0,m.sE)(F),"data-focus":(0,m.sE)(I),"data-focus-visible":(0,m.sE)($),"data-disabled":(0,m.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{..._,...J,role:"button"}:{},e.isHoverable?W:{},(0,x.$)(R,{enabled:E}),(0,x.$)(s))}},[U,V,z,E,e.isPressable,e.isHoverable,e.isDisabled,L,F,$,_,J,W,R]),q=(0,i.useCallback)(()=>({ripples:M,onClear:X}),[M,X]);return{context:K,domRef:U,Component:B,classNames:S,children:k,isHovered:L,isPressed:F,disableAnimation:H,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:O,handlePress:Z,isFocusVisible:$,getCardProps:Y,getRippleProps:q}}({...e,ref:s});return(0,j.jsxs)(t,{...N(),children:[(0,j.jsx)(r.u,{value:l,children:a}),y&&!w&&!k&&(0,j.jsx)(g.j,{...D()})]})});y.displayName="HeroUI.Card";var w=y}},e=>{var s=s=>e(e.s=s);e.O(0,[8031,4110,7443,4273,8688,201,6029,390,110,7358],()=>s(29613)),_N_E=e.O()}]);