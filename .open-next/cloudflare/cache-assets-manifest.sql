CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/_not-found"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/_not-found/layout", "V1pb2o6WueLFz5aEdP0nG/_not-found"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/_not-found/page", "V1pb2o6WueLFz5aEdP0nG/_not-found"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/_not-found", "V1pb2o6WueLFz5aEdP0nG/_not-found"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/account-activation"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/account-activation/layout", "V1pb2o6WueLFz5aEdP0nG/account-activation"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/account-activation/page", "V1pb2o6WueLFz5aEdP0nG/account-activation"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/account-activation", "V1pb2o6WueLFz5aEdP0nG/account-activation"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/admin/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/layout", "V1pb2o6WueLFz5aEdP0nG/admin/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/brush/layout", "V1pb2o6WueLFz5aEdP0nG/admin/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/brush/page", "V1pb2o6WueLFz5aEdP0nG/admin/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/brush", "V1pb2o6WueLFz5aEdP0nG/admin/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/admin-mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin-mobile-test/layout", "V1pb2o6WueLFz5aEdP0nG/admin-mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin-mobile-test/page", "V1pb2o6WueLFz5aEdP0nG/admin-mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin-mobile-test", "V1pb2o6WueLFz5aEdP0nG/admin-mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/admin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/layout", "V1pb2o6WueLFz5aEdP0nG/admin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin/page", "V1pb2o6WueLFz5aEdP0nG/admin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/admin", "V1pb2o6WueLFz5aEdP0nG/admin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/auth/error"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/layout", "V1pb2o6WueLFz5aEdP0nG/auth/error"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/error/layout", "V1pb2o6WueLFz5aEdP0nG/auth/error"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/error/page", "V1pb2o6WueLFz5aEdP0nG/auth/error"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/error", "V1pb2o6WueLFz5aEdP0nG/auth/error"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/auth/register"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/layout", "V1pb2o6WueLFz5aEdP0nG/auth/register"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/register/layout", "V1pb2o6WueLFz5aEdP0nG/auth/register"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/register/page", "V1pb2o6WueLFz5aEdP0nG/auth/register"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/register", "V1pb2o6WueLFz5aEdP0nG/auth/register"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/auth/signin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/layout", "V1pb2o6WueLFz5aEdP0nG/auth/signin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/signin/layout", "V1pb2o6WueLFz5aEdP0nG/auth/signin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/signin/page", "V1pb2o6WueLFz5aEdP0nG/auth/signin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/auth/signin", "V1pb2o6WueLFz5aEdP0nG/auth/signin"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/brush/layout", "V1pb2o6WueLFz5aEdP0nG/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/brush/page", "V1pb2o6WueLFz5aEdP0nG/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/brush", "V1pb2o6WueLFz5aEdP0nG/brush"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/download"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/download/layout", "V1pb2o6WueLFz5aEdP0nG/download"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/download/page", "V1pb2o6WueLFz5aEdP0nG/download"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/download", "V1pb2o6WueLFz5aEdP0nG/download"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/favicon.ico"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/favicon.ico/layout", "V1pb2o6WueLFz5aEdP0nG/favicon.ico"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/favicon.ico/route", "V1pb2o6WueLFz5aEdP0nG/favicon.ico"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/favicon.ico", "V1pb2o6WueLFz5aEdP0nG/favicon.ico"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/generate-icons"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-icons/layout", "V1pb2o6WueLFz5aEdP0nG/generate-icons"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-icons/page", "V1pb2o6WueLFz5aEdP0nG/generate-icons"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-icons", "V1pb2o6WueLFz5aEdP0nG/generate-icons"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/generate-screenshots"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-screenshots/layout", "V1pb2o6WueLFz5aEdP0nG/generate-screenshots"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-screenshots/page", "V1pb2o6WueLFz5aEdP0nG/generate-screenshots"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/generate-screenshots", "V1pb2o6WueLFz5aEdP0nG/generate-screenshots"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/index"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/page", "V1pb2o6WueLFz5aEdP0nG/index"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/", "V1pb2o6WueLFz5aEdP0nG/index"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/mobile-test/layout", "V1pb2o6WueLFz5aEdP0nG/mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/mobile-test/page", "V1pb2o6WueLFz5aEdP0nG/mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/mobile-test", "V1pb2o6WueLFz5aEdP0nG/mobile-test"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/network-status"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/network-status/layout", "V1pb2o6WueLFz5aEdP0nG/network-status"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/network-status/page", "V1pb2o6WueLFz5aEdP0nG/network-status"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/network-status", "V1pb2o6WueLFz5aEdP0nG/network-status"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/pwa-debug"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/pwa-debug/layout", "V1pb2o6WueLFz5aEdP0nG/pwa-debug"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/pwa-debug/page", "V1pb2o6WueLFz5aEdP0nG/pwa-debug"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/pwa-debug", "V1pb2o6WueLFz5aEdP0nG/pwa-debug"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/security-notice"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/security-notice/layout", "V1pb2o6WueLFz5aEdP0nG/security-notice"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/security-notice/page", "V1pb2o6WueLFz5aEdP0nG/security-notice"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/security-notice", "V1pb2o6WueLFz5aEdP0nG/security-notice"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-balance-display"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance-display/layout", "V1pb2o6WueLFz5aEdP0nG/test-balance-display"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance-display/page", "V1pb2o6WueLFz5aEdP0nG/test-balance-display"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance-display", "V1pb2o6WueLFz5aEdP0nG/test-balance-display"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance/layout", "V1pb2o6WueLFz5aEdP0nG/test-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance/page", "V1pb2o6WueLFz5aEdP0nG/test-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-balance", "V1pb2o6WueLFz5aEdP0nG/test-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-brush-modal"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-brush-modal/layout", "V1pb2o6WueLFz5aEdP0nG/test-brush-modal"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-brush-modal/page", "V1pb2o6WueLFz5aEdP0nG/test-brush-modal"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-brush-modal", "V1pb2o6WueLFz5aEdP0nG/test-brush-modal"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-fixes"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-fixes/layout", "V1pb2o6WueLFz5aEdP0nG/test-fixes"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-fixes/page", "V1pb2o6WueLFz5aEdP0nG/test-fixes"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-fixes", "V1pb2o6WueLFz5aEdP0nG/test-fixes"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-nile-complete"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-nile-complete/layout", "V1pb2o6WueLFz5aEdP0nG/test-nile-complete"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-nile-complete/page", "V1pb2o6WueLFz5aEdP0nG/test-nile-complete"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-nile-complete", "V1pb2o6WueLFz5aEdP0nG/test-nile-complete"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-usdt-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-usdt-balance/layout", "V1pb2o6WueLFz5aEdP0nG/test-usdt-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-usdt-balance/page", "V1pb2o6WueLFz5aEdP0nG/test-usdt-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-usdt-balance", "V1pb2o6WueLFz5aEdP0nG/test-usdt-balance"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/test-zoom"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-zoom/layout", "V1pb2o6WueLFz5aEdP0nG/test-zoom"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-zoom/page", "V1pb2o6WueLFz5aEdP0nG/test-zoom"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/test-zoom", "V1pb2o6WueLFz5aEdP0nG/test-zoom"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/wallet-password-help"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet-password-help/layout", "V1pb2o6WueLFz5aEdP0nG/wallet-password-help"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet-password-help/page", "V1pb2o6WueLFz5aEdP0nG/wallet-password-help"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet-password-help", "V1pb2o6WueLFz5aEdP0nG/wallet-password-help"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/layout", "V1pb2o6WueLFz5aEdP0nG/wallet"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet/layout", "V1pb2o6WueLFz5aEdP0nG/wallet"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet/page", "V1pb2o6WueLFz5aEdP0nG/wallet"), ("V1pb2o6WueLFz5aEdP0nG/_N_T_/wallet", "V1pb2o6WueLFz5aEdP0nG/wallet");