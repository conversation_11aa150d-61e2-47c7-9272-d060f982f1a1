CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/_not-found"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/_not-found/layout", "I3dRfcQDjXwxot1pWOofB/_not-found"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/_not-found/page", "I3dRfcQDjXwxot1pWOofB/_not-found"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/_not-found", "I3dRfcQDjXwxot1pWOofB/_not-found"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/account-activation"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/account-activation/layout", "I3dRfcQDjXwxot1pWOofB/account-activation"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/account-activation/page", "I3dRfcQDjXwxot1pWOofB/account-activation"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/account-activation", "I3dRfcQDjXwxot1pWOofB/account-activation"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/admin/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/layout", "I3dRfcQDjXwxot1pWOofB/admin/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/brush/layout", "I3dRfcQDjXwxot1pWOofB/admin/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/brush/page", "I3dRfcQDjXwxot1pWOofB/admin/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/brush", "I3dRfcQDjXwxot1pWOofB/admin/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/admin-mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin-mobile-test/layout", "I3dRfcQDjXwxot1pWOofB/admin-mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin-mobile-test/page", "I3dRfcQDjXwxot1pWOofB/admin-mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin-mobile-test", "I3dRfcQDjXwxot1pWOofB/admin-mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/admin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/layout", "I3dRfcQDjXwxot1pWOofB/admin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin/page", "I3dRfcQDjXwxot1pWOofB/admin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/admin", "I3dRfcQDjXwxot1pWOofB/admin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/auth/error"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/layout", "I3dRfcQDjXwxot1pWOofB/auth/error"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/error/layout", "I3dRfcQDjXwxot1pWOofB/auth/error"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/error/page", "I3dRfcQDjXwxot1pWOofB/auth/error"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/error", "I3dRfcQDjXwxot1pWOofB/auth/error"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/auth/register"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/layout", "I3dRfcQDjXwxot1pWOofB/auth/register"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/register/layout", "I3dRfcQDjXwxot1pWOofB/auth/register"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/register/page", "I3dRfcQDjXwxot1pWOofB/auth/register"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/register", "I3dRfcQDjXwxot1pWOofB/auth/register"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/auth/signin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/layout", "I3dRfcQDjXwxot1pWOofB/auth/signin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/signin/layout", "I3dRfcQDjXwxot1pWOofB/auth/signin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/signin/page", "I3dRfcQDjXwxot1pWOofB/auth/signin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/auth/signin", "I3dRfcQDjXwxot1pWOofB/auth/signin"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/brush/layout", "I3dRfcQDjXwxot1pWOofB/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/brush/page", "I3dRfcQDjXwxot1pWOofB/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/brush", "I3dRfcQDjXwxot1pWOofB/brush"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/download"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/download/layout", "I3dRfcQDjXwxot1pWOofB/download"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/download/page", "I3dRfcQDjXwxot1pWOofB/download"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/download", "I3dRfcQDjXwxot1pWOofB/download"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/favicon.ico"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/favicon.ico/layout", "I3dRfcQDjXwxot1pWOofB/favicon.ico"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/favicon.ico/route", "I3dRfcQDjXwxot1pWOofB/favicon.ico"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/favicon.ico", "I3dRfcQDjXwxot1pWOofB/favicon.ico"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/generate-icons"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-icons/layout", "I3dRfcQDjXwxot1pWOofB/generate-icons"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-icons/page", "I3dRfcQDjXwxot1pWOofB/generate-icons"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-icons", "I3dRfcQDjXwxot1pWOofB/generate-icons"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/generate-screenshots"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-screenshots/layout", "I3dRfcQDjXwxot1pWOofB/generate-screenshots"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-screenshots/page", "I3dRfcQDjXwxot1pWOofB/generate-screenshots"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/generate-screenshots", "I3dRfcQDjXwxot1pWOofB/generate-screenshots"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/index"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/page", "I3dRfcQDjXwxot1pWOofB/index"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/", "I3dRfcQDjXwxot1pWOofB/index"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/mobile-test/layout", "I3dRfcQDjXwxot1pWOofB/mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/mobile-test/page", "I3dRfcQDjXwxot1pWOofB/mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/mobile-test", "I3dRfcQDjXwxot1pWOofB/mobile-test"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/network-status"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/network-status/layout", "I3dRfcQDjXwxot1pWOofB/network-status"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/network-status/page", "I3dRfcQDjXwxot1pWOofB/network-status"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/network-status", "I3dRfcQDjXwxot1pWOofB/network-status"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/pwa-debug"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/pwa-debug/layout", "I3dRfcQDjXwxot1pWOofB/pwa-debug"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/pwa-debug/page", "I3dRfcQDjXwxot1pWOofB/pwa-debug"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/pwa-debug", "I3dRfcQDjXwxot1pWOofB/pwa-debug"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/security-notice"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/security-notice/layout", "I3dRfcQDjXwxot1pWOofB/security-notice"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/security-notice/page", "I3dRfcQDjXwxot1pWOofB/security-notice"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/security-notice", "I3dRfcQDjXwxot1pWOofB/security-notice"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-balance-display"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance-display/layout", "I3dRfcQDjXwxot1pWOofB/test-balance-display"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance-display/page", "I3dRfcQDjXwxot1pWOofB/test-balance-display"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance-display", "I3dRfcQDjXwxot1pWOofB/test-balance-display"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance/layout", "I3dRfcQDjXwxot1pWOofB/test-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance/page", "I3dRfcQDjXwxot1pWOofB/test-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-balance", "I3dRfcQDjXwxot1pWOofB/test-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-brush-modal"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-brush-modal/layout", "I3dRfcQDjXwxot1pWOofB/test-brush-modal"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-brush-modal/page", "I3dRfcQDjXwxot1pWOofB/test-brush-modal"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-brush-modal", "I3dRfcQDjXwxot1pWOofB/test-brush-modal"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-fixes"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-fixes/layout", "I3dRfcQDjXwxot1pWOofB/test-fixes"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-fixes/page", "I3dRfcQDjXwxot1pWOofB/test-fixes"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-fixes", "I3dRfcQDjXwxot1pWOofB/test-fixes"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-nile-complete"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-nile-complete/layout", "I3dRfcQDjXwxot1pWOofB/test-nile-complete"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-nile-complete/page", "I3dRfcQDjXwxot1pWOofB/test-nile-complete"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-nile-complete", "I3dRfcQDjXwxot1pWOofB/test-nile-complete"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-usdt-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-usdt-balance/layout", "I3dRfcQDjXwxot1pWOofB/test-usdt-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-usdt-balance/page", "I3dRfcQDjXwxot1pWOofB/test-usdt-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-usdt-balance", "I3dRfcQDjXwxot1pWOofB/test-usdt-balance"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/test-zoom"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-zoom/layout", "I3dRfcQDjXwxot1pWOofB/test-zoom"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-zoom/page", "I3dRfcQDjXwxot1pWOofB/test-zoom"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/test-zoom", "I3dRfcQDjXwxot1pWOofB/test-zoom"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/wallet-password-help"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet-password-help/layout", "I3dRfcQDjXwxot1pWOofB/wallet-password-help"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet-password-help/page", "I3dRfcQDjXwxot1pWOofB/wallet-password-help"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet-password-help", "I3dRfcQDjXwxot1pWOofB/wallet-password-help"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/layout", "I3dRfcQDjXwxot1pWOofB/wallet"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet/layout", "I3dRfcQDjXwxot1pWOofB/wallet"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet/page", "I3dRfcQDjXwxot1pWOofB/wallet"), ("I3dRfcQDjXwxot1pWOofB/_N_T_/wallet", "I3dRfcQDjXwxot1pWOofB/wallet");