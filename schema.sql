-- 清理所有现有表

-- 用户表 (支持账号密码登录和邀请码注册)
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL, -- 用户名，用于登录
    email TEXT UNIQUE, -- 邮箱，可选
    password_hash TEXT NOT NULL, -- 密码哈希
    name TEXT, -- 显示名称
    avatar_url TEXT,
    invite_code_id INTEGER, -- 注册时使用的邀请码ID
    invited_by INTEGER, -- 邀请人ID
    mfa_secret TEXT, -- MFA密钥
    mfa_enabled BOOLEAN DEFAULT FALSE, -- 是否启用MFA
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 邀请码表
CREATE TABLE IF NOT EXISTS invite_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    created_by INTEGER, -- 创建者ID，NULL表示系统创建
    used_by INTEGER, -- 使用者ID，NULL表示未使用
    is_active BOOLEAN DEFAULT TRUE,
    max_uses INTEGER DEFAULT 1, -- 最大使用次数，-1表示无限制
    used_count INTEGER DEFAULT 0, -- 已使用次数
    expires_at DATETIME, -- 过期时间，NULL表示永不过期
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    used_at DATETIME -- 使用时间
);

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL UNIQUE, -- 关联到用户表
    role TEXT NOT NULL DEFAULT 'agent', -- 'super_admin', 'agent'
    permissions TEXT, -- JSON格式存储权限列表
    parent_admin_id INTEGER, -- 上级管理员ID，用于代理层级关系
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 钱包表 (用户的TRON钱包)
CREATE TABLE IF NOT EXISTS wallets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    name TEXT NOT NULL, -- 钱包名称，用户自定义
    address TEXT NOT NULL, -- TRON地址
    private_key_encrypted TEXT, -- 加密的私钥（仅对创建的钱包）
    wallet_type TEXT NOT NULL, -- 'created', 'imported'
    network TEXT DEFAULT 'nile', -- 'mainnet', 'nile', 'shasta'
    is_default BOOLEAN DEFAULT FALSE, -- 是否为默认钱包
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, address)
);

-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    wallet_id INTEGER NOT NULL,
    tx_hash TEXT NOT NULL UNIQUE, -- 区块链交易哈希
    from_address TEXT NOT NULL,
    to_address TEXT NOT NULL,
    amount DECIMAL(20, 8) NOT NULL, -- 支持高精度
    currency TEXT NOT NULL, -- 'TRX', 'USDT'
    tx_type TEXT NOT NULL, -- 'send', 'receive'
    status TEXT DEFAULT 'pending', -- 'pending', 'confirmed', 'failed'
    block_number INTEGER,
    gas_used INTEGER,
    gas_price DECIMAL(20, 8),
    network TEXT DEFAULT 'mainnet', -- 'mainnet', 'nile', 'shasta'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    confirmed_at DATETIME
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插入系统初始邀请码（用于创建第一个超级管理员）
INSERT INTO invite_codes (code, created_by, is_active, max_uses, expires_at) VALUES
    ('SUPER_ADMIN_INIT', NULL, TRUE, 1, NULL);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
    ('app_name', 'TRON钱包', '应用名称'),
    ('default_network', 'mainnet', '默认TRON网络'),
    ('supported_currencies', '["TRX", "USDT"]', '支持的币种'),
    ('min_transfer_amount_trx', '1', '最小TRX转账金额'),
    ('min_transfer_amount_usdt', '1', '最小USDT转账金额'),
    ('max_transfer_amount_trx', '1000000', '最大TRX转账金额'),
    ('max_transfer_amount_usdt', '100000', '最大USDT转账金额'),
    ('admin_permissions', '{"super_admin": ["manage_users", "manage_admins", "view_all_wallets", "view_all_transactions", "system_config"], "agent": ["manage_invited_users", "view_invited_wallets", "view_invited_transactions"]}', '管理员权限配置');


-- ===== 刷单系统相关表 =====

-- 商品表
CREATE TABLE IF NOT EXISTS products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    image VARCHAR(255), -- url
    price INTEGER NOT NULL,-- 分
    description TEXT,
    status TINYINT DEFAULT 1,  -- 1: 正常, 0: 下架
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户余额表（内部账户）
CREATE TABLE IF NOT EXISTS user_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL UNIQUE,
    balance INTEGER DEFAULT 0, -- 余额，单位：分
    frozen_balance INTEGER DEFAULT 0, -- 冻结余额，单位：分
    total_recharged INTEGER DEFAULT 0, -- 累计充值金额，单位：分
    total_spent INTEGER DEFAULT 0, -- 累计消费金额，单位：分
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 充值记录表
CREATE TABLE IF NOT EXISTS recharge_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    tx_hash VARCHAR(100) NOT NULL UNIQUE, -- 区块链交易哈希
    from_address VARCHAR(50) NOT NULL, -- 用户钱包地址
    to_address VARCHAR(50) NOT NULL, -- 平台收款地址
    usdt_amount DECIMAL(20, 8) NOT NULL, -- USDT金额
    internal_amount INTEGER NOT NULL, -- 内部账户金额，单位：分
    exchange_rate DECIMAL(10, 4) DEFAULT 1.0000, -- 汇率 (USDT to 内部币)
    status TINYINT DEFAULT 0, -- 0: 待确认, 1: 已确认, 2: 失败
    network VARCHAR(20) DEFAULT 'mainnet', -- 网络类型
    block_number INTEGER,
    confirmed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 刷单订单表
CREATE TABLE IF NOT EXISTS brush_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    order_no VARCHAR(32) NOT NULL UNIQUE, -- 订单号
    product_name VARCHAR(100) NOT NULL, -- 商品名称快照
    product_price INTEGER NOT NULL, -- 商品价格快照，单位：分
    quantity INTEGER DEFAULT 1, -- 购买数量
    total_amount INTEGER NOT NULL, -- 订单总金额，单位：分
    commission_rate DECIMAL(5, 4) DEFAULT 0.0500, -- 佣金比例，默认5%
    commission_amount INTEGER DEFAULT 0, -- 佣金金额，单位：分
    status TINYINT DEFAULT 0, -- 0: 待付款, 1: 已付款, 2: 已完成, 3: 已取消, 4: 爆单
    is_burst BOOLEAN DEFAULT FALSE, -- 是否为爆单
    burst_reason TEXT, -- 爆单原因
    completed_at TIMESTAMP, -- 完成时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 刷单规则表
CREATE TABLE IF NOT EXISTS brush_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL, -- 规则名称
    description TEXT, -- 规则描述
    is_active BOOLEAN DEFAULT TRUE, -- 是否启用
    burst_probability DECIMAL(5, 4) DEFAULT 0.1000, -- 爆单概率，默认10%
    burst_order_range VARCHAR(20) DEFAULT '3-7', -- 爆单订单范围，如 "3-7" 表示第3-7单可能爆单
    min_commission_rate DECIMAL(5, 4) DEFAULT 0.0300, -- 最小佣金比例
    max_commission_rate DECIMAL(5, 4) DEFAULT 0.0800, -- 最大佣金比例
    daily_order_limit INTEGER DEFAULT 50, -- 每日订单限制
    min_order_interval INTEGER DEFAULT 30, -- 最小下单间隔（秒）
    max_order_interval INTEGER DEFAULT 300, -- 最大下单间隔（秒）
    config_json TEXT, -- 其他配置，JSON格式
    created_by INTEGER, -- 创建者ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户刷单统计表
CREATE TABLE IF NOT EXISTS user_brush_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL UNIQUE,
    total_orders INTEGER DEFAULT 0, -- 总订单数
    completed_orders INTEGER DEFAULT 0, -- 完成订单数
    burst_orders INTEGER DEFAULT 0, -- 爆单数
    total_spent INTEGER DEFAULT 0, -- 总消费金额，单位：分
    total_commission INTEGER DEFAULT 0, -- 总佣金收入，单位：分
    today_orders INTEGER DEFAULT 0, -- 今日订单数
    today_spent INTEGER DEFAULT 0, -- 今日消费，单位：分
    today_commission INTEGER DEFAULT 0, -- 今日佣金，单位：分
    last_order_at TIMESTAMP, -- 最后下单时间
    stats_date DATE DEFAULT (DATE('now')), -- 统计日期
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 余额变动记录表
CREATE TABLE IF NOT EXISTS balance_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type TINYINT NOT NULL, -- 1: 充值, 2: 消费, 3: 佣金收入, 4: 退款, 5: 冻结, 6: 解冻
    amount INTEGER NOT NULL, -- 变动金额，单位：分
    balance_before INTEGER NOT NULL, -- 变动前余额
    balance_after INTEGER NOT NULL, -- 变动后余额
    related_id INTEGER, -- 关联ID（订单ID、充值记录ID等）
    related_type VARCHAR(20), -- 关联类型 (order, recharge, etc.)
    description VARCHAR(200), -- 变动描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 平台钱包配置表
CREATE TABLE IF NOT EXISTS platform_wallets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL, -- 钱包名称
    address VARCHAR(50) NOT NULL UNIQUE, -- 钱包地址
    network VARCHAR(20) NOT NULL, -- 网络类型
    currency VARCHAR(10) NOT NULL, -- 币种
    is_active BOOLEAN DEFAULT TRUE, -- 是否启用
    is_default BOOLEAN DEFAULT FALSE, -- 是否为默认钱包
    description TEXT, -- 描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认刷单规则
INSERT OR IGNORE INTO brush_rules (name, description, is_active, burst_probability, burst_order_range, min_commission_rate, max_commission_rate, daily_order_limit) VALUES
    ('默认规则', '系统默认刷单规则', TRUE, 0.1000, '3-7', 0.0300, 0.0800, 50);

-- 插入默认商品
INSERT OR IGNORE INTO products (name, image, price, description, status) VALUES
    ('iPhone 15 Pro', 'https://brush.sgp1.cdn.digitaloceanspaces.com/aceb6fa6d157c0acf30c4827c5ae490f.jpeg', 899900, '苹果iPhone 15 Pro 256GB', 1),
    ('MacBook Pro', 'https://brush.sgp1.cdn.digitaloceanspaces.com/aceb6fa6d157c0acf30c4827c5ae490f.jpeg', 1999900, 'MacBook Pro 14英寸 M3芯片', 1),
    ('AirPods Pro', 'https://brush.sgp1.cdn.digitaloceanspaces.com/aceb6fa6d157c0acf30c4827c5ae490f.jpeg', 24900, '苹果AirPods Pro 第二代', 1),
    ('iPad Air', 'https://brush.sgp1.cdn.digitaloceanspaces.com/aceb6fa6d157c0acf30c4827c5ae490f.jpeg', 59900, 'iPad Air 10.9英寸 WiFi版', 1),
    ('Apple Watch', 'https://brush.sgp1.cdn.digitaloceanspaces.com/aceb6fa6d157c0acf30c4827c5ae490f.jpeg', 39900, 'Apple Watch Series 9 GPS版', 1);

-- 插入系统配置（刷单相关）
INSERT OR IGNORE INTO system_configs (config_key, config_value, description) VALUES
    ('brush_enabled', 'true', '是否启用刷单功能'),
    ('brush_exchange_rate', '1.0000', 'USDT到内部币汇率'),
    ('brush_min_recharge', '10', '最小充值金额(USDT)'),
    ('brush_platform_wallet_mainnet', '', '主网平台收款钱包地址'),
    ('brush_platform_wallet_nile', '', '测试网平台收款钱包地址'),
    ('brush_auto_confirm_threshold', '100', '自动确认充值阈值(USDT)');

-- 注意：超级管理员需要通过以下步骤创建：
-- 1. 使用邀请码 'SUPER_ADMIN_INIT' 注册用户
-- 2. 手动执行 SQL 将该用户设置为超级管理员：
INSERT INTO admins (user_id, role, permissions, is_active) VALUES
    (1, 'super_admin', '["manage_users", "manage_admins", "view_all_wallets", "view_all_transactions", "system_config", "manage_products", "manage_brush_rules", "view_brush_stats"]', TRUE);
