# TRON钱包应用部署指南

## 🚀 快速开始

### 1. 本地开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd tron-wallet-app

# 2. 安装依赖
pnpm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 配置必要的环境变量

# 4. 初始化本地数据库
npx wrangler d1 execute wallet --local --command="
CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY AUTOINCREMENT, username TEXT UNIQUE NOT NULL, email TEXT UNIQUE, password_hash TEXT NOT NULL, name TEXT, avatar_url TEXT, invite_code_id INTEGER, invited_by INTEGER, mfa_secret TEXT, mfa_enabled BOOLEAN DEFAULT FALSE, is_active BOOLEAN DEFAULT TRUE, last_login_at DATETIME, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, updated_at DATETIME DEFAULT CURRENT_TIMESTAMP);
CREATE TABLE IF NOT EXISTS invite_codes (id INTEGER PRIMARY KEY AUTOINCREMENT, code TEXT UNIQUE NOT NULL, created_by INTEGER, used_by INTEGER, is_active BOOLEAN DEFAULT TRUE, max_uses INTEGER DEFAULT 1, used_count INTEGER DEFAULT 0, expires_at DATETIME, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, used_at DATETIME);
CREATE TABLE IF NOT EXISTS admins (id INTEGER PRIMARY KEY AUTOINCREMENT, user_id INTEGER NOT NULL UNIQUE, role TEXT NOT NULL DEFAULT 'agent', permissions TEXT, parent_admin_id INTEGER, is_active BOOLEAN DEFAULT TRUE, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, updated_at DATETIME DEFAULT CURRENT_TIMESTAMP);
CREATE TABLE IF NOT EXISTS wallets (id INTEGER PRIMARY KEY AUTOINCREMENT, user_id INTEGER NOT NULL, name TEXT NOT NULL, address TEXT NOT NULL, private_key_encrypted TEXT, wallet_type TEXT NOT NULL, is_default BOOLEAN DEFAULT FALSE, is_active BOOLEAN DEFAULT TRUE, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, updated_at DATETIME DEFAULT CURRENT_TIMESTAMP, UNIQUE(user_id, address));
CREATE TABLE IF NOT EXISTS transactions (id INTEGER PRIMARY KEY AUTOINCREMENT, user_id INTEGER NOT NULL, wallet_id INTEGER NOT NULL, tx_hash TEXT NOT NULL UNIQUE, from_address TEXT NOT NULL, to_address TEXT NOT NULL, amount DECIMAL(20, 8) NOT NULL, currency TEXT NOT NULL, tx_type TEXT NOT NULL, status TEXT DEFAULT 'pending', block_number INTEGER, gas_used INTEGER, gas_price DECIMAL(20, 8), network TEXT DEFAULT 'mainnet', created_at DATETIME DEFAULT CURRENT_TIMESTAMP, confirmed_at DATETIME);
CREATE TABLE IF NOT EXISTS system_configs (id INTEGER PRIMARY KEY AUTOINCREMENT, config_key TEXT UNIQUE NOT NULL, config_value TEXT NOT NULL, description TEXT, created_at DATETIME DEFAULT CURRENT_TIMESTAMP, updated_at DATETIME DEFAULT CURRENT_TIMESTAMP);
INSERT OR IGNORE INTO invite_codes (code, created_by, is_active, max_uses, used_count) VALUES ('SUPER_ADMIN_INIT', NULL, TRUE, 1, 0);
"

# 5. 启动开发服务器
pnpm run dev
```

### 2. 初始化超级管理员

```bash
# 1. 访问注册页面
http://localhost:3000/auth/register

# 2. 使用系统邀请码注册
邀请码: SUPER_ADMIN_INIT
用户名: admin
密码: 设置强密码

# 3. 设置为超级管理员
npx wrangler d1 execute wallet --local --command="
INSERT INTO admins (user_id, role, permissions, is_active) VALUES 
(1, 'super_admin', '[\"manage_users\", \"manage_admins\", \"view_all_wallets\", \"view_all_transactions\", \"system_config\"]', TRUE);
"
```

## 🌐 生产环境部署

### 1. Cloudflare配置

```bash
# 1. 登录Cloudflare
npx wrangler login

# 2. 创建生产数据库
npx wrangler d1 create tron-wallet-prod

# 3. 更新wrangler.jsonc中的database_id
# 将返回的database_id更新到wrangler.jsonc文件中

# 4. 初始化生产数据库
npx wrangler d1 execute tron-wallet-prod --file=./schema.sql

# 5. 部署应用
pnpm run deploy
```

### 2. 环境变量配置

在Cloudflare Workers中配置以下环境变量：

```bash
# 设置环境变量
npx wrangler secret put JWT_SECRET
# 输入强随机字符串

npx wrangler secret put TRON_API_KEY
# 输入TRON API密钥（可选）
```

### 3. 域名配置

1. 在Cloudflare Workers中配置自定义域名
2. 更新环境变量中的 `NEXT_PUBLIC_BASE_URL`

## 📋 功能测试清单

### 用户功能测试
- [ ] 用户注册（使用邀请码）
- [ ] 用户登录
- [ ] MFA设置和验证
- [ ] 创建钱包
- [ ] 导入钱包
- [ ] 查看余额
- [ ] 转账功能
- [ ] 收款功能
- [ ] 交易记录

### 管理功能测试
- [ ] 管理员登录
- [ ] 用户管理
- [ ] 创建代理
- [ ] 邀请码管理
- [ ] 权限控制

## 🔧 故障排除

### 常见问题

#### Q: 数据库连接失败
A: 确保wrangler.jsonc中的database_id正确，并且已经创建了D1数据库

#### Q: 认证失败
A: 检查JWT_SECRET环境变量是否正确设置

#### Q: TRON网络连接失败
A: 检查网络连接，确保可以访问TRON网络API

#### Q: 字体加载失败
A: 这是网络问题，不影响应用功能，可以忽略

### 日志查看

```bash
# 查看Cloudflare Workers日志
npx wrangler tail

# 查看本地数据库
npx wrangler d1 execute wallet --local --command="SELECT * FROM users LIMIT 10"
```

## 🔒 安全建议

1. **强密码策略**: 确保JWT_SECRET使用强随机字符串
2. **HTTPS**: 生产环境必须使用HTTPS
3. **定期备份**: 定期备份D1数据库
4. **监控**: 设置错误监控和日志记录
5. **权限审查**: 定期审查管理员权限

## 📈 性能优化

1. **缓存策略**: 配置适当的缓存策略
2. **CDN**: 利用Cloudflare CDN加速静态资源
3. **数据库优化**: 定期清理过期数据
4. **监控**: 设置性能监控

## 🔄 更新和维护

### 更新应用
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖
pnpm install

# 3. 运行数据库迁移（如有）
npx wrangler d1 execute wallet --file=./migrations/xxx.sql

# 4. 部署更新
pnpm run deploy
```

### 数据备份
```bash
# 备份生产数据库
npx wrangler d1 export tron-wallet-prod --output=backup-$(date +%Y%m%d).sql
```

## 📞 技术支持

如遇到问题，请检查：
1. 环境变量配置
2. 数据库连接
3. 网络连接
4. 日志输出

更多详细信息请参考：
- [README.md](./README.md) - 项目概述
- [USAGE_GUIDE.md](./USAGE_GUIDE.md) - 使用指南
- [DEVELOPMENT_SETUP.md](./DEVELOPMENT_SETUP.md) - 开发环境配置
