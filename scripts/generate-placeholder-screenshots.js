#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 创建简单的SVG占位图片
function createPlaceholderSVG(width, height, title, type) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)"/>
  <text x="50%" y="40%" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">
    TRON钱包
  </text>
  <text x="50%" y="50%" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16">
    ${title}
  </text>
  <text x="50%" y="60%" text-anchor="middle" fill="rgba(255,255,255,0.8)" font-family="Arial, sans-serif" font-size="14">
    ${type} - ${width}×${height}
  </text>
</svg>`;
}

// 确保目录存在
const screenshotsDir = path.join(__dirname, '../public/screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

// 生成占位图片
const screenshots = [
  { name: 'mobile-1.png', width: 390, height: 844, title: '移动端界面', type: 'Mobile' },
  { name: 'mobile-2.png', width: 390, height: 844, title: '刷单功能', type: 'Mobile' },
  { name: 'desktop-1.png', width: 1280, height: 800, title: '桌面端界面', type: 'Desktop' },
  { name: 'desktop-2.png', width: 1280, height: 800, title: '管理后台', type: 'Desktop' }
];

screenshots.forEach(({ name, width, height, title, type }) => {
  const svgContent = createPlaceholderSVG(width, height, title, type);
  const svgPath = path.join(screenshotsDir, name.replace('.png', '.svg'));
  
  fs.writeFileSync(svgPath, svgContent);
  console.log(`Created placeholder: ${svgPath}`);
});

console.log('\\nPlaceholder screenshots created successfully!');
console.log('Note: These are SVG placeholders. For production, replace with actual PNG screenshots.');
