#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要生成的图标尺寸
const iconSizes = [
  16, 32, 57, 60, 72, 76, 96, 114, 120, 128, 144, 152, 180, 192, 384, 512
];

// 图标目录
const iconsDir = path.join(__dirname, '../public/icons');
const svgPath = path.join(iconsDir, 'logo.svg');

// 检查是否安装了sharp
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.error('❌ Sharp未安装。请运行: npm install sharp --save-dev');
  process.exit(1);
}

// 检查SVG文件是否存在
if (!fs.existsSync(svgPath)) {
  console.error('❌ SVG文件不存在:', svgPath);
  process.exit(1);
}

// 生成图标函数
async function generateIcon(size) {
  try {
    const outputPath = path.join(iconsDir, `icon-${size}x${size}.png`);
    
    await sharp(svgPath)
      .resize(size, size, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 } // 透明背景
      })
      .png()
      .toFile(outputPath);
    
    console.log(`✅ 生成成功: icon-${size}x${size}.png`);
  } catch (error) {
    console.error(`❌ 生成失败 ${size}x${size}:`, error.message);
  }
}

// 生成所有图标
async function generateAllIcons() {
  console.log('🚀 开始生成PWA图标...\n');
  
  // 确保图标目录存在
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }
  
  // 并行生成所有尺寸的图标
  const promises = iconSizes.map(size => generateIcon(size));
  
  try {
    await Promise.all(promises);
    console.log('\n🎉 所有图标生成完成！');
    
    // 生成图标清单
    console.log('\n📋 生成的图标文件:');
    iconSizes.forEach(size => {
      console.log(`   - icon-${size}x${size}.png`);
    });
    
    // 检查文件是否真的生成了
    console.log('\n🔍 验证文件...');
    let allGenerated = true;
    for (const size of iconSizes) {
      const filePath = path.join(iconsDir, `icon-${size}x${size}.png`);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ icon-${size}x${size}.png (${Math.round(stats.size / 1024)}KB)`);
      } else {
        console.log(`❌ icon-${size}x${size}.png 未生成`);
        allGenerated = false;
      }
    }
    
    if (allGenerated) {
      console.log('\n🎊 所有图标验证通过！');
      console.log('\n💡 接下来您可以:');
      console.log('   1. 检查 public/icons/ 目录中的图标');
      console.log('   2. 更新 manifest.json 中的图标路径');
      console.log('   3. 测试PWA安装功能');
    }
    
  } catch (error) {
    console.error('❌ 生成图标时出错:', error);
  }
}

// 主函数
async function main() {
  try {
    await generateAllIcons();
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { generateAllIcons, generateIcon };
