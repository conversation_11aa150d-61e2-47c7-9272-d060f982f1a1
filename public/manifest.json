{"name": "TRON钱包", "short_name": "TRON钱包", "description": "安全、便捷的TRON区块链钱包应用", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "zh-CN", "categories": ["finance", "utilities"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "钱包", "short_name": "钱包", "description": "查看和管理您的钱包", "url": "/wallet", "icons": [{"src": "/icons/wallet-96x96.png", "sizes": "96x96"}]}, {"name": "转账", "short_name": "转账", "description": "发送TRON和USDT", "url": "/wallet?action=transfer", "icons": [{"src": "/icons/transfer-96x96.png", "sizes": "96x96"}]}, {"name": "收款", "short_name": "收款", "description": "接收TRON和USDT", "url": "/wallet?action=receive", "icons": [{"src": "/icons/receive-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/mobile-1.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "钱包主界面"}, {"src": "/screenshots/mobile-2.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "转账界面"}, {"src": "/screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "桌面端钱包界面"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "protocol_handlers": [{"protocol": "tron", "url": "/wallet?address=%s"}]}