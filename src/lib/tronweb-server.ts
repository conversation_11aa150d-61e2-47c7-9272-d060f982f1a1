// 服务端TronWeb实例 - 用于API路由中的区块链操作
const { TronWeb } = require('tronweb');

// TRON网络配置
const TRON_NETWORKS = {
  mainnet: {
    fullHost: 'https://api.trongrid.io',
    solidityNode: 'https://api.trongrid.io',
    eventServer: 'https://api.trongrid.io'
  },
  nile: {
    fullHost: 'https://api.nileex.io',
    solidityNode: 'https://api.nileex.io',
    eventServer: 'https://api.nileex.io'
  },
  shasta: {
    fullHost: 'https://api.shasta.trongrid.io',
    solidityNode: 'https://api.shasta.trongrid.io',
    eventServer: 'https://api.shasta.trongrid.io'
  }
};

// USDT合约地址
const USDT_CONTRACTS = {
  mainnet: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
  nile: 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf',
  shasta: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'
};

// 获取网络对应的API密钥
function getApiKeyForNetwork(network: 'mainnet' | 'nile' | 'shasta'): string | undefined {
  switch (network) {
    case 'mainnet':
      // 主网需要API密钥
      return process.env.TRON_API_KEY || process.env.NEXT_PUBLIC_TRON_API_KEY || '3f67c2cc-9119-468b-a336-f3f95b7bfec3';
    case 'nile':
      // Nile测试网可能不需要API密钥，或使用专门的测试网密钥
      return process.env.TRON_NILE_API_KEY || undefined;
    case 'shasta':
      // Shasta测试网可能不需要API密钥，或使用专门的测试网密钥
      return process.env.TRON_SHASTA_API_KEY || undefined;
    default:
      return undefined;
  }
}

// 创建TronWeb实例
export function createTronWebInstance(network: 'mainnet' | 'nile' | 'shasta' = 'mainnet') {
  const networkConfig = TRON_NETWORKS[network];
  const apiKey = getApiKeyForNetwork(network);

  console.log(`创建TronWeb实例 - 网络: ${network}`);
  console.log(`API密钥: ${apiKey ? apiKey.substring(0, 8) + '...' : '未设置'}`);
  console.log(`网络配置: ${networkConfig.fullHost}`);

  // 根据是否有API密钥来配置headers
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };

  if (apiKey) {
    headers['TRON-PRO-API-KEY'] = apiKey;
  }

  const tronWeb = new TronWeb({
    fullHost: networkConfig.fullHost,
    solidityNode: networkConfig.solidityNode,
    eventServer: networkConfig.eventServer,
    headers: headers,
    privateKey: '01' // 临时私钥，实际使用时会替换
  });

  console.log(`TronWeb实例创建完成 - Headers: ${JSON.stringify(Object.keys(headers))}`);

  return tronWeb;
}

// 获取账户余额
export async function getAccountBalance(address: string, currency: 'TRX' | 'USDT', network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'): Promise<number> {
  const tronWeb = createTronWebInstance(network);
  
  try {
    if (currency === 'TRX') {
      const balance = await tronWeb.trx.getBalance(address);
      return tronWeb.fromSun(balance);
    } else {
      // USDT TRC20 - 使用 HTTP API 替代合约调用
      const contractAddress = USDT_CONTRACTS[network];
      console.log(`获取USDT余额 - 网络: ${network}, 合约地址: ${contractAddress}`);

      if (!contractAddress) {
        throw new Error(`未找到 ${network} 网络的 USDT 合约地址`);
      }

      // 使用 HTTP API 调用合约方法
      const apiUrl = network === 'mainnet'
        ? 'https://api.trongrid.io'
        : 'https://api.nileex.io';

      // 构造 balanceOf 函数调用参数
      const functionSelector = 'balanceOf(address)';
      const parameter = tronWeb.address.toHex(address).substring(2).padStart(64, '0');

      console.log(`调用合约参数:`, {
        owner_address: tronWeb.address.toHex(address),
        contract_address: tronWeb.address.toHex(contractAddress),
        function_selector: functionSelector,
        parameter: parameter
      });

      const response = await fetch(`${apiUrl}/wallet/triggerconstantcontract`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(process.env.TRON_API_KEY ? { 'TRON-PRO-API-KEY': process.env.TRON_API_KEY } : {})
        },
        body: JSON.stringify({
          owner_address: tronWeb.address.toHex(address),
          contract_address: tronWeb.address.toHex(contractAddress),
          function_selector: functionSelector,
          parameter: parameter
        })
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
      }

      const data: any = await response.json();
      console.log(`API响应数据:`, data);

      if (!data.result || !data.result.result) {
        throw new Error(`合约调用失败: ${data.result?.message || '未知错误'}`);
      }

      if (!data.constant_result || !data.constant_result[0]) {
        console.log(`余额为0或无数据`);
        return 0;
      }

      // 解析十六进制余额
      const hexBalance = data.constant_result[0];
      console.log(`十六进制余额: ${hexBalance}`);

      const balanceNumber = parseInt(hexBalance, 16);
      console.log(`解析后的余额: ${balanceNumber}`);

      // USDT有6位小数
      const result = balanceNumber / Math.pow(10, 6);
      console.log(`USDT计算: ${balanceNumber} / 1000000 = ${result}`);
      return result;
    }
  } catch (error: any) {
    console.error(`获取${currency}余额失败:`, error);
    throw new Error(`获取${currency}余额失败: ${error.message}`);
  }
}

// 验证TRON地址
export function isValidTronAddress(address: string): boolean {
  try {
    const tronWeb = createTronWebInstance();
    return tronWeb.isAddress(address);
  } catch (error) {
    return false;
  }
}

// 检查账户是否已激活
export async function isAccountActivated(address: string, network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'): Promise<boolean> {
  const tronWeb = createTronWebInstance(network);

  try {
    const account = await tronWeb.trx.getAccount(address);
    // 如果账户存在且有余额或其他属性，说明已激活
    return account && (account.balance !== undefined || account.create_time !== undefined);
  } catch (error: any) {
    console.error('检查账户激活状态失败:', error);
    // 如果是 "account does not exist" 错误，说明账户未激活
    if (error.message && error.message.includes('does not exist')) {
      return false;
    }
    throw error;
  }
}

// 转换地址格式
export function convertAddress(address: string, toHex: boolean = false): string {
  try {
    const tronWeb = createTronWebInstance();
    if (toHex) {
      return tronWeb.address.toHex(address);
    } else {
      return tronWeb.address.fromHex(address);
    }
  } catch (error) {
    throw new Error(`地址转换失败: ${error}`);
  }
}

// 发送TRX
export async function sendTRX(
  fromPrivateKey: string, 
  toAddress: string, 
  amount: number, 
  network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'
): Promise<{ success: boolean; txHash?: string; error?: string }> {
  const tronWeb = createTronWebInstance(network);
  
  try {
    // 设置私钥
    tronWeb.setPrivateKey(fromPrivateKey);
    const fromAddress = tronWeb.address.fromPrivateKey(fromPrivateKey);
    
    // 构建交易
    const transaction = await tronWeb.transactionBuilder.sendTrx(
      toAddress,
      tronWeb.toSun(amount),
      fromAddress
    );
    
    // 签名交易
    const signedTransaction = await tronWeb.trx.sign(transaction);
    
    // 广播交易
    const result = await tronWeb.trx.sendRawTransaction(signedTransaction);
    
    if (result.result) {
      return { success: true, txHash: result.txid };
    } else {
      return { success: false, error: '交易失败' };
    }
  } catch (error: any) {
    console.error('TRX转账失败:', error);
    return { success: false, error: error.message };
  }
}

// 发送USDT
export async function sendUSDT(
  fromPrivateKey: string, 
  toAddress: string, 
  amount: number, 
  network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'
): Promise<{ success: boolean; txHash?: string; error?: string }> {
  const tronWeb = createTronWebInstance(network);
  
  try {
    // 设置私钥
    tronWeb.setPrivateKey(fromPrivateKey);
    const fromAddress = tronWeb.address.fromPrivateKey(fromPrivateKey);
    
    // 获取USDT合约
    const contractAddress = USDT_CONTRACTS[network];
    const contract = await tronWeb.contract().at(contractAddress);
    
    // USDT有6位小数
    const amountInSmallestUnit = Math.floor(amount * Math.pow(10, 6));
    
    // 调用transfer方法
    const result = await contract.transfer(toAddress, amountInSmallestUnit).send({
      from: fromAddress
    });
    
    if (result) {
      const txHash = typeof result === 'string' ? result : result.txid || result.transaction?.txID;
      return { success: true, txHash };
    } else {
      return { success: false, error: 'USDT转账失败' };
    }
  } catch (error: any) {
    console.error('USDT转账失败:', error);
    return { success: false, error: error.message };
  }
}

// 查询交易状态
export async function getTransactionInfo(txHash: string, network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'): Promise<any> {
  const tronWeb = createTronWebInstance(network);
  
  try {
    const txInfo = await tronWeb.trx.getTransactionInfo(txHash);
    return txInfo;
  } catch (error: any) {
    console.error('查询交易信息失败:', error);
    throw new Error(`查询交易信息失败: ${error.message}`);
  }
}

export { TRON_NETWORKS, USDT_CONTRACTS };
