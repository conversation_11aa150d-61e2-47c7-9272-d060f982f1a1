// 数据库工具函数 - TRON钱包应用
export interface User {
  id: number;
  username: string;
  email?: string;
  password_hash: string;
  name?: string;
  avatar_url?: string;
  invite_code_id?: number;
  invited_by?: number;
  mfa_secret?: string;
  mfa_enabled: boolean;
  is_active: boolean;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface InviteCode {
  id: number;
  code: string;
  created_by?: number;
  used_by?: number;
  is_active: boolean;
  max_uses: number;
  used_count: number;
  expires_at?: string;
  created_at: string;
  used_at?: string;
}

export interface Admin {
  id: number;
  user_id: number;
  role: 'super_admin' | 'agent';
  permissions?: string; // JSON string
  parent_admin_id?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Wallet {
  id: number;
  user_id: number;
  name: string;
  address: string;
  private_key_encrypted?: string;
  wallet_type: 'created' | 'imported';
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Transaction {
  id: number;
  user_id: number;
  wallet_id: number;
  tx_hash: string;
  from_address: string;
  to_address: string;
  amount: number;
  currency: 'TRX' | 'USDT';
  tx_type: 'send' | 'receive';
  status: 'pending' | 'confirmed' | 'failed';
  block_number?: number;
  gas_used?: number;
  gas_price?: number;
  network: 'mainnet' | 'nile' | 'shasta';
  created_at: string;
  confirmed_at?: string;
}

export interface SystemConfig {
  id: number;
  config_key: string;
  config_value: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// 服务接口
export interface Service {
  id: number;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 服务套餐接口
export interface ServicePlan {
  id: number;
  service_id: number;
  name: string;
  slug: string;
  duration_days: number;
  price_usdt: number;
  description?: string;
  features?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  id: number;
  user_id?: number;
  guest_session_id?: string;
  service_id: number;
  plan_id: number;
  status: 'pending' | 'active' | 'expired' | 'cancelled';
  start_date?: string;
  end_date?: string;
  price_paid: number;
  currency: string;
  admin_notes?: string; // 管理员备注
  delivery_status: 'pending' | 'delivered' | 'cancelled'; // 发货状态
  delivered_at?: string; // 发货时间
  delivered_by?: number; // 发货管理员ID
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: number;
  subscription_id: number;
  payment_provider: string;
  payment_id?: string;
  amount: number;
  unique_amount?: number; // 带唯一小数的金额
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_url?: string;
  callback_data?: string;
  network?: string; // 支付网络: 'mainnet', 'nile'
  wallet_address?: string; // 创建时使用的钱包地址
  paid_at?: string;
  created_at: string;
  updated_at: string;
}

// 钱包地址轮换配置
export interface WalletRotation {
  id: number;
  network: 'mainnet' | 'nile'; // 网络类型
  address: string; // 钱包地址
  is_active: boolean; // 是否激活
  valid_from: string; // 有效期开始时间
  valid_until?: string; // 有效期结束时间
  priority: number; // 优先级，数字越小优先级越高
  description?: string; // 描述
  created_at: string;
  updated_at: string;
}



// 站内信接口
export interface Notification {
  id: number;
  user_id?: number;
  guest_session_id?: string;
  title: string;
  content: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'delivery';
  related_subscription_id?: number;
  is_read: boolean;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

// 发货日志接口
export interface DeliveryLog {
  id: number;
  subscription_id: number;
  admin_id: number;
  action: 'note_added' | 'delivered' | 'cancelled';
  old_value?: string;
  new_value?: string;
  notes?: string;
  created_at: string;
}

// 系统配置接口
export interface SystemConfig {
  id: number;
  config_key: string;
  config_value: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// Webhook类型枚举
export const WEBHOOK_TYPES = {
  CUSTOM: 'custom',
  TELEGRAM_BOT: 'telegram_bot',
  DINGTALK_BOT: 'dingtalk_bot',
  WECHAT_WORK_BOT: 'wechat_work_bot',
  FEISHU_BOT: 'feishu_bot',
} as const;

export type WebhookType = typeof WEBHOOK_TYPES[keyof typeof WEBHOOK_TYPES];

// Webhook配置接口
export interface WebhookConfig {
  id: number;
  name: string;
  webhook_type: WebhookType;
  webhook_url: string;
  events: string; // JSON string
  is_active: boolean;
  secret_token?: string;
  timeout_seconds: number;
  retry_count: number;
  headers?: string; // JSON string
  bot_config?: string; // JSON string - 机器人特定配置
  created_at: string;
  updated_at: string;
}

// Webhook日志接口
export interface WebhookLog {
  id: number;
  webhook_config_id: number;
  event_type: string;
  payload: string;
  response_status?: number;
  response_body?: string;
  error_message?: string;
  attempt_number: number;
  success: boolean;
  created_at: string;
}

// ===== 刷单系统相关接口 =====

// 商品接口
export interface Product {
  id: number;
  name: string;
  image?: string;
  price: number; // 单位：分
  description?: string;
  status: number; // 1: 正常, 0: 下架
  created_at: string;
  updated_at: string;
}

// 用户余额接口
export interface UserBalance {
  id: number;
  user_id: number;
  balance: number; // 单位：分
  frozen_balance: number; // 单位：分
  total_recharged: number; // 单位：分
  total_spent: number; // 单位：分
  created_at: string;
  updated_at: string;
}

// 充值记录接口
export interface RechargeRecord {
  id: number;
  user_id: number;
  tx_hash: string;
  from_address: string;
  to_address: string;
  usdt_amount: number;
  internal_amount: number; // 单位：分
  exchange_rate: number;
  status: number; // 0: 待确认, 1: 已确认, 2: 失败
  network: string;
  block_number?: number;
  confirmed_at?: string;
  created_at: string;
}

// 刷单订单接口
export interface BrushOrder {
  id: number;
  user_id: number;
  product_id: number;
  order_no: string;
  product_name: string;
  product_image?: string;
  product_price: number; // 单位：分
  quantity: number;
  total_amount: number; // 单位：分
  commission_rate: number;
  commission_amount: number; // 单位：分
  status: number; // 0: 待付款, 1: 已付款, 2: 已完成, 3: 已取消, 4: 爆单
  is_burst: boolean;
  burst_reason?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

// 刷单规则接口
export interface BrushRule {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  burst_probability: number;
  burst_order_range: string;
  min_commission_rate: number;
  max_commission_rate: number;
  daily_order_limit: number;
  min_order_interval: number;
  max_order_interval: number;
  config_json?: string;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

// 用户刷单统计接口
export interface UserBrushStats {
  id: number;
  user_id: number;
  total_orders: number;
  completed_orders: number;
  burst_orders: number;
  total_spent: number; // 单位：分
  total_commission: number; // 单位：分
  today_orders: number;
  today_spent: number; // 单位：分
  today_commission: number; // 单位：分
  last_order_at?: string;
  stats_date: string;
  created_at: string;
  updated_at: string;
}

// 余额变动记录接口
export interface BalanceLog {
  id: number;
  user_id: number;
  type: number; // 1: 充值, 2: 消费, 3: 佣金收入, 4: 退款, 5: 冻结, 6: 解冻
  amount: number; // 单位：分
  balance_before: number;
  balance_after: number;
  related_id?: number;
  related_type?: string;
  description?: string;
  created_at: string;
}

// 平台钱包配置接口
export interface PlatformWallet {
  id: number;
  name: string;
  address: string;
  network: string;
  currency: string;
  is_active: boolean;
  is_default: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
}

export class DatabaseService {
  constructor(private db: D1Database) {}

  // 用户相关操作
  async getAllUsers(): Promise<User[]> {
    const { results } = await this.db.prepare("SELECT * FROM users ORDER BY created_at DESC").all();
    return results as unknown as User[];
  }

  async getUserById(id: number): Promise<User | null> {
    const { results } = await this.db.prepare("SELECT * FROM users WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as User : null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const { results } = await this.db.prepare(
      "SELECT * FROM users WHERE email = ? LIMIT 1"
    ).bind(email).all();
    return results.length > 0 ? results[0] as unknown as User : null;
  }

  async createUser(data: Partial<User> & {
    oauth_provider?: string;
    oauth_id?: string;
    is_guest?: boolean;
    guest_session_id?: string;
  }): Promise<User> {
    const { email, name, oauth_provider, oauth_id, is_guest, guest_session_id } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO users (email, name, oauth_provider, oauth_id, is_guest, guest_session_id) VALUES (?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(email || null, name || null, oauth_provider || null, oauth_id || null, is_guest || false, guest_session_id || null).all();
    return results[0] as unknown as User;
  }

  async createGuestUser(sessionId: string): Promise<User> {
    const { results } = await this.db.prepare(
      "INSERT INTO users (is_guest, guest_session_id) VALUES (?, ?) RETURNING *"
    ).bind(true, sessionId).all();
    return results[0] as unknown as User;
  }

  async getUserByOAuthId(provider: string, oauthId: string): Promise<User | null> {
    const { results } = await this.db.prepare(
      "SELECT * FROM users WHERE oauth_provider = ? AND oauth_id = ? LIMIT 1"
    ).bind(provider, oauthId).all();
    return results.length > 0 ? results[0] as unknown as User : null;
  }

  async updateUser(id: number, data: Partial<User>): Promise<User | null> {
    const { email, name, avatar_url } = data;
    const { results } = await this.db.prepare(
      "UPDATE users SET email = ?, name = ?, avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? RETURNING *"
    ).bind(email || null, name || null, avatar_url || null, id).all();
    return results.length > 0 ? results[0] as unknown as User : null;
  }

  // 服务相关操作
  async getAllServices(): Promise<Service[]> {
    const { results } = await this.db.prepare("SELECT * FROM services WHERE is_active = ? ORDER BY created_at DESC").bind(true).all();
    return results as unknown as Service[];
  }

  // 管理员获取所有服务（包括已关闭的）
  async getAllServicesForAdmin(): Promise<Service[]> {
    const { results } = await this.db.prepare("SELECT * FROM services ORDER BY created_at DESC").all();
    return results as unknown as Service[];
  }

  async getServiceById(id: number): Promise<Service | null> {
    const { results } = await this.db.prepare("SELECT * FROM services WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as Service : null;
  }

  async getServiceBySlug(slug: string): Promise<Service | null> {
    const { results } = await this.db.prepare("SELECT * FROM services WHERE slug = ?").bind(slug).all();
    return results.length > 0 ? results[0] as unknown as Service : null;
  }

  // 服务套餐相关操作
  async getServicePlans(serviceId: number): Promise<ServicePlan[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM service_plans WHERE service_id = ? AND is_active = ? ORDER BY price_usdt DESC"
    ).bind(serviceId, true).all();
    return results as unknown as ServicePlan[];
  }

  // 管理员获取所有套餐（包括已关闭的）
  async getAllServicePlansForAdmin(): Promise<(ServicePlan & { service_name: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT sp.*, s.name as service_name
      FROM service_plans sp
      LEFT JOIN services s ON sp.service_id = s.id
      ORDER BY s.name, sp.price_usdt DESC
    `).all();
    return results as unknown as (ServicePlan & { service_name: string })[];
  }

  // 根据服务ID获取所有套餐（管理员用）
  async getServicePlansForAdmin(serviceId: number): Promise<ServicePlan[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM service_plans WHERE service_id = ? ORDER BY price_usdt DESC"
    ).bind(serviceId).all();
    return results as unknown as ServicePlan[];
  }

  // 根据ID获取套餐
  async getServicePlanById(id: number): Promise<ServicePlan | null> {
    const { results } = await this.db.prepare(
      "SELECT * FROM service_plans WHERE id = ?"
    ).bind(id).all();
    return results.length > 0 ? results[0] as unknown as ServicePlan : null;
  }

  // 创建套餐
  async createServicePlan(planData: {
    service_id: number;
    name: string;
    slug: string;
    duration_days: number;
    price_usdt: number;
    description?: string;
    features?: string[];
    is_active: boolean;
  }): Promise<number> {
    const result = await this.db.prepare(`
      INSERT INTO service_plans (service_id, name, slug, duration_days, price_usdt, description, features, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      planData.service_id,
      planData.name,
      planData.slug,
      planData.duration_days,
      planData.price_usdt,
      planData.description || null,
      JSON.stringify(planData.features || []),
      planData.is_active ? 1 : 0 // 转换布尔值为数字
    ).run();

    return (result as any).meta.last_row_id;
  }

  // 更新套餐
  async updateServicePlan(id: number, planData: {
    name: string;
    slug: string;
    duration_days: number;
    price_usdt: number;
    description?: string;
    features?: string[];
    is_active: boolean;
  }): Promise<boolean> {
    const result = await this.db.prepare(`
      UPDATE service_plans
      SET name = ?, slug = ?, duration_days = ?, price_usdt = ?, description = ?, features = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      planData.name,
      planData.slug,
      planData.duration_days,
      planData.price_usdt,
      planData.description || null,
      JSON.stringify(planData.features || []),
      planData.is_active ? 1 : 0, // 转换布尔值为数字
      id
    ).run();

    return (result as any).meta?.changes > 0;
  }

  // 删除套餐
  async deleteServicePlan(id: number): Promise<boolean> {
    const result = await this.db.prepare(
      "DELETE FROM service_plans WHERE id = ?"
    ).bind(id).run();

    return (result as any).meta?.changes > 0;
  }



  async getAllServicePlans(): Promise<(ServicePlan & { service_name: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT sp.*, s.name as service_name
      FROM service_plans sp
      JOIN services s ON sp.service_id = s.id
      WHERE sp.is_active = ? AND s.is_active = ?
      ORDER BY s.name, sp.price_usdt DESC
    `).bind(true, true).all();
    return results as unknown as (ServicePlan & { service_name: string })[];
  }

  // 订阅相关操作
  async createSubscription(data: {
    userId?: number;
    guestSessionId?: string;
    serviceId: number;
    planId: number;
    pricePaid: number;
    currency?: string;
  }): Promise<Subscription> {
    const { userId, guestSessionId, serviceId, planId, pricePaid, currency = 'USDT' } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO subscriptions (user_id, guest_session_id, service_id, plan_id, price_paid, currency) VALUES (?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(userId || null, guestSessionId || null, serviceId, planId, pricePaid, currency).all();
    return results[0] as unknown as Subscription;
  }

  async getSubscriptionById(id: number): Promise<Subscription | null> {
    const { results } = await this.db.prepare("SELECT * FROM subscriptions WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as Subscription : null;
  }

  async getUserSubscriptions(userId: number): Promise<(Subscription & { service_name: string; plan_name: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT s.*, srv.name as service_name, sp.name as plan_name
      FROM subscriptions s
      JOIN services srv ON s.service_id = srv.id
      JOIN service_plans sp ON s.plan_id = sp.id
      WHERE s.user_id = ?
      ORDER BY s.created_at DESC
    `).bind(userId).all();
    return results as unknown as (Subscription & { service_name: string; plan_name: string })[];
  }

  async getGuestSubscriptions(sessionId: string): Promise<(Subscription & { service_name: string; plan_name: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT s.*, srv.name as service_name, sp.name as plan_name
      FROM subscriptions s
      JOIN services srv ON s.service_id = srv.id
      JOIN service_plans sp ON s.plan_id = sp.id
      WHERE s.guest_session_id = ?
      ORDER BY s.created_at DESC
    `).bind(sessionId).all();
    return results as unknown as (Subscription & { service_name: string; plan_name: string })[];
  }

  async updateSubscriptionStatus(id: number, status: string, startDate?: string, endDate?: string): Promise<Subscription | null> {
    const { results } = await this.db.prepare(
      "UPDATE subscriptions SET status = ?, start_date = ?, end_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? RETURNING *"
    ).bind(status, startDate || null, endDate || null, id).all();
    return results.length > 0 ? results[0] as unknown as Subscription : null;
  }

  // 支付相关操作
  async createPayment(data: {
    subscriptionId: number;
    amount: number;
    currency: string;
    paymentProvider?: string;
    network?: string;
    walletAddress?: string;
  }): Promise<Payment> {
    const { subscriptionId, amount, currency, paymentProvider = 'cryptomus', network = 'mainnet', walletAddress } = data;

    // 生成唯一金额
    const uniqueAmount = await this.generateUniqueAmount(amount);

    const { results } = await this.db.prepare(
      "INSERT INTO payments (subscription_id, amount, unique_amount, currency, payment_provider, network, wallet_address) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(subscriptionId, amount, uniqueAmount, currency, paymentProvider, network, walletAddress).all();
    return results[0] as unknown as Payment;
  }

  async updatePayment(id: number, data: {
    paymentId?: string;
    status?: string;
    paymentUrl?: string;
    callbackData?: string;
    paidAt?: string;
    network?: string;
  }): Promise<Payment | null> {
    const { paymentId, status, paymentUrl, callbackData, paidAt, network } = data;
    const { results } = await this.db.prepare(
      "UPDATE payments SET payment_id = ?, status = ?, payment_url = ?, callback_data = ?, paid_at = ?, network = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? RETURNING *"
    ).bind(paymentId || null, status || null, paymentUrl || null, callbackData || null, paidAt || null, network || null, id).all();
    return results.length > 0 ? results[0] as unknown as Payment : null;
  }

  async getPaymentById(id: number): Promise<Payment | null> {
    const { results } = await this.db.prepare("SELECT * FROM payments WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as Payment : null;
  }

  async getPaymentByTxHash(txHash: string): Promise<Payment | null> {
    const { results } = await this.db.prepare("SELECT * FROM payments WHERE payment_id = ?").bind(txHash).all();
    return results.length > 0 ? results[0] as unknown as Payment : null;
  }

  // 序列管理方法
  async getNextSequenceValue(): Promise<number> {
    // 原子性地获取并递增序列值
    const { results } = await this.db.prepare(
      "UPDATE payment_sequences SET sequence_value = sequence_value + 1 WHERE id = 1 RETURNING sequence_value"
    ).all();

    if (results.length === 0) {
      // 如果序列不存在，创建它
      await this.db.prepare("INSERT OR REPLACE INTO payment_sequences (id, sequence_value) VALUES (1, 1)").run();
      return 1;
    }

    return (results[0] as any).sequence_value;
  }

  // 生成唯一金额
  async generateUniqueAmount(baseAmount: number): Promise<number> {
    const sequence = await this.getNextSequenceValue();
    // 使用求余确保小数部分在合理范围内 (0.001-0.999)
    const decimal = (sequence % 999) + 1; // 1-999
    const uniqueAmount = baseAmount + (decimal / 1000); // 添加0.001-0.999的小数
    return Math.round(uniqueAmount * 1000) / 1000; // 保留3位小数
  }

  async getPaymentsBySubscriptionId(subscriptionId: number): Promise<Payment[]> {
    const { results } = await this.db.prepare("SELECT * FROM payments WHERE subscription_id = ? ORDER BY created_at DESC").bind(subscriptionId).all();
    return results as unknown as Payment[];
  }

  // 钱包轮换相关操作
  async createWalletRotation(data: {
    network: 'mainnet' | 'nile';
    address: string;
    validFrom: string;
    validUntil?: string;
    priority?: number;
    description?: string;
  }): Promise<WalletRotation> {
    const { network, address, validFrom, validUntil, priority = 0, description } = data;

    const { results } = await this.db.prepare(`
      INSERT INTO wallet_rotations (network, address, valid_from, valid_until, priority, description, is_active)
      VALUES (?, ?, ?, ?, ?, ?, 1) RETURNING *
    `).bind(network, address, validFrom, validUntil, priority, description).all();

    return results[0] as unknown as WalletRotation;
  }

  async getActiveWalletAddress(network: 'mainnet' | 'nile'): Promise<string | null> {
    const now = new Date().toISOString();

    // 查找当前有效的钱包地址，按优先级排序
    const { results } = await this.db.prepare(`
      SELECT address FROM wallet_rotations
      WHERE network = ?
        AND is_active = 1
        AND valid_from <= ?
        AND (valid_until IS NULL OR valid_until > ?)
      ORDER BY priority ASC, created_at ASC
      LIMIT 1
    `).bind(network, now, now).all();

    if (results.length > 0) {
      return (results[0] as any).address;
    }

    // 如果没有轮换地址，返回null（不再回退到系统配置）
    return null;
  }

  async getWalletRotations(network?: 'mainnet' | 'nile'): Promise<WalletRotation[]> {
    let query = "SELECT * FROM wallet_rotations";
    let params: any[] = [];

    if (network) {
      query += " WHERE network = ?";
      params.push(network);
    }

    query += " ORDER BY network, priority ASC, created_at DESC";

    const { results } = await this.db.prepare(query).bind(...params).all();
    return results as unknown as WalletRotation[];
  }

  async updateWalletRotation(id: number, data: {
    address?: string;
    isActive?: boolean;
    validFrom?: string;
    validUntil?: string;
    priority?: number;
    description?: string;
  }): Promise<void> {
    const updates: string[] = [];
    const params: any[] = [];

    if (data.address !== undefined) {
      updates.push("address = ?");
      params.push(data.address);
    }
    if (data.isActive !== undefined) {
      updates.push("is_active = ?");
      params.push(data.isActive ? 1 : 0);
    }
    if (data.validFrom !== undefined) {
      updates.push("valid_from = ?");
      params.push(data.validFrom);
    }
    if (data.validUntil !== undefined) {
      updates.push("valid_until = ?");
      params.push(data.validUntil);
    }
    if (data.priority !== undefined) {
      updates.push("priority = ?");
      params.push(data.priority);
    }
    if (data.description !== undefined) {
      updates.push("description = ?");
      params.push(data.description);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      params.push(id);

      await this.db.prepare(`
        UPDATE wallet_rotations SET ${updates.join(", ")} WHERE id = ?
      `).bind(...params).run();
    }
  }

  async deleteWalletRotation(id: number): Promise<void> {
    await this.db.prepare("DELETE FROM wallet_rotations WHERE id = ?").bind(id).run();
  }

  async getPaymentBySubscriptionId(subscriptionId: number): Promise<Payment | null> {
    const { results } = await this.db.prepare("SELECT * FROM payments WHERE subscription_id = ? ORDER BY created_at DESC LIMIT 1").bind(subscriptionId).all();
    return results.length > 0 ? results[0] as unknown as Payment : null;
  }

  // 统计查询
  async getSubscriptionStats(): Promise<{ total_subscriptions: number; active_subscriptions: number; total_revenue: number }> {
    const { results } = await this.db.prepare(`
      SELECT
        COUNT(*) as total_subscriptions,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(price_paid) as total_revenue
      FROM subscriptions
    `).all();
    return results[0] as unknown as { total_subscriptions: number; active_subscriptions: number; total_revenue: number };
  }

  // ===== 虚拟发货相关方法 =====

  // 管理员相关操作
  async createAdmin(data: { userId: number; role?: string; permissions?: string[] }): Promise<Admin> {
    const { userId, role = 'admin', permissions = [] } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO admins (user_id, role, permissions) VALUES (?, ?, ?) RETURNING *"
    ).bind(userId, role, JSON.stringify(permissions)).all();
    return results[0] as unknown as Admin;
  }

  async getAdminByUserId(userId: number): Promise<Admin | null> {
    const { results } = await this.db.prepare("SELECT * FROM admins WHERE user_id = ? AND is_active = 1").bind(userId).all();
    return results.length > 0 ? results[0] as unknown as Admin : null;
  }

  async getAllAdmins(): Promise<(Admin & { user_name: string; user_email: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT a.*, u.name as user_name, u.email as user_email
      FROM admins a
      JOIN users u ON a.user_id = u.id
      WHERE a.is_active = 1
      ORDER BY a.created_at DESC
    `).all();
    return results as unknown as (Admin & { user_name: string; user_email: string })[];
  }

  // 订阅管理相关操作
  async updateSubscriptionNotes(subscriptionId: number, adminNotes: string, adminId: number): Promise<void> {
    // 获取旧值用于日志
    const { results: oldData } = await this.db.prepare("SELECT admin_notes FROM subscriptions WHERE id = ?").bind(subscriptionId).all();
    const oldNotes = (oldData[0] as any)?.admin_notes || '';

    // 更新订阅备注
    await this.db.prepare(
      "UPDATE subscriptions SET admin_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    ).bind(adminNotes, subscriptionId).run();

    // 记录日志
    await this.createDeliveryLog({
      subscriptionId,
      adminId,
      action: 'note_added',
      oldValue: oldNotes,
      newValue: adminNotes,
      notes: '管理员更新备注信息'
    });
  }

  async markSubscriptionDelivered(subscriptionId: number, adminId: number): Promise<void> {
    await this.db.prepare(`
      UPDATE subscriptions
      SET delivery_status = 'delivered', delivered_at = CURRENT_TIMESTAMP, delivered_by = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(adminId, subscriptionId).run();

    // 记录日志
    await this.createDeliveryLog({
      subscriptionId,
      adminId,
      action: 'delivered',
      notes: '标记为已发货'
    });
  }

  async getAllSubscriptionsForAdmin(): Promise<(Subscription & { service_name: string; plan_name: string; user_email?: string; user_name?: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT
        s.*,
        srv.name as service_name,
        sp.name as plan_name,
        u.email as user_email,
        u.name as user_name
      FROM subscriptions s
      LEFT JOIN services srv ON s.service_id = srv.id
      LEFT JOIN service_plans sp ON s.plan_id = sp.id
      LEFT JOIN users u ON s.user_id = u.id
      ORDER BY s.created_at DESC
    `).all();
    return results as unknown as (Subscription & { service_name: string; plan_name: string; user_email?: string; user_name?: string })[];
  }

  // 分页获取订阅（管理员专用）
  async getSubscriptionsForAdminPaginated(options: {
    page?: number;
    pageSize?: number;
    userFilter?: string;
    statusFilter?: string;
    serviceFilter?: string;
  }): Promise<{
    subscriptions: (Subscription & {
      service_name: string;
      plan_name: string;
      user_email?: string;
      user_name?: string;
      payment_hash?: string;
      payment_status?: string;
      expires_at?: string;
    })[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const page = options.page || 1;
    const pageSize = options.pageSize || 20;
    const offset = (page - 1) * pageSize;

    // 构建WHERE条件
    let whereConditions = [];
    let params = [];

    if (options.userFilter) {
      whereConditions.push('(u.email LIKE ? OR u.name LIKE ?)');
      const userPattern = `%${options.userFilter}%`;
      params.push(userPattern, userPattern);
    }

    if (options.statusFilter) {
      whereConditions.push('s.status = ?');
      params.push(options.statusFilter);
    }

    if (options.serviceFilter) {
      whereConditions.push('s.service_id = ?');
      params.push(parseInt(options.serviceFilter));
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM subscriptions s
      LEFT JOIN users u ON s.user_id = u.id
      ${whereClause}
    `;

    const { results: countResults } = await this.db.prepare(countQuery).bind(...params).all();
    const total = (countResults[0] as any).total;

    // 获取分页数据
    const dataQuery = `
      SELECT
        s.*,
        srv.name as service_name,
        sp.name as plan_name,
        sp.duration_days,
        u.email as user_email,
        u.name as user_name,
        p.payment_id as payment_hash,
        p.status as payment_status,
        p.network as payment_network,
        datetime(s.created_at, '+' || sp.duration_days || ' days') as expires_at
      FROM subscriptions s
      LEFT JOIN services srv ON s.service_id = srv.id
      LEFT JOIN service_plans sp ON s.plan_id = sp.id
      LEFT JOIN users u ON s.user_id = u.id
      LEFT JOIN payments p ON s.id = p.subscription_id AND p.status = 'completed'
      ${whereClause}
      ORDER BY s.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const { results } = await this.db.prepare(dataQuery).bind(...params, pageSize, offset).all();

    return {
      subscriptions: results as any,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 站内信相关操作
  async createNotification(data: {
    userId?: number;
    guestSessionId?: string;
    title: string;
    content: string;
    type?: string;
    relatedSubscriptionId?: number;
    createdBy?: number;
  }): Promise<Notification> {
    const { userId, guestSessionId, title, content, type = 'info', relatedSubscriptionId, createdBy } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO notifications (user_id, guest_session_id, title, content, type, related_subscription_id, created_by) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(userId || null, guestSessionId || null, title, content, type, relatedSubscriptionId || null, createdBy || null).all();
    return results[0] as unknown as Notification;
  }

  async getUserNotifications(userId: number): Promise<Notification[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC"
    ).bind(userId).all();
    return results as unknown as Notification[];
  }

  async getGuestNotifications(guestSessionId: string): Promise<Notification[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM notifications WHERE guest_session_id = ? ORDER BY created_at DESC"
    ).bind(guestSessionId).all();
    return results as unknown as Notification[];
  }

  async markNotificationAsRead(notificationId: number): Promise<void> {
    await this.db.prepare(
      "UPDATE notifications SET is_read = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    ).bind(notificationId).run();
  }

  async getUnreadNotificationCount(userId?: number, guestSessionId?: string): Promise<number> {
    let query = "SELECT COUNT(*) as count FROM notifications WHERE is_read = 0";
    let params: any[] = [];

    if (userId) {
      query += " AND user_id = ?";
      params.push(userId);
    } else if (guestSessionId) {
      query += " AND guest_session_id = ?";
      params.push(guestSessionId);
    }

    const { results } = await this.db.prepare(query).bind(...params).all();
    return (results[0] as any).count;
  }

  // 发货日志相关操作
  async createDeliveryLog(data: {
    subscriptionId: number;
    adminId: number;
    action: string;
    oldValue?: string;
    newValue?: string;
    notes?: string;
  }): Promise<DeliveryLog> {
    const { subscriptionId, adminId, action, oldValue, newValue, notes } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO delivery_logs (subscription_id, admin_id, action, old_value, new_value, notes) VALUES (?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(subscriptionId, adminId, action, oldValue || null, newValue || null, notes || null).all();
    return results[0] as unknown as DeliveryLog;
  }

  async getSubscriptionLogs(subscriptionId: number): Promise<(DeliveryLog & { admin_name: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT dl.*, u.name as admin_name
      FROM delivery_logs dl
      JOIN users u ON dl.admin_id = u.id
      WHERE dl.subscription_id = ?
      ORDER BY dl.created_at DESC
    `).bind(subscriptionId).all();
    return results as unknown as (DeliveryLog & { admin_name: string })[];
  }

  // 系统配置相关操作
  async getSystemConfig(configKey: string): Promise<SystemConfig | null> {
    const { results } = await this.db.prepare("SELECT * FROM system_configs WHERE config_key = ?").bind(configKey).all();
    return results.length > 0 ? results[0] as unknown as SystemConfig : null;
  }

  async updateSystemConfig(configKey: string, configValue: string): Promise<void> {
    await this.db.prepare(
      "UPDATE system_configs SET config_value = ?, updated_at = CURRENT_TIMESTAMP WHERE config_key = ?"
    ).bind(configValue, configKey).run();
  }

  // ===== Webhook相关方法 =====

  // Webhook配置管理
  async createWebhookConfig(data: {
    name: string;
    webhookType: WebhookType;
    webhookUrl: string;
    events: string[];
    secretToken?: string;
    timeoutSeconds?: number;
    retryCount?: number;
    headers?: Record<string, string>;
    botConfig?: Record<string, any>;
  }): Promise<WebhookConfig> {
    const {
      name,
      webhookType,
      webhookUrl,
      events,
      secretToken,
      timeoutSeconds = 30,
      retryCount = 3,
      headers,
      botConfig
    } = data;

    const { results } = await this.db.prepare(
      "INSERT INTO webhook_configs (name, webhook_type, webhook_url, events, secret_token, timeout_seconds, retry_count, headers, bot_config) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(
      name,
      webhookType,
      webhookUrl,
      JSON.stringify(events),
      secretToken || null,
      timeoutSeconds,
      retryCount,
      headers ? JSON.stringify(headers) : null,
      botConfig ? JSON.stringify(botConfig) : null
    ).all();
    return results[0] as unknown as WebhookConfig;
  }

  async getAllWebhookConfigs(): Promise<WebhookConfig[]> {
    const { results } = await this.db.prepare("SELECT * FROM webhook_configs ORDER BY created_at DESC").all();
    return results as unknown as WebhookConfig[];
  }

  async getActiveWebhookConfigs(eventType: string): Promise<WebhookConfig[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM webhook_configs WHERE is_active = 1 AND events LIKE ? ORDER BY created_at DESC"
    ).bind(`%"${eventType}"%`).all();
    return results as unknown as WebhookConfig[];
  }

  async updateWebhookConfig(id: number, data: Partial<{
    name: string;
    webhookUrl: string;
    events: string[];
    isActive: boolean;
    secretToken: string;
    timeoutSeconds: number;
    retryCount: number;
    headers: Record<string, string>;
  }>): Promise<void> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      updates.push("name = ?");
      values.push(data.name);
    }
    if (data.webhookUrl !== undefined) {
      updates.push("webhook_url = ?");
      values.push(data.webhookUrl);
    }
    if (data.events !== undefined) {
      updates.push("events = ?");
      values.push(JSON.stringify(data.events));
    }
    if (data.isActive !== undefined) {
      updates.push("is_active = ?");
      values.push(data.isActive);
    }
    if (data.secretToken !== undefined) {
      updates.push("secret_token = ?");
      values.push(data.secretToken);
    }
    if (data.timeoutSeconds !== undefined) {
      updates.push("timeout_seconds = ?");
      values.push(data.timeoutSeconds);
    }
    if (data.retryCount !== undefined) {
      updates.push("retry_count = ?");
      values.push(data.retryCount);
    }
    if (data.headers !== undefined) {
      updates.push("headers = ?");
      values.push(JSON.stringify(data.headers));
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(id);

      await this.db.prepare(
        `UPDATE webhook_configs SET ${updates.join(", ")} WHERE id = ?`
      ).bind(...values).run();
    }
  }

  async deleteWebhookConfig(id: number): Promise<void> {
    await this.db.prepare("DELETE FROM webhook_configs WHERE id = ?").bind(id).run();
  }

  // Webhook日志管理
  async createWebhookLog(data: {
    webhookConfigId: number;
    eventType: string;
    payload: any;
    responseStatus?: number;
    responseBody?: string;
    errorMessage?: string;
    attemptCount?: number;
    success?: boolean;
  }): Promise<WebhookLog> {
    const {
      webhookConfigId,
      eventType,
      payload,
      responseStatus,
      responseBody,
      errorMessage,
      attemptCount = 1,
      success = false
    } = data;

    const { results } = await this.db.prepare(
      "INSERT INTO webhook_logs (webhook_config_id, event_type, payload, response_status, response_body, error_message, attempt_number, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(
      webhookConfigId,
      eventType,
      JSON.stringify(payload),
      responseStatus || null,
      responseBody || null,
      errorMessage || null,
      attemptCount,
      success
    ).all();
    return results[0] as unknown as WebhookLog;
  }

  async getWebhookLogs(webhookConfigId?: number, limit: number = 100): Promise<(WebhookLog & { webhook_name: string })[]> {
    let query = `
      SELECT wl.*, wc.name as webhook_name
      FROM webhook_logs wl
      JOIN webhook_configs wc ON wl.webhook_config_id = wc.id
    `;
    const params: any[] = [];

    if (webhookConfigId) {
      query += " WHERE wl.webhook_config_id = ?";
      params.push(webhookConfigId);
    }

    query += " ORDER BY wl.created_at DESC LIMIT ?";
    params.push(limit);

    const { results } = await this.db.prepare(query).bind(...params).all();
    return results as unknown as (WebhookLog & { webhook_name: string })[];
  }

  // ===== 刷单系统相关方法 =====

  // 商品管理
  async getAllProducts(): Promise<Product[]> {
    const { results } = await this.db.prepare("SELECT * FROM products ORDER BY created_at DESC").all();
    return results as unknown as Product[];
  }

  async getActiveProducts(): Promise<Product[]> {
    const { results } = await this.db.prepare("SELECT * FROM products WHERE status = 1 ORDER BY created_at DESC").all();
    return results as unknown as Product[];
  }

  async getProductById(id: number): Promise<Product | null> {
    const { results } = await this.db.prepare("SELECT * FROM products WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as Product : null;
  }

  async createProduct(data: {
    name: string;
    image?: string;
    price: number;
    description?: string;
    status?: number;
  }): Promise<Product> {
    const { name, image, price, description, status = 1 } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO products (name, image, price, description, status) VALUES (?, ?, ?, ?, ?) RETURNING *"
    ).bind(name, image || null, price, description || null, status).all();
    return results[0] as unknown as Product;
  }

  async updateProduct(id: number, data: {
    name?: string;
    image?: string;
    price?: number;
    description?: string;
    status?: number;
  }): Promise<boolean> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      updates.push("name = ?");
      values.push(data.name);
    }
    if (data.image !== undefined) {
      updates.push("image = ?");
      values.push(data.image);
    }
    if (data.price !== undefined) {
      updates.push("price = ?");
      values.push(data.price);
    }
    if (data.description !== undefined) {
      updates.push("description = ?");
      values.push(data.description);
    }
    if (data.status !== undefined) {
      updates.push("status = ?");
      values.push(data.status);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(id);

      const result = await this.db.prepare(
        `UPDATE products SET ${updates.join(", ")} WHERE id = ?`
      ).bind(...values).run();

      return (result as any).meta?.changes > 0;
    }

    return false;
  }

  async deleteProduct(id: number): Promise<boolean> {
    const result = await this.db.prepare("DELETE FROM products WHERE id = ?").bind(id).run();
    return (result as any).meta?.changes > 0;
  }

  // 用户余额管理
  async getUserBalance(userId: number): Promise<UserBalance | null> {
    const { results } = await this.db.prepare("SELECT * FROM user_balances WHERE user_id = ?").bind(userId).all();
    return results.length > 0 ? results[0] as unknown as UserBalance : null;
  }

  async createUserBalance(userId: number): Promise<UserBalance> {
    const { results } = await this.db.prepare(
      "INSERT INTO user_balances (user_id) VALUES (?) RETURNING *"
    ).bind(userId).all();
    return results[0] as unknown as UserBalance;
  }

  async updateUserBalance(userId: number, data: {
    balance?: number;
    frozenBalance?: number;
    totalRecharged?: number;
    totalSpent?: number;
  }): Promise<boolean> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.balance !== undefined) {
      updates.push("balance = ?");
      values.push(data.balance);
    }
    if (data.frozenBalance !== undefined) {
      updates.push("frozen_balance = ?");
      values.push(data.frozenBalance);
    }
    if (data.totalRecharged !== undefined) {
      updates.push("total_recharged = ?");
      values.push(data.totalRecharged);
    }
    if (data.totalSpent !== undefined) {
      updates.push("total_spent = ?");
      values.push(data.totalSpent);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(userId);

      const result = await this.db.prepare(
        `UPDATE user_balances SET ${updates.join(", ")} WHERE user_id = ?`
      ).bind(...values).run();

      return (result as any).meta?.changes > 0;
    }

    return false;
  }

  // 充值记录管理
  async createRechargeRecord(data: {
    userId: number;
    txHash: string;
    fromAddress: string;
    toAddress: string;
    usdtAmount: number;
    internalAmount: number;
    exchangeRate?: number;
    network?: string;
  }): Promise<RechargeRecord> {
    const { userId, txHash, fromAddress, toAddress, usdtAmount, internalAmount, exchangeRate = 1.0, network = 'mainnet' } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO recharge_records (user_id, tx_hash, from_address, to_address, usdt_amount, internal_amount, exchange_rate, network) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(userId, txHash, fromAddress, toAddress, usdtAmount, internalAmount, exchangeRate, network).all();
    return results[0] as unknown as RechargeRecord;
  }

  async getRechargeRecordByTxHash(txHash: string): Promise<RechargeRecord | null> {
    const { results } = await this.db.prepare("SELECT * FROM recharge_records WHERE tx_hash = ?").bind(txHash).all();
    return results.length > 0 ? results[0] as unknown as RechargeRecord : null;
  }

  async updateRechargeRecordStatus(id: number, status: number, blockNumber?: number): Promise<boolean> {
    const updates = ["status = ?", "updated_at = CURRENT_TIMESTAMP"];
    const values = [status];

    if (blockNumber !== undefined) {
      updates.push("block_number = ?");
      values.push(blockNumber);
    }

    if (status === 1) { // 已确认
      updates.push("confirmed_at = CURRENT_TIMESTAMP");
    }

    values.push(id);

    const result = await this.db.prepare(
      `UPDATE recharge_records SET ${updates.join(", ")} WHERE id = ?`
    ).bind(...values).run();

    return (result as any).meta?.changes > 0;
  }

  async getUserRechargeRecords(userId: number, limit: number = 50): Promise<RechargeRecord[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM recharge_records WHERE user_id = ? ORDER BY created_at DESC LIMIT ?"
    ).bind(userId, limit).all();
    return results as unknown as RechargeRecord[];
  }

  // 刷单订单管理
  async createBrushOrder(data: {
    userId: number;
    productId: number;
    orderNo: string;
    productName: string;
    productPrice: number;
    quantity?: number;
    totalAmount: number;
    commissionRate: number;
    commissionAmount: number;
  }): Promise<BrushOrder> {
    const { userId, productId, orderNo, productName, productPrice, quantity = 1, totalAmount, commissionRate, commissionAmount } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO brush_orders (user_id, product_id, order_no, product_name, product_price, quantity, total_amount, commission_rate, commission_amount) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(userId, productId, orderNo, productName, productPrice, quantity, totalAmount, commissionRate, commissionAmount).all();
    return results[0] as unknown as BrushOrder;
  }

  async getBrushOrderById(id: number): Promise<BrushOrder | null> {
    const { results } = await this.db.prepare("SELECT * FROM brush_orders WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as BrushOrder : null;
  }

  async getBrushOrderByOrderNo(orderNo: string): Promise<BrushOrder | null> {
    const { results } = await this.db.prepare("SELECT * FROM brush_orders WHERE order_no = ?").bind(orderNo).all();
    return results.length > 0 ? results[0] as unknown as BrushOrder : null;
  }

  async updateBrushOrderStatus(id: number, status: number, isBurst?: boolean, burstReason?: string): Promise<boolean> {
    const updates = ["status = ?", "updated_at = CURRENT_TIMESTAMP"];
    const values: any[] = [status];

    if (isBurst !== undefined) {
      updates.push("is_burst = ?");
      values.push(isBurst ? 1 : 0);
    }

    if (burstReason !== undefined) {
      updates.push("burst_reason = ?");
      values.push(burstReason);
    }

    if (status === 2) { // 已完成
      updates.push("completed_at = CURRENT_TIMESTAMP");
    }

    values.push(id);

    const result = await this.db.prepare(
      `UPDATE brush_orders SET ${updates.join(", ")} WHERE id = ?`
    ).bind(...values).run();

    return (result as any).meta?.changes > 0;
  }

  async getUserBrushOrders(userId: number, limit: number = 50): Promise<BrushOrder[]> {
    const { results } = await this.db.prepare(`
      SELECT
        bo.*,
        COALESCE(bo.product_image, p.image) as product_image
      FROM brush_orders bo
      LEFT JOIN products p ON bo.product_id = p.id
      WHERE bo.user_id = ?
      ORDER BY bo.created_at DESC
      LIMIT ?
    `).bind(userId, limit).all();
    return results as unknown as BrushOrder[];
  }

  async getAllBrushOrdersForAdmin(limit: number = 100): Promise<(BrushOrder & { username: string })[]> {
    const { results } = await this.db.prepare(`
      SELECT bo.*, u.username
      FROM brush_orders bo
      LEFT JOIN users u ON bo.user_id = u.id
      ORDER BY bo.created_at DESC
      LIMIT ?
    `).bind(limit).all();
    return results as unknown as (BrushOrder & { username: string })[];
  }

  // 刷单规则管理
  async getAllBrushRules(): Promise<BrushRule[]> {
    const { results } = await this.db.prepare("SELECT * FROM brush_rules ORDER BY created_at DESC").all();
    return results as unknown as BrushRule[];
  }

  async getActiveBrushRule(): Promise<BrushRule | null> {
    const { results } = await this.db.prepare("SELECT * FROM brush_rules WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1").all();
    return results.length > 0 ? results[0] as unknown as BrushRule : null;
  }

  async getBrushRuleById(id: number): Promise<BrushRule | null> {
    const { results } = await this.db.prepare("SELECT * FROM brush_rules WHERE id = ?").bind(id).all();
    return results.length > 0 ? results[0] as unknown as BrushRule : null;
  }

  async createBrushRule(data: {
    name: string;
    description?: string;
    burstProbability?: number;
    burstOrderRange?: string;
    minCommissionRate?: number;
    maxCommissionRate?: number;
    dailyOrderLimit?: number;
    minOrderInterval?: number;
    maxOrderInterval?: number;
    configJson?: string;
    createdBy?: number;
  }): Promise<BrushRule> {
    const {
      name,
      description,
      burstProbability = 0.1,
      burstOrderRange = '3-7',
      minCommissionRate = 0.03,
      maxCommissionRate = 0.08,
      dailyOrderLimit = 50,
      minOrderInterval = 30,
      maxOrderInterval = 300,
      configJson,
      createdBy
    } = data;

    const { results } = await this.db.prepare(
      "INSERT INTO brush_rules (name, description, burst_probability, burst_order_range, min_commission_rate, max_commission_rate, daily_order_limit, min_order_interval, max_order_interval, config_json, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(name, description || null, burstProbability, burstOrderRange, minCommissionRate, maxCommissionRate, dailyOrderLimit, minOrderInterval, maxOrderInterval, configJson || null, createdBy || null).all();
    return results[0] as unknown as BrushRule;
  }

  async updateBrushRule(id: number, data: {
    name?: string;
    description?: string;
    isActive?: boolean;
    burstProbability?: number;
    burstOrderRange?: string;
    minCommissionRate?: number;
    maxCommissionRate?: number;
    dailyOrderLimit?: number;
    minOrderInterval?: number;
    maxOrderInterval?: number;
    configJson?: string;
  }): Promise<boolean> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      updates.push("name = ?");
      values.push(data.name);
    }
    if (data.description !== undefined) {
      updates.push("description = ?");
      values.push(data.description);
    }
    if (data.isActive !== undefined) {
      updates.push("is_active = ?");
      values.push(data.isActive);
    }
    if (data.burstProbability !== undefined) {
      updates.push("burst_probability = ?");
      values.push(data.burstProbability);
    }
    if (data.burstOrderRange !== undefined) {
      updates.push("burst_order_range = ?");
      values.push(data.burstOrderRange);
    }
    if (data.minCommissionRate !== undefined) {
      updates.push("min_commission_rate = ?");
      values.push(data.minCommissionRate);
    }
    if (data.maxCommissionRate !== undefined) {
      updates.push("max_commission_rate = ?");
      values.push(data.maxCommissionRate);
    }
    if (data.dailyOrderLimit !== undefined) {
      updates.push("daily_order_limit = ?");
      values.push(data.dailyOrderLimit);
    }
    if (data.minOrderInterval !== undefined) {
      updates.push("min_order_interval = ?");
      values.push(data.minOrderInterval);
    }
    if (data.maxOrderInterval !== undefined) {
      updates.push("max_order_interval = ?");
      values.push(data.maxOrderInterval);
    }
    if (data.configJson !== undefined) {
      updates.push("config_json = ?");
      values.push(data.configJson);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(id);

      const result = await this.db.prepare(
        `UPDATE brush_rules SET ${updates.join(", ")} WHERE id = ?`
      ).bind(...values).run();

      return (result as any).meta?.changes > 0;
    }

    return false;
  }

  // 用户刷单统计管理
  async getUserBrushStats(userId: number): Promise<UserBrushStats | null> {
    const { results } = await this.db.prepare("SELECT * FROM user_brush_stats WHERE user_id = ?").bind(userId).all();
    return results.length > 0 ? results[0] as unknown as UserBrushStats : null;
  }

  async createUserBrushStats(userId: number): Promise<UserBrushStats> {
    const { results } = await this.db.prepare(
      "INSERT INTO user_brush_stats (user_id) VALUES (?) RETURNING *"
    ).bind(userId).all();
    return results[0] as unknown as UserBrushStats;
  }

  async updateUserBrushStats(userId: number, data: {
    totalOrders?: number;
    completedOrders?: number;
    burstOrders?: number;
    totalSpent?: number;
    totalCommission?: number;
    todayOrders?: number;
    todaySpent?: number;
    todayCommission?: number;
    lastOrderAt?: string;
  }): Promise<boolean> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.totalOrders !== undefined) {
      updates.push("total_orders = ?");
      values.push(data.totalOrders);
    }
    if (data.completedOrders !== undefined) {
      updates.push("completed_orders = ?");
      values.push(data.completedOrders);
    }
    if (data.burstOrders !== undefined) {
      updates.push("burst_orders = ?");
      values.push(data.burstOrders);
    }
    if (data.totalSpent !== undefined) {
      updates.push("total_spent = ?");
      values.push(data.totalSpent);
    }
    if (data.totalCommission !== undefined) {
      updates.push("total_commission = ?");
      values.push(data.totalCommission);
    }
    if (data.todayOrders !== undefined) {
      updates.push("today_orders = ?");
      values.push(data.todayOrders);
    }
    if (data.todaySpent !== undefined) {
      updates.push("today_spent = ?");
      values.push(data.todaySpent);
    }
    if (data.todayCommission !== undefined) {
      updates.push("today_commission = ?");
      values.push(data.todayCommission);
    }
    if (data.lastOrderAt !== undefined) {
      updates.push("last_order_at = ?");
      values.push(data.lastOrderAt);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(userId);

      const result = await this.db.prepare(
        `UPDATE user_brush_stats SET ${updates.join(", ")} WHERE user_id = ?`
      ).bind(...values).run();

      return (result as any).meta?.changes > 0;
    }

    return false;
  }

  // 余额变动记录管理
  async createBalanceLog(data: {
    userId: number;
    type: number;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    relatedId?: number;
    relatedType?: string;
    description?: string;
  }): Promise<BalanceLog> {
    const { userId, type, amount, balanceBefore, balanceAfter, relatedId, relatedType, description } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO balance_logs (user_id, type, amount, balance_before, balance_after, related_id, related_type, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(userId, type, amount, balanceBefore, balanceAfter, relatedId || null, relatedType || null, description || null).all();
    return results[0] as unknown as BalanceLog;
  }

  async getUserBalanceLogs(userId: number, limit: number = 50): Promise<BalanceLog[]> {
    const { results } = await this.db.prepare(
      "SELECT * FROM balance_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT ?"
    ).bind(userId, limit).all();
    return results as unknown as BalanceLog[];
  }

  // 平台钱包管理
  async getAllPlatformWallets(): Promise<PlatformWallet[]> {
    const { results } = await this.db.prepare("SELECT * FROM platform_wallets ORDER BY created_at DESC").all();
    return results as unknown as PlatformWallet[];
  }

  async getActivePlatformWallets(network?: string, currency?: string): Promise<PlatformWallet[]> {
    let query = "SELECT * FROM platform_wallets WHERE is_active = 1";
    const params: any[] = [];

    if (network) {
      query += " AND network = ?";
      params.push(network);
    }

    if (currency) {
      query += " AND currency = ?";
      params.push(currency);
    }

    query += " ORDER BY is_default DESC, created_at DESC";

    const { results } = await this.db.prepare(query).bind(...params).all();
    return results as unknown as PlatformWallet[];
  }

  async getDefaultPlatformWallet(network: string, currency: string): Promise<PlatformWallet | null> {
    const { results } = await this.db.prepare(
      "SELECT * FROM platform_wallets WHERE network = ? AND currency = ? AND is_active = 1 AND is_default = 1 LIMIT 1"
    ).bind(network, currency).all();
    return results.length > 0 ? results[0] as unknown as PlatformWallet : null;
  }

  async createPlatformWallet(data: {
    name: string;
    address: string;
    network: string;
    currency: string;
    isActive?: boolean;
    isDefault?: boolean;
    description?: string;
  }): Promise<PlatformWallet> {
    const { name, address, network, currency, isActive = true, isDefault = false, description } = data;
    const { results } = await this.db.prepare(
      "INSERT INTO platform_wallets (name, address, network, currency, is_active, is_default, description) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING *"
    ).bind(name, address, network, currency, isActive ? 1 : 0, isDefault ? 1 : 0, description || null).all();
    return results[0] as unknown as PlatformWallet;
  }

  async updatePlatformWallet(id: number, data: {
    name?: string;
    address?: string;
    isActive?: boolean;
    isDefault?: boolean;
    description?: string;
  }): Promise<boolean> {
    const updates: string[] = [];
    const values: any[] = [];

    if (data.name !== undefined) {
      updates.push("name = ?");
      values.push(data.name);
    }
    if (data.address !== undefined) {
      updates.push("address = ?");
      values.push(data.address);
    }
    if (data.isActive !== undefined) {
      updates.push("is_active = ?");
      values.push(data.isActive ? 1 : 0);
    }
    if (data.isDefault !== undefined) {
      updates.push("is_default = ?");
      values.push(data.isDefault ? 1 : 0);
    }
    if (data.description !== undefined) {
      updates.push("description = ?");
      values.push(data.description);
    }

    if (updates.length > 0) {
      updates.push("updated_at = CURRENT_TIMESTAMP");
      values.push(id);

      const result = await this.db.prepare(
        `UPDATE platform_wallets SET ${updates.join(", ")} WHERE id = ?`
      ).bind(...values).run();
      // Cloudflare D1 的 changes 在 meta 对象中
      return (result as any).meta?.changes > 0;
    }

    return false;
  }

  async deletePlatformWallet(id: number): Promise<boolean> {
    const result = await this.db.prepare("DELETE FROM platform_wallets WHERE id = ?").bind(id).run();
    return (result as any).meta?.changes > 0;
  }

  // 刷单业务逻辑方法
  async processUserRecharge(userId: number, usdtAmount: number, txHash: string, fromAddress: string, toAddress: string, network: string = 'mainnet'): Promise<boolean> {
    try {
      // 获取汇率
      const exchangeRateConfig = await this.getSystemConfig('brush_exchange_rate');
      const exchangeRate = exchangeRateConfig ? parseFloat(exchangeRateConfig.config_value) : 1.0;

      // 计算内部金额（分）
      const internalAmount = Math.floor(usdtAmount * exchangeRate * 100);

      // 创建充值记录
      const rechargeRecord = await this.createRechargeRecord({
        userId,
        txHash,
        fromAddress,
        toAddress,
        usdtAmount,
        internalAmount,
        exchangeRate,
        network
      });

      // 获取或创建用户余额
      let userBalance = await this.getUserBalance(userId);
      if (!userBalance) {
        userBalance = await this.createUserBalance(userId);
      }

      // 更新用户余额
      const newBalance = userBalance.balance + internalAmount;
      const newTotalRecharged = userBalance.total_recharged + internalAmount;

      await this.updateUserBalance(userId, {
        balance: newBalance,
        totalRecharged: newTotalRecharged
      });

      // 创建余额变动记录
      await this.createBalanceLog({
        userId,
        type: 1, // 充值
        amount: internalAmount,
        balanceBefore: userBalance.balance,
        balanceAfter: newBalance,
        relatedId: rechargeRecord.id,
        relatedType: 'recharge',
        description: `USDT充值 ${usdtAmount} USDT`
      });

      // 确认充值记录
      await this.updateRechargeRecordStatus(rechargeRecord.id, 1);

      return true;
    } catch (error) {
      console.error('处理用户充值失败:', error);
      return false;
    }
  }

  // 生成订单号
  generateOrderNo(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `BO${timestamp}${random}`;
  }

  // 通过钱包地址查找用户ID
  async findUserByWalletAddress(address: string): Promise<number | null> {
    try {
      const { results } = await this.db.prepare(
        "SELECT user_id FROM wallets WHERE address = ? AND is_active = 1 LIMIT 1"
      ).bind(address).all();

      return results.length > 0 ? (results[0] as any).user_id : null;
    } catch (error) {
      console.error('通过钱包地址查找用户失败:', error);
      return null;
    }
  }

  // 获取用户的所有钱包地址
  async getUserWalletAddresses(userId: number): Promise<string[]> {
    try {
      const { results } = await this.db.prepare(
        "SELECT address FROM wallets WHERE user_id = ? AND is_active = 1"
      ).bind(userId).all();

      return results.map((row: any) => row.address);
    } catch (error) {
      console.error('获取用户钱包地址失败:', error);
      return [];
    }
  }
}

// 获取数据库服务实例的辅助函数
export function getDbService(env: { DB: D1Database }): DatabaseService {
  return new DatabaseService(env.DB);
}

// 重新导出 @opennextjs/cloudflare 的 getCloudflareContext
export { getCloudflareContext } from '@opennextjs/cloudflare';
