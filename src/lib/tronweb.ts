// TRON 多钱包支付集成 - 支持 TronLink、<PERSON><PERSON><PERSON>、TokenPocket 等
import { safeToNumber, formatBalance, safeLog, processContractBalance, prepareTransactionAmount, TOKEN_DECIMALS } from './bigint-utils';

declare global {
  interface Window {
    tronWeb: any;
    tronLink: any;
  }
}

export interface TronPaymentConfig {
  amount: number;
  currency: 'TRX' | 'USDT';
  toAddress: string;
  orderId: string;
  network: 'mainnet' | 'nile' | 'shasta';
}

export interface TronPaymentResult {
  success: boolean;
  txHash?: string;
  error?: string;
  amount: number;
  currency: string;
  fromAddress: string;
  toAddress: string;
}

// USDT TRC20 合约地址
export const USDT_CONTRACTS = {
  mainnet: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
  nile: 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf', // 更新的 Nile USDT 合约地址
  shasta: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'
};

// 网络配置
export const TRON_NETWORKS = {
  mainnet: {
    name: 'TRON Mainnet',
    fullHost: 'https://api.trongrid.io',
    solidityNode: 'https://api.trongrid.io',
    eventServer: 'https://api.trongrid.io',
    explorer: 'https://tronscan.org'
  },
  nile: {
    name: 'Nile Testnet',
    fullHost: 'https://api.nileex.io',
    solidityNode: 'https://api.nileex.io',
    eventServer: 'https://api.nileex.io',
    explorer: 'https://nile.tronscan.org'
  },
  shasta: {
    name: 'Shasta Testnet',
    fullHost: 'https://api.shasta.trongrid.io',
    solidityNode: 'https://api.shasta.trongrid.io',
    eventServer: 'https://api.shasta.trongrid.io',
    explorer: 'https://shasta.tronscan.org'
  }
};

export class TronWebPaymentService {
  private tronWeb: any;
  private network: keyof typeof TRON_NETWORKS;

  constructor(network: keyof typeof TRON_NETWORKS = 'mainnet') {
    this.network = network;
    this.tronWeb = null; // 明确初始化为 null

    // 如果在浏览器环境且 TronWeb 已就绪，立即初始化
    if (typeof window !== 'undefined' && window.tronWeb && window.tronWeb.ready) {
      this.tronWeb = window.tronWeb;
      // 设置API密钥
      this.setupApiKey();
      console.log('TronWeb 在构造函数中初始化成功');
    }
  }

  // 获取网络对应的API密钥
  private getApiKeyForNetwork(): string | undefined {
    switch (this.network) {
      case 'mainnet':
        return process.env.NEXT_PUBLIC_TRON_API_KEY || '3f67c2cc-9119-468b-a336-f3f95b7bfec3';
      case 'nile':
        return process.env.NEXT_PUBLIC_TRON_NILE_API_KEY;
      case 'shasta':
        return process.env.NEXT_PUBLIC_TRON_SHASTA_API_KEY;
      default:
        return undefined;
    }
  }

  // 设置API密钥
  private setupApiKey() {
    if (this.tronWeb && typeof window !== 'undefined') {
      const apiKey = this.getApiKeyForNetwork();

      if (apiKey) {
        // 设置API密钥到TronWeb实例
        if (this.tronWeb.setHeader) {
          this.tronWeb.setHeader('TRON-PRO-API-KEY', apiKey);
        }

        // 如果TronWeb有fullNode，也设置到fullNode
        if (this.tronWeb.fullNode && this.tronWeb.fullNode.setHeader) {
          this.tronWeb.fullNode.setHeader('TRON-PRO-API-KEY', apiKey);
        }

        console.log(`API密钥已设置 - 网络: ${this.network}`);
      } else {
        console.log(`无需API密钥 - 网络: ${this.network}`);
      }
    }
  }

  // 检查 TronWeb 是否可用
  async checkTronWeb(): Promise<boolean> {
    if (typeof window === 'undefined') return false;

    console.log('检查 TronWeb 状态...');

    if (window.tronWeb && window.tronWeb.ready) {
      this.tronWeb = window.tronWeb;
      this.setupApiKey(); // 设置API密钥
      console.log('TronWeb 已就绪');
      return true;
    }

    console.log('等待 TronWeb 加载...');

    // 等待 TronWeb 加载
    return new Promise((resolve) => {
      let attempts = 0;
      const checkInterval = setInterval(() => {
        attempts++;
        console.log(`TronWeb 检查尝试 ${attempts}/10`);

        if (window.tronWeb && window.tronWeb.ready) {
          this.tronWeb = window.tronWeb;
          this.setupApiKey(); // 设置API密钥
          console.log('TronWeb 加载成功');
          clearInterval(checkInterval);
          resolve(true);
        } else if (attempts > 10) {
          console.log('TronWeb 加载超时');
          clearInterval(checkInterval);
          resolve(false);
        }
      }, 500);
    });
  }

  // 检查网络
  async checkNetwork(): Promise<boolean> {
    if (!this.tronWeb) return false;

    try {
      const currentNetwork = this.tronWeb.fullNode.host;
      const expectedNetwork = TRON_NETWORKS[this.network].fullHost;
      
      return currentNetwork.includes(expectedNetwork.replace('https://', ''));
    } catch (error) {
      console.error('检查网络失败:', error);
      return false;
    }
  }

  // 获取用户地址
  async getUserAddress(): Promise<string | null> {
    if (!this.tronWeb) return null;

    try {
      let address = null;

      // 方法1: 使用 defaultAddress.base58
      if (this.tronWeb.defaultAddress && this.tronWeb.defaultAddress.base58) {
        address = this.tronWeb.defaultAddress.base58;
        console.log('使用 defaultAddress.base58 获取地址:', address);
      }

      // 方法2: 使用 tronWeb.address
      if (!address && this.tronWeb.address) {
        address = this.tronWeb.address;
        console.log('使用 tronWeb.address 获取地址:', address);
      }

      // 方法3: 请求账户
      if (!address) {
        try {
          const accounts = await this.tronWeb.request({ method: 'tron_accounts' });
          if (accounts && accounts.length > 0) {
            address = accounts[0];
            console.log('使用 tron_accounts 获取地址:', address);
          }
        } catch (e) {
          console.log('tron_accounts 方法失败:', e);
        }
      }

      // 验证地址格式
      if (address) {
        const isValid = typeof address === 'string' &&
                       address.length === 34 &&
                       address.startsWith('T');

        if (!isValid) {
          console.error('地址格式无效:', {
            address,
            type: typeof address,
            length: address?.length,
            prefix: address?.charAt(0)
          });
          return null;
        }

        console.log('地址验证成功:', address);
      }

      return address;
    } catch (error) {
      console.error('获取地址失败:', error);
      return null;
    }
  }

  // 获取余额
  async getBalance(address: string, currency: 'TRX' | 'USDT'): Promise<number> {
    console.log(`开始获取 ${currency} 余额，地址: ${address}`);

    if (!this.tronWeb) {
      console.error('TronWeb 未初始化');
      throw new Error('TronWeb 未初始化');
    }

    // 检查地址格式
    if (!address || address.length !== 34 || !address.startsWith('T')) {
      console.error('无效的 TRON 地址:', address);
      throw new Error(`无效的 TRON 地址: ${address}`);
    }

    try {
      if (currency === 'TRX') {
        console.log('开始获取 TRX 余额...');
        const balance = await this.tronWeb.trx.getBalance(address);
        console.log('TRX 原始余额 (sun):', balance);

        const trxAmount = this.tronWeb.fromSun(balance);
        console.log('TRX 转换后余额:', trxAmount);

        // 使用工具函数确保返回数字类型
        const safeAmount = safeToNumber(trxAmount);
        console.log(`TRX 最终余额: ${safeAmount} TRX`);
        return safeAmount;
      } else {
        // USDT TRC20
        const contractAddress = USDT_CONTRACTS[this.network];
        console.log(`开始获取 USDT 余额，网络: ${this.network}, 合约地址: ${contractAddress}`);

        if (!contractAddress) {
          throw new Error(`未找到 ${this.network} 网络的 USDT 合约地址`);
        }

        console.log('创建 USDT 合约实例...');
        const contract = await this.tronWeb.contract().at(contractAddress);
        console.log('合约实例创建成功，调用 balanceOf...');

        const balance = await contract.balanceOf(address).call();
        console.log('USDT 合约调用成功');

        safeLog('USDT 原始余额数据', balance);

        // 使用工具函数处理合约余额
        const usdtAmount = processContractBalance(balance, TOKEN_DECIMALS.USDT);

        console.log(`USDT 最终余额: ${usdtAmount} USDT`);
        return usdtAmount;
      }
    } catch (error: any) {
      console.error('获取余额失败:', {
        message: error?.message || 'Unknown error',
        code: error?.code,
        stack: error?.stack,
        error: error
      });

      // 抛出更详细的错误信息
      throw new Error(`获取${currency}余额失败: ${error?.message || JSON.stringify(error)}`);
    }
  }

  // TRX 转账
  async sendTRX(config: TronPaymentConfig): Promise<TronPaymentResult> {
    if (!this.tronWeb) {
      return { success: false, error: 'TronWeb 未连接', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
    }

    try {
      const fromAddress = await this.getUserAddress();
      if (!fromAddress) {
        return { success: false, error: '无法获取用户地址', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
      }

      // 检查余额
      const balance = await this.getBalance(fromAddress, 'TRX');
      if (balance < config.amount) {
        return { success: false, error: `余额不足: ${balance} TRX`, amount: config.amount, currency: config.currency, fromAddress, toAddress: config.toAddress };
      }

      // 发送交易
      const transaction = await this.tronWeb.transactionBuilder.sendTrx(
        config.toAddress,
        this.tronWeb.toSun(config.amount),
        fromAddress
      );

      const signedTransaction = await this.tronWeb.trx.sign(transaction);
      const result = await this.tronWeb.trx.sendRawTransaction(signedTransaction);

      if (result.result) {
        return {
          success: true,
          txHash: result.txid,
          amount: config.amount,
          currency: config.currency,
          fromAddress,
          toAddress: config.toAddress
        };
      } else {
        return { success: false, error: '交易失败', amount: config.amount, currency: config.currency, fromAddress, toAddress: config.toAddress };
      }
    } catch (error: any) {
      return { success: false, error: error.message || '交易失败', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
    }
  }

  // USDT TRC20 转账
  async sendUSDT(config: TronPaymentConfig): Promise<TronPaymentResult> {
    if (!this.tronWeb) {
      return { success: false, error: 'TronWeb 未连接', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
    }

    try {
      const fromAddress = await this.getUserAddress();
      if (!fromAddress) {
        return { success: false, error: '无法获取用户地址', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
      }

      // 检查 USDT 余额
      const usdtBalance = await this.getBalance(fromAddress, 'USDT');
      if (usdtBalance < config.amount) {
        return { success: false, error: `USDT 余额不足: ${usdtBalance} USDT`, amount: config.amount, currency: config.currency, fromAddress, toAddress: config.toAddress };
      }

      // 检查 TRX 余额（用于 gas）
      const trxBalance = await this.getBalance(fromAddress, 'TRX');
      if (trxBalance < 20) { // 至少需要 20 TRX 作为 gas
        return { success: false, error: `TRX 余额不足支付手续费: ${trxBalance} TRX`, amount: config.amount, currency: config.currency, fromAddress, toAddress: config.toAddress };
      }

      // 获取 USDT 合约
      const contractAddress = USDT_CONTRACTS[this.network];
      const contract = await this.tronWeb.contract().at(contractAddress);

      // 发送 USDT
      const amountInSmallestUnit = prepareTransactionAmount(config.amount, TOKEN_DECIMALS.USDT);
      const result = await contract.transfer(
        config.toAddress,
        amountInSmallestUnit
      ).send();

      // 处理不同类型的返回值
      let txHash = '';
      if (typeof result === 'string') {
        txHash = result;
      } else if (result && result.txid) {
        txHash = result.txid;
      } else if (result && result.transaction && result.transaction.txID) {
        txHash = result.transaction.txID;
      } else {
        console.warn('未知的交易结果格式:', result);
        txHash = result ? result.toString() : '';
      }

      if (txHash) {
        return {
          success: true,
          txHash: txHash,
          amount: config.amount,
          currency: config.currency,
          fromAddress,
          toAddress: config.toAddress
        };
      } else {
        return { success: false, error: 'USDT 转账失败', amount: config.amount, currency: config.currency, fromAddress, toAddress: config.toAddress };
      }
    } catch (error: any) {
      return { success: false, error: error.message || 'USDT 转账失败', amount: config.amount, currency: config.currency, fromAddress: '', toAddress: config.toAddress };
    }
  }

  // 统一支付接口
  async makePayment(config: TronPaymentConfig): Promise<TronPaymentResult> {
    if (config.currency === 'TRX') {
      return this.sendTRX(config);
    } else {
      return this.sendUSDT(config);
    }
  }

  // 查询交易状态
  async getTransactionStatus(txHash: string): Promise<'pending' | 'confirmed' | 'failed'> {
    if (!this.tronWeb) return 'failed';

    try {
      const tx = await this.tronWeb.trx.getTransactionInfo(txHash);
      
      if (tx.receipt && tx.receipt.result === 'SUCCESS') {
        return 'confirmed';
      } else if (tx.receipt && tx.receipt.result === 'REVERT') {
        return 'failed';
      } else {
        return 'pending';
      }
    } catch (error) {
      // 交易可能还在 pending
      return 'pending';
    }
  }

  // 等待交易确认
  async waitForConfirmation(txHash: string, maxWaitTime: number = 60000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getTransactionStatus(txHash);
      
      if (status === 'confirmed') {
        return true;
      } else if (status === 'failed') {
        return false;
      }
      
      // 等待 3 秒后重试
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    return false; // 超时
  }
}

// 创建支付服务实例
export function createTronPaymentService(network: keyof typeof TRON_NETWORKS = 'mainnet'): TronWebPaymentService {
  return new TronWebPaymentService(network);
}

// 检查 TronLink 是否安装
export function isTronLinkInstalled(): boolean {
  return typeof window !== 'undefined' && (!!window.tronWeb || !!window.tronLink);
}

// 获取 TronLink 安装链接
export function getTronLinkInstallUrl(): string {
  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('chrome')) {
    return 'https://chrome.google.com/webstore/detail/tronlink/ibnejdfjmmkpcnlpebklmnkoeoihofec';
  } else if (userAgent.includes('firefox')) {
    return 'https://addons.mozilla.org/en-US/firefox/addon/tronlink/';
  } else {
    return 'https://www.tronlink.org/';
  }
}
