// TRON 网络服务 - 采用 TronWeb 风格的接口，使用 HTTP API 实现
// 参考你的 TronUtils 类的设计模式

export interface TronTransactionResult {
  found: boolean;
  txHash: string;
  timestamp?: number;
  amount?: number;
  from?: string;
  to?: string;
}

export class TronWebService {
  private fullHost: string;
  private network: 'mainnet' | 'nile';
  private headers: Record<string, string>;

  constructor(network: 'mainnet' | 'nile' = 'mainnet') {
    this.network = network;
    this.fullHost = this.network === 'nile'
      ? 'https://nile.trongrid.io'
      : 'https://api.trongrid.io';

    this.headers = {
      'Content-Type': 'application/json',
      'TRON-PRO-API-KEY': process.env.TRON_API_KEY || ''
    };

    console.log(`TronWeb 兼容服务初始化完成: ${this.network} 网络, 端点: ${this.fullHost}`);
  }

  // TronWeb 兼容的工具函数
  private fromSun(sun: number): number {
    return sun / 1000000;
  }

  private toSun(trx: number): number {
    return trx * 1000000;
  }

  // 地址转换方法 (简化实现)
  private hexToAddress(hexAddress: string): string {
    // 在实际项目中，这里应该实现完整的 hex 到 base58 转换
    // 暂时返回原地址，因为 API 通常返回的已经是正确格式
    return hexAddress;
  }

  // 获取账户余额
  async getBalance(address: string, tokenType: 'TRX' | 'USDT' = 'TRX'): Promise<number> {
    try {
      if (tokenType === 'TRX') {
        // 使用 HTTP API 获取 TRX 余额
        const response = await fetch(`${this.fullHost}/wallet/getaccount`, {
          method: 'POST',
          headers: this.headers,
          body: JSON.stringify({ address })
        });

        if (!response.ok) {
          throw new Error(`API 请求失败: ${response.status}`);
        }

        const data: any = await response.json();
        const balance = data.balance || 0;
        return this.fromSun(balance);
      } else {
        // USDT TRC20 余额查询比较复杂，这里简化处理
        return 0; // 主要用于扫描功能
      }
    } catch (error) {
      console.error(`获取 ${tokenType} 余额失败:`, error);
      return 0;
    }
  }

  // 获取账户交易历史
  async getAccountTransactions(address: string, limit: number = 50): Promise<any[]> {
    try {
      const response = await fetch(`${this.fullHost}/v1/accounts/${address}/transactions?limit=${limit}&only_to=true`, {
        headers: this.headers
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const data: any = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('获取交易历史失败:', error);
      return [];
    }
  }

  // 获取 TRC20 代币转账记录
  async getTRC20Transfers(address: string, contractAddress: string, limit: number = 50): Promise<any[]> {
    try {
      // TronWeb 没有直接的 TRC20 转账历史 API，使用 HTTP API 作为降级方案
      const fullHost = this.fullHost;
      const url = `${fullHost}/v1/accounts/${address}/transactions/trc20?limit=${limit}&contract_address=${contractAddress}`;

      const response = await fetch(url, {
        headers: { 'TRON-PRO-API-KEY': process.env.TRON_API_KEY || '' }
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const data: any = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('获取 TRC20 转账记录失败:', error);
      return [];
    }
  }

  // 扫描支付交易
  async scanForPayment(
    address: string,
    expectedAmount: number,
    currency: 'TRX' | 'USDT',
    orderCreatedAt?: number
  ): Promise<TronTransactionResult> {
    try {
      console.log(`使用 TronWeb 扫描支付: ${this.network} 网络, 地址: ${address}, 金额: ${expectedAmount} ${currency}`);

      // 设置时间窗口
      const orderTime = orderCreatedAt || Date.now();
      const timeWindowBefore = 10 * 60 * 1000; // 10分钟前
      const timeWindowAfter = 24 * 60 * 60 * 1000; // 24小时后
      const minTime = orderTime - timeWindowBefore;
      const maxTime = orderTime + timeWindowAfter;

      if (currency === 'TRX') {
        return await this.scanTRXPayment(address, expectedAmount, minTime, maxTime);
      } else {
        return await this.scanUSDTPayment(address, expectedAmount, minTime, maxTime);
      }
    } catch (error) {
      console.error('TronWeb 扫描支付失败:', error);
      return { found: false, txHash: '' };
    }
  }

  // 扫描 TRX 支付
  private async scanTRXPayment(
    address: string,
    expectedAmount: number,
    minTime: number,
    maxTime: number
  ): Promise<TronTransactionResult> {
    try {
      const transactions = await this.getAccountTransactions(address, 50);
      console.log(`获取到 ${transactions.length} 条 TRX 交易记录`);

      for (const tx of transactions) {
        const txTime = tx.block_timestamp || tx.timestamp;
        if (txTime < minTime || txTime > maxTime) {
          continue;
        }

        // 检查是否是转账交易
        if (tx.raw_data?.contract?.[0]?.type === 'TransferContract') {
          const contract = tx.raw_data.contract[0];
          const toAddress = this.hexToAddress(contract.parameter.value.to_address);

          if (toAddress === address) {
            const amount = this.fromSun(contract.parameter.value.amount);
            console.log(`检查 TRX 交易: ${tx.txID}, 金额: ${amount}, 期望: ${expectedAmount}`);

            if (Math.abs(amount - expectedAmount) < 0.001) {
              console.log('找到匹配的 TRX 支付，检查确认状态:', tx.txID);

              // 检查交易确认状态
              const confirmationStatus = await this.checkTransactionConfirmation(tx.txID);

              if (confirmationStatus.isConfirmed) {
                console.log('交易已确认，支付有效!');
                return {
                  found: true,
                  txHash: tx.txID,
                  timestamp: txTime,
                  amount: amount,
                  from: this.hexToAddress(contract.parameter.value.owner_address),
                  to: toAddress
                };
              } else {
                console.log(`交易未确认，状态: ${confirmationStatus.status}, 确认数: ${confirmationStatus.confirmations}`);
                // 继续查找其他交易
              }
            }
          }
        }
      }

      return { found: false, txHash: '' };
    } catch (error) {
      console.error('扫描 TRX 支付失败:', error);
      return { found: false, txHash: '' };
    }
  }

  // 扫描 USDT 支付
  private async scanUSDTPayment(
    address: string,
    expectedAmount: number,
    minTime: number,
    maxTime: number
  ): Promise<TronTransactionResult> {
    try {
      const contractAddress = this.network === 'nile'
        ? 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'
        : 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';

      const transfers = await this.getTRC20Transfers(address, contractAddress, 50);
      console.log(`获取到 ${transfers.length} 条 USDT 转账记录`);

      for (const transfer of transfers) {
        const txTime = transfer.block_timestamp;
        if (txTime < minTime || txTime > maxTime) {
          continue;
        }

        if (transfer.to === address) {
          const amount = parseFloat(transfer.value) / 1000000; // USDT 6位小数
          console.log(`检查 USDT 转账: ${transfer.transaction_id}, 金额: ${amount}, 期望: ${expectedAmount}`);

          // 使用更严格的金额匹配：容差为 0.001 USDT
          if (Math.abs(amount - expectedAmount) < 0.001) {
            console.log('找到匹配的 USDT 支付，检查确认状态:', transfer.transaction_id);

            // 检查交易确认状态
            const confirmationStatus = await this.checkTransactionConfirmation(transfer.transaction_id);

            if (confirmationStatus.isConfirmed) {
              console.log('交易已确认，支付有效!');
              return {
                found: true,
                txHash: transfer.transaction_id,
                timestamp: txTime,
                amount: amount,
                from: transfer.from,
                to: transfer.to
              };
            } else {
              console.log(`交易未确认，状态: ${confirmationStatus.status}, 确认数: ${confirmationStatus.confirmations}`);
              // 继续查找其他交易
            }
          }
        }
      }

      return { found: false, txHash: '' };
    } catch (error) {
      console.error('扫描 USDT 支付失败:', error);
      return { found: false, txHash: '' };
    }
  }

  // 获取交易详情
  async getTransactionInfo(txHash: string): Promise<any> {
    try {
      const response = await fetch(`${this.fullHost}/wallet/gettransactioninfobyid`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({ value: txHash })
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('获取交易详情失败:', error);
      return null;
    }
  }

  // 验证地址格式
  isValidAddress(address: string): boolean {
    return address.startsWith('T') && address.length === 34;
  }

  // 地址格式转换 (公开方法)
  addressFromHex(hexAddress: string): string {
    return this.hexToAddress(hexAddress);
  }

  addressToHex(address: string): string {
    // 简化实现，实际项目中应该使用完整的 base58 转换
    return address;
  }

  // 参考你的 checkByTxid 方法，添加交易检查功能
  async checkByTxid(txid: string) {
    console.log("checkByTxid", txid);

    try {
      const response = await fetch(`${this.fullHost}/wallet/gettransactionbyid`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({ value: txid })
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const tx: any = await response.json();
      const txID = tx.txID;
      const status = tx.ret[0]?.contractRet || 'UNKNOWN';
      const timestamp = tx.raw_data.timestamp / 1000;
      const date = new Date(timestamp * 1000);

      const ownerAddressHex = tx.raw_data.contract[0].parameter.value.owner_address;
      const fromAddress = this.hexToAddress(ownerAddressHex);

      const data = tx.raw_data.contract[0].parameter.value.data;
      let toAddress = '';
      let amount = '';

      if (data && data.length >= 136) {
        // 跳过前8位（4字节方法ID），取接收地址的32字节
        const addressHex = data.slice(8 + 24, 8 + 64); // 只取后40位
        const tronHex = '41' + addressHex; // 波场地址前缀
        toAddress = this.hexToAddress(tronHex);

        const amountHex = data.slice(8 + 64);
        const amountBigInt = BigInt('0x' + amountHex);
        amount = (Number(amountBigInt) / 1e6).toString(); // USDT 6位小数
      }

      return {
        txID,
        from: fromAddress,
        to: toAddress,
        amount,
        status: status === 'SUCCESS' ? 'success' : 'failed',
        timestamp: date
      };
    } catch (error) {
      console.error('检查交易失败:', error);
      return null;
    }
  }

  // 检查交易确认状态
  async checkTransactionConfirmation(txid: string): Promise<{
    isConfirmed: boolean;
    status: string;
    confirmations: number;
    blockNumber?: number;
  }> {
    try {
      const response = await fetch(`${this.fullHost}/wallet/gettransactioninfobyid`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({ value: txid })
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const txInfo: any = await response.json();

      // 检查交易是否存在
      if (!txInfo.id) {
        return {
          isConfirmed: false,
          status: 'NOT_FOUND',
          confirmations: 0
        };
      }

      // 获取当前区块高度
      const currentBlockResponse = await fetch(`${this.fullHost}/wallet/getnowblock`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({})
      });

      if (!currentBlockResponse.ok) {
        throw new Error(`获取当前区块失败: ${currentBlockResponse.status}`);
      }

      const currentBlock: any = await currentBlockResponse.json();
      const currentBlockNumber = currentBlock.block_header.raw_data.number;
      const txBlockNumber = txInfo.blockNumber;

      // 计算确认数
      const confirmations = currentBlockNumber - txBlockNumber;

      // TRON 网络：19个或更多SR确认才算CONFIRMED
      const isConfirmed = confirmations >= 19;
      const status = isConfirmed ? 'CONFIRMED' : 'UNCONFIRMED';

      console.log(`交易 ${txid} 确认状态: ${status}, 确认数: ${confirmations}, 交易区块: ${txBlockNumber}, 当前区块: ${currentBlockNumber}`);

      return {
        isConfirmed,
        status,
        confirmations,
        blockNumber: txBlockNumber
      };
    } catch (error) {
      console.error('检查交易确认状态失败:', error);
      return {
        isConfirmed: false,
        status: 'ERROR',
        confirmations: 0
      };
    }
  }

  // 快速版本的交易检查（只获取基本信息）
  async checkByTxidFast(txid: string) {
    console.log("checkByTxidFast", txid);

    try {
      const response = await fetch(`${this.fullHost}/wallet/gettransactioninfobyid`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify({ value: txid })
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status}`);
      }

      const txInfo: any = await response.json();

      return {
        txID: txid,
        blockNumber: txInfo.blockNumber,
        blockTimeStamp: txInfo.blockTimeStamp,
        fee: txInfo.fee,
        result: txInfo.result || 'SUCCESS',
        contractResult: txInfo.contractResult?.[0] || 'SUCCESS'
      };
    } catch (error) {
      console.error('快速检查交易失败:', error);
      return null;
    }
  }
}

// 工厂函数
export function createTronWebService(network: 'mainnet' | 'nile' = 'mainnet'): TronWebService {
  return new TronWebService(network);
}
