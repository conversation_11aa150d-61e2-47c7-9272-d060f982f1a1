// BigInt 处理工具函数

/**
 * 安全地将各种类型转换为数字
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 转换后的数字
 */
export function safeToNumber(value: any, defaultValue: number = 0): number {
  try {
    if (typeof value === 'number') {
      return isNaN(value) ? defaultValue : value;
    } else if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    } else if (typeof value === 'bigint') {
      // 检查 BigInt 是否在安全范围内
      if (value > Number.MAX_SAFE_INTEGER) {
        console.warn('BigInt 值超出安全范围，可能丢失精度:', value.toString());
      }
      return Number(value);
    } else if (value && typeof value.toNumber === 'function') {
      // BigNumber 或类似对象
      return value.toNumber();
    } else if (value && typeof value.toString === 'function') {
      const str = value.toString();
      const parsed = parseFloat(str);
      return isNaN(parsed) ? defaultValue : parsed;
    } else if (value && value._hex) {
      // 十六进制 BigNumber
      return parseInt(value._hex, 16);
    } else {
      return defaultValue;
    }
  } catch (error) {
    console.error('转换数字时出错:', error, value);
    return defaultValue;
  }
}

/**
 * 安全地格式化余额显示
 * @param balance 余额值
 * @param decimals 小数位数
 * @param divisor 除数 (如 USDT 的 1000000)
 * @returns 格式化后的字符串
 */
export function formatBalance(balance: any, decimals: number = 2, divisor: number = 1): string {
  try {
    const numValue = safeToNumber(balance);
    const adjustedValue = numValue / divisor;
    return adjustedValue.toFixed(decimals);
  } catch (error) {
    console.error('格式化余额时出错:', error, balance);
    return '0.00';
  }
}

/**
 * 安全地记录包含 BigInt 的对象
 * @param label 日志标签
 * @param obj 要记录的对象
 */
export function safeLog(label: string, obj: any): void {
  try {
    if (typeof obj === 'bigint') {
      console.log(`${label}:`, `BigInt(${obj.toString()})`);
    } else if (obj && typeof obj === 'object') {
      // 创建一个可序列化的副本
      const serializable = JSON.parse(JSON.stringify(obj, (key, value) => {
        if (typeof value === 'bigint') {
          return `BigInt(${value.toString()})`;
        }
        return value;
      }));
      console.log(`${label}:`, serializable);
    } else {
      console.log(`${label}:`, obj);
    }
  } catch (error) {
    console.log(`${label}: [无法序列化的对象]`, typeof obj);
  }
}

/**
 * 检查值的类型并返回描述
 * @param value 要检查的值
 * @returns 类型描述
 */
export function getValueTypeInfo(value: any): string {
  const type = typeof value;
  
  if (type === 'bigint') {
    return `BigInt(${value.toString()})`;
  } else if (type === 'object' && value !== null) {
    if (value.constructor) {
      return `${value.constructor.name} object`;
    } else {
      return 'Object';
    }
  } else {
    return type;
  }
}

/**
 * 安全地将 USDT 余额转换为可读格式
 * @param rawBalance 原始余额 (包含 6 位小数)
 * @returns 格式化的 USDT 金额
 */
export function formatUSDTBalance(rawBalance: any): string {
  return formatBalance(rawBalance, 2, 1000000);
}

/**
 * 安全地将 TRX 余额转换为可读格式
 * @param rawBalance 原始余额 (以 sun 为单位)
 * @returns 格式化的 TRX 金额
 */
export function formatTRXBalance(rawBalance: any): string {
  return formatBalance(rawBalance, 6, 1000000);
}

/**
 * 检查余额是否足够
 * @param balance 当前余额
 * @param required 需要的金额
 * @param divisor 除数
 * @returns 是否足够
 */
export function isBalanceSufficient(balance: any, required: number, divisor: number = 1): boolean {
  try {
    const currentAmount = safeToNumber(balance) / divisor;
    return currentAmount >= required;
  } catch (error) {
    console.error('检查余额时出错:', error);
    return false;
  }
}

/**
 * 创建一个安全的 JSON 序列化器
 * @param obj 要序列化的对象
 * @returns JSON 字符串
 */
export function safeStringify(obj: any): string {
  try {
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'bigint') {
        return `BigInt(${value.toString()})`;
      } else if (typeof value === 'function') {
        return '[Function]';
      } else if (value instanceof Error) {
        return {
          name: value.name,
          message: value.message,
          stack: value.stack
        };
      }
      return value;
    }, 2);
  } catch (error) {
    return `[序列化失败: ${error.message}]`;
  }
}

/**
 * 处理 TronWeb 合约调用返回的余额
 * @param contractBalance 合约返回的余额
 * @param tokenDecimals 代币小数位数
 * @returns 处理后的数字余额
 */
export function processContractBalance(contractBalance: any, tokenDecimals: number = 6): number {
  try {
    console.log('处理合约余额:', getValueTypeInfo(contractBalance));
    
    const rawValue = safeToNumber(contractBalance);
    const adjustedValue = rawValue / Math.pow(10, tokenDecimals);
    
    console.log(`原始值: ${rawValue}, 调整后: ${adjustedValue}`);
    
    return adjustedValue;
  } catch (error) {
    console.error('处理合约余额时出错:', error);
    return 0;
  }
}

/**
 * 为 TronWeb 交易准备金额 (转换为最小单位)
 * @param amount 用户输入的金额
 * @param tokenDecimals 代币小数位数
 * @returns 转换后的最小单位金额
 */
export function prepareTransactionAmount(amount: number, tokenDecimals: number = 6): string {
  try {
    const multiplier = Math.pow(10, tokenDecimals);
    const rawAmount = Math.floor(amount * multiplier);
    
    // 对于大数值，返回字符串以避免精度丢失
    return rawAmount.toString();
  } catch (error) {
    console.error('准备交易金额时出错:', error);
    return '0';
  }
}

// 导出常用的代币小数位数
export const TOKEN_DECIMALS = {
  TRX: 6,
  USDT: 6,
  USDC: 6,
  BTC: 8,
  ETH: 18
} as const;

// 导出常用的网络配置
export const NETWORK_CONFIGS = {
  mainnet: {
    name: 'TRON Mainnet',
    chainId: 1,
    rpcUrl: 'https://api.trongrid.io'
  },
  nile: {
    name: 'Nile Testnet',
    chainId: 3448148188,
    rpcUrl: 'https://api.nileex.io'
  },
  shasta: {
    name: 'Shasta Testnet',
    chainId: 2494104990,
    rpcUrl: 'https://api.shasta.trongrid.io'
  }
} as const;
