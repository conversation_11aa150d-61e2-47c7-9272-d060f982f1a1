// Webhook队列系统 - 用于异步处理webhook发送
import type { WebhookEventType, WebhookPayload } from './webhook';

// 队列任务接口
export interface WebhookQueueTask {
  id: string;
  eventType: WebhookEventType;
  data: any;
  metadata?: any;
  priority: number;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  scheduledAt: Date;
  attempts: Array<{
    timestamp: Date;
    error?: string;
    success: boolean;
  }>;
}

// 队列优先级
export const WEBHOOK_PRIORITY = {
  HIGH: 1,     // 支付成功等重要事件
  NORMAL: 2,   // 一般业务事件
  LOW: 3       // 统计、日志等非关键事件
} as const;

// 事件优先级映射
export const EVENT_PRIORITY_MAP: Record<string, number> = {
  'payment.success': WEBHOOK_PRIORITY.HIGH,
  'payment.failed': WEBHOOK_PRIORITY.HIGH,
  'subscription.activated': WEBHOOK_PRIORITY.HIGH,
  'delivery.completed': WEBHOOK_PRIORITY.HIGH,
  'subscription.created': WEBHOOK_PRIORITY.NORMAL,
  'subscription.expired': WEBHOOK_PRIORITY.NORMAL,
  'user.registered': WEBHOOK_PRIORITY.LOW,
};

// 内存队列实现（生产环境建议使用Redis等持久化队列）
export class WebhookQueue {
  private queue: WebhookQueueTask[] = [];
  private processing = false;
  private processingInterval: NodeJS.Timeout | null = null;
  private readonly maxConcurrency = 5; // 最大并发处理数
  private readonly processingIntervalMs = 1000; // 处理间隔

  constructor() {
    this.startProcessing();
  }

  // 添加任务到队列
  enqueue(
    eventType: WebhookEventType,
    data: any,
    metadata?: any,
    priority?: number
  ): string {
    const taskId = this.generateTaskId();
    const task: WebhookQueueTask = {
      id: taskId,
      eventType,
      data,
      metadata,
      priority: priority || EVENT_PRIORITY_MAP[eventType] || WEBHOOK_PRIORITY.NORMAL,
      retryCount: 0,
      maxRetries: 3,
      createdAt: new Date(),
      scheduledAt: new Date(),
      attempts: []
    };

    this.queue.push(task);
    this.sortQueue();

    console.log(`Webhook任务已加入队列: ${taskId} (${eventType}) 优先级: ${task.priority}`);
    return taskId;
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      total: this.queue.length,
      byPriority: {
        high: this.queue.filter(t => t.priority === WEBHOOK_PRIORITY.HIGH).length,
        normal: this.queue.filter(t => t.priority === WEBHOOK_PRIORITY.NORMAL).length,
        low: this.queue.filter(t => t.priority === WEBHOOK_PRIORITY.LOW).length,
      },
      processing: this.processing
    };
  }

  // 开始处理队列
  private startProcessing() {
    if (this.processingInterval) {
      return;
    }

    this.processingInterval = setInterval(() => {
      this.processQueue().catch(error => {
        console.error('队列处理错误:', error);
      });
    }, this.processingIntervalMs);

    console.log('Webhook队列处理器已启动');
  }

  // 停止处理队列
  stopProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Webhook队列处理器已停止');
    }
  }

  // 处理队列中的任务
  private async processQueue() {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;

    try {
      // 获取待处理的任务（按优先级和时间排序）
      const tasksToProcess = this.queue
        .filter(task => task.scheduledAt <= new Date())
        .slice(0, this.maxConcurrency);

      if (tasksToProcess.length === 0) {
        return;
      }

      console.log(`开始处理 ${tasksToProcess.length} 个webhook任务`);

      // 并发处理任务
      const promises = tasksToProcess.map(task => this.processTask(task));
      await Promise.allSettled(promises);

    } finally {
      this.processing = false;
    }
  }

  // 处理单个任务
  private async processTask(task: WebhookQueueTask) {
    try {
      // 这里应该调用实际的webhook发送逻辑
      // 为了避免循环依赖，我们通过回调或事件的方式处理
      console.log(`处理webhook任务: ${task.id} (${task.eventType})`);

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 100));

      // 记录成功尝试
      task.attempts.push({
        timestamp: new Date(),
        success: true
      });

      // 从队列中移除成功的任务
      this.removeTask(task.id);

      console.log(`Webhook任务处理成功: ${task.id}`);

    } catch (error: any) {
      console.error(`Webhook任务处理失败: ${task.id}`, error);

      // 记录失败尝试
      task.attempts.push({
        timestamp: new Date(),
        error: error.message,
        success: false
      });

      task.retryCount++;

      if (task.retryCount >= task.maxRetries) {
        // 达到最大重试次数，移除任务
        console.error(`Webhook任务最终失败: ${task.id} (已重试 ${task.retryCount} 次)`);
        this.removeTask(task.id);
      } else {
        // 计算下次重试时间（指数退避）
        const delayMs = Math.pow(2, task.retryCount) * 1000;
        task.scheduledAt = new Date(Date.now() + delayMs);
        console.log(`Webhook任务将在 ${delayMs}ms 后重试: ${task.id}`);
      }
    }
  }

  // 按优先级排序队列
  private sortQueue() {
    this.queue.sort((a, b) => {
      // 首先按优先级排序（数字越小优先级越高）
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      // 相同优先级按创建时间排序
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
  }

  // 生成任务ID
  private generateTaskId(): string {
    return `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 从队列中移除任务
  private removeTask(taskId: string) {
    const index = this.queue.findIndex(task => task.id === taskId);
    if (index !== -1) {
      this.queue.splice(index, 1);
    }
  }

  // 清空队列
  clear() {
    this.queue = [];
    console.log('Webhook队列已清空');
  }

  // 获取队列中的所有任务
  getAllTasks(): WebhookQueueTask[] {
    return [...this.queue];
  }

  // 获取特定任务
  getTask(taskId: string): WebhookQueueTask | undefined {
    return this.queue.find(task => task.id === taskId);
  }
}

// 全局队列实例
let globalWebhookQueue: WebhookQueue | null = null;

// 获取全局队列实例
export function getWebhookQueue(): WebhookQueue {
  if (!globalWebhookQueue) {
    globalWebhookQueue = new WebhookQueue();
  }
  return globalWebhookQueue;
}

// 便捷函数：添加webhook到队列
export function enqueueWebhook(
  eventType: WebhookEventType,
  data: any,
  metadata?: any,
  priority?: number
): string {
  const queue = getWebhookQueue();
  return queue.enqueue(eventType, data, metadata, priority);
}

// 便捷函数：获取队列状态
export function getWebhookQueueStatus() {
  const queue = getWebhookQueue();
  return queue.getQueueStatus();
}
