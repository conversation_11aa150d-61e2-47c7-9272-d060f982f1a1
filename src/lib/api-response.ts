import { NextResponse } from 'next/server';

/**
 * 统一的API响应格式
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: any;
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T>(
  data?: T, 
  message?: string, 
  status: number = 200
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    ...(data !== undefined && { data }),
    ...(message && { message })
  };
  
  return NextResponse.json(response, { status });
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  error: string, 
  status: number = 500, 
  details?: any
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error,
    ...(details && { details })
  };
  
  return NextResponse.json(response, { status });
}

/**
 * 创建认证错误响应
 */
export function createAuthErrorResponse(error: string = '未登录'): NextResponse {
  return createErrorResponse(error, 401);
}

/**
 * 创建权限错误响应
 */
export function createPermissionErrorResponse(error: string = '权限不足'): NextResponse {
  return createErrorResponse(error, 403);
}

/**
 * 创建验证错误响应
 */
export function createValidationErrorResponse(error: string): NextResponse {
  return createErrorResponse(error, 400);
}

/**
 * 处理认证结果
 */
export function handleAuthResult(authResult: any): NextResponse | null {
  if (!authResult.success || !authResult.user) {
    return createErrorResponse(
      authResult.error || '未登录',
      authResult.status || 401
    );
  }
  return null;
}

/**
 * 检查管理员权限
 */
export function checkAdminPermission(user: any, requiredRoles: string[] = ['super_admin', 'agent']): NextResponse | null {
  if (!user.role || !requiredRoles.includes(user.role)) {
    return createPermissionErrorResponse();
  }
  return null;
}

/**
 * API响应格式规范说明：
 * 
 * 成功响应格式：
 * {
 *   "success": true,
 *   "data": any,           // 可选，响应数据
 *   "message": string      // 可选，成功消息
 * }
 * 
 * 错误响应格式：
 * {
 *   "success": false,
 *   "error": string,       // 必需，错误消息
 *   "details": any         // 可选，错误详情
 * }
 * 
 * 特殊响应格式（仅限特定API）：
 * - 登录API可能包含额外字段如 requireMFA
 * - 管理员检查API使用 isAdmin 字段
 * - 测试API可能包含额外的调试信息
 */
