// KV 存储模拟器，用于本地开发
class KVMock {
  private storage = new Map<string, { value: string; expiration?: number }>();

  async get(key: string): Promise<string | null> {
    const item = this.storage.get(key);
    if (!item) return null;
    
    // 检查是否过期
    if (item.expiration && Date.now() > item.expiration) {
      this.storage.delete(key);
      return null;
    }
    
    return item.value;
  }

  async put(key: string, value: string, options?: { expirationTtl?: number }): Promise<void> {
    const expiration = options?.expirationTtl 
      ? Date.now() + (options.expirationTtl * 1000)
      : undefined;
    
    this.storage.set(key, { value, expiration });
  }

  async delete(key: string): Promise<void> {
    this.storage.delete(key);
  }

  // 清理过期的键
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.storage.entries()) {
      if (item.expiration && now > item.expiration) {
        this.storage.delete(key);
      }
    }
  }
}

// 全局 KV 模拟实例
let kvMockInstance: KVMock | null = null;

export function getKVMock(): KVMock {
  if (!kvMockInstance) {
    kvMockInstance = new KVMock();
    
    // 每分钟清理一次过期键
    setInterval(() => {
      kvMockInstance?.cleanup();
    }, 60000);
  }
  
  return kvMockInstance;
}
