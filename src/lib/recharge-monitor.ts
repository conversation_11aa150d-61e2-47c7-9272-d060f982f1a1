// USDT充值监控服务
import { createWalletService } from './wallet-service';
import { getDbService, type DatabaseService } from './db';

export interface TransactionInfo {
  txHash: string;
  fromAddress: string;
  toAddress: string;
  amount: number;
  blockNumber: number;
  timestamp: number;
  confirmed: boolean;
}

export class RechargeMonitor {
  private dbService: DatabaseService;
  private network: 'mainnet' | 'nile';
  private walletService: any;
  private monitoringAddresses: Set<string> = new Set();
  private lastCheckedBlock: number = 0;
  private isRunning: boolean = false;

  constructor(dbService: DatabaseService, network: 'mainnet' | 'nile' = 'mainnet') {
    this.dbService = dbService;
    this.network = network;
    this.walletService = createWalletService(network);
  }

  // 启动监控
  async startMonitoring() {
    if (this.isRunning) {
      console.log('充值监控已在运行中');
      return;
    }

    console.log(`启动 ${this.network} 网络充值监控`);
    this.isRunning = true;

    // 加载平台钱包地址
    await this.loadPlatformAddresses();

    // 获取最新区块号
    this.lastCheckedBlock = await this.getCurrentBlockNumber();

    // 开始监控循环
    this.monitorLoop();
  }

  // 停止监控
  stopMonitoring() {
    console.log('停止充值监控');
    this.isRunning = false;
  }

  // 加载平台钱包地址
  private async loadPlatformAddresses() {
    try {
      const wallets = await this.dbService.getActivePlatformWallets(this.network, 'USDT');
      this.monitoringAddresses.clear();
      
      wallets.forEach(wallet => {
        this.monitoringAddresses.add(wallet.address);
        console.log(`监控地址: ${wallet.address}`);
      });

      if (this.monitoringAddresses.size === 0) {
        console.warn('没有找到需要监控的平台钱包地址');
      }
    } catch (error) {
      console.error('加载平台钱包地址失败:', error);
    }
  }

  // 获取当前区块号
  private async getCurrentBlockNumber(): Promise<number> {
    try {
      const blockInfo = await this.walletService.tronWeb.trx.getCurrentBlock();
      return blockInfo.block_header.raw_data.number;
    } catch (error) {
      console.error('获取当前区块号失败:', error);
      return 0;
    }
  }

  // 监控循环
  private async monitorLoop() {
    while (this.isRunning) {
      try {
        await this.checkNewTransactions();
        
        // 等待30秒后继续下一轮检查
        await this.sleep(30000);
      } catch (error) {
        console.error('监控循环出错:', error);
        
        // 出错后等待60秒再重试
        await this.sleep(60000);
      }
    }
  }

  // 检查新交易
  private async checkNewTransactions() {
    try {
      const currentBlock = await this.getCurrentBlockNumber();
      
      if (currentBlock <= this.lastCheckedBlock) {
        return; // 没有新区块
      }

      console.log(`检查区块 ${this.lastCheckedBlock + 1} 到 ${currentBlock}`);

      // 检查每个新区块
      for (let blockNum = this.lastCheckedBlock + 1; blockNum <= currentBlock; blockNum++) {
        await this.checkBlockTransactions(blockNum);
      }

      this.lastCheckedBlock = currentBlock;
    } catch (error) {
      console.error('检查新交易失败:', error);
    }
  }

  // 检查指定区块的交易
  private async checkBlockTransactions(blockNumber: number) {
    try {
      const block = await this.walletService.tronWeb.trx.getBlock(blockNumber);
      
      if (!block || !block.transactions) {
        return;
      }

      for (const tx of block.transactions) {
        await this.processTransaction(tx, blockNumber);
      }
    } catch (error) {
      console.error(`检查区块 ${blockNumber} 交易失败:`, error);
    }
  }

  // 处理单个交易
  private async processTransaction(tx: any, blockNumber: number) {
    try {
      const txHash = tx.txID;
      
      // 检查是否是USDT转账交易
      if (!this.isUSDTTransfer(tx)) {
        return;
      }

      const transferInfo = this.parseUSDTTransfer(tx);
      if (!transferInfo) {
        return;
      }

      // 检查是否转入监控地址
      if (!this.monitoringAddresses.has(transferInfo.toAddress)) {
        return;
      }

      console.log(`发现USDT充值交易: ${txHash}`);
      console.log(`从 ${transferInfo.fromAddress} 转入 ${transferInfo.amount} USDT 到 ${transferInfo.toAddress}`);

      // 处理充值
      await this.processRecharge(txHash, transferInfo, blockNumber);

    } catch (error) {
      console.error('处理交易失败:', error);
    }
  }

  // 判断是否是USDT转账交易
  private isUSDTTransfer(tx: any): boolean {
    if (!tx.raw_data || !tx.raw_data.contract || tx.raw_data.contract.length === 0) {
      return false;
    }

    const contract = tx.raw_data.contract[0];
    return contract.type === 'TriggerSmartContract';
  }

  // 解析USDT转账信息
  private parseUSDTTransfer(tx: any): TransactionInfo | null {
    try {
      const contract = tx.raw_data.contract[0];
      const parameter = contract.parameter.value;
      
      // 检查是否是USDT合约调用
      const usdtContract = this.getUSDTContractAddress();
      if (parameter.contract_address !== usdtContract) {
        return null;
      }

      // 解析transfer方法调用
      const data = parameter.data;
      if (!data || !data.startsWith('a9059cbb')) { // transfer方法签名
        return null;
      }

      // 解析转账参数
      const toAddressHex = data.slice(8, 72);
      const amountHex = data.slice(72, 136);

      const toAddress = this.walletService.tronWeb.address.fromHex('41' + toAddressHex.slice(24));
      const amount = parseInt(amountHex, 16) / 1000000; // USDT有6位小数

      const fromAddress = this.walletService.tronWeb.address.fromHex(parameter.owner_address);

      return {
        txHash: tx.txID,
        fromAddress,
        toAddress,
        amount,
        blockNumber: 0, // 将在调用处设置
        timestamp: tx.raw_data.timestamp,
        confirmed: true
      };
    } catch (error) {
      console.error('解析USDT转账信息失败:', error);
      return null;
    }
  }

  // 获取USDT合约地址
  private getUSDTContractAddress(): string {
    // 主网USDT合约地址
    if (this.network === 'mainnet') {
      return 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
    }
    // 测试网USDT合约地址（如果有的话）
    return 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'; // 这是一个示例地址
  }

  // 处理充值
  private async processRecharge(txHash: string, transferInfo: TransactionInfo, blockNumber: number) {
    try {
      // 检查是否已经处理过这个交易
      const existingRecord = await this.dbService.getRechargeRecordByTxHash(txHash);
      if (existingRecord) {
        console.log(`交易 ${txHash} 已处理过，跳过`);
        return;
      }

      // 查找对应的用户（通过发送地址匹配用户钱包）
      const userId = await this.findUserByAddress(transferInfo.fromAddress);
      if (!userId) {
        console.log(`未找到地址 ${transferInfo.fromAddress} 对应的用户，跳过处理`);
        return;
      }

      // 检查最小充值金额
      const minRechargeConfig = await this.dbService.getSystemConfig('brush_min_recharge');
      const minRecharge = minRechargeConfig ? parseFloat(minRechargeConfig.config_value) : 10;
      
      if (transferInfo.amount < minRecharge) {
        console.log(`充值金额 ${transferInfo.amount} USDT 小于最小充值金额 ${minRecharge} USDT，跳过处理`);
        return;
      }

      // 自动处理充值
      const success = await this.dbService.processUserRecharge(
        userId,
        transferInfo.amount,
        txHash,
        transferInfo.fromAddress,
        transferInfo.toAddress,
        this.network
      );

      if (success) {
        console.log(`用户 ${userId} 充值 ${transferInfo.amount} USDT 处理成功`);
      } else {
        console.error(`用户 ${userId} 充值处理失败`);
      }

    } catch (error) {
      console.error('处理充值失败:', error);
    }
  }

  // 通过地址查找用户
  private async findUserByAddress(address: string): Promise<number | null> {
    try {
      return await this.dbService.findUserByWalletAddress(address);
    } catch (error) {
      console.error('查找用户失败:', error);
      return null;
    }
  }

  // 睡眠函数
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建充值监控实例
export function createRechargeMonitor(env: { DB: D1Database }, network: 'mainnet' | 'nile' = 'mainnet'): RechargeMonitor {
  const dbService = getDbService(env);
  return new RechargeMonitor(dbService, network);
}
