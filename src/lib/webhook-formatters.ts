import type { WebhookPayload } from './webhook';
import type { WebhookType } from './db';

// 机器人配置接口
export interface BotConfig {
  // Telegram Bot 配置
  telegram?: {
    chatId?: string;
    parseMode?: 'HTML' | 'Markdown' | 'MarkdownV2';
    disableWebPagePreview?: boolean;
  };
  
  // 钉钉群机器人配置
  dingtalk?: {
    secret?: string;
    atMobiles?: string[];
    atUserIds?: string[];
    isAtAll?: boolean;
  };
  
  // 企业微信群机器人配置
  wechatWork?: {
    mentionedList?: string[];
    mentionedMobileList?: string[];
  };
  
  // 飞书机器人配置
  feishu?: {
    secret?: string;
    atUserIds?: string[];
    atMobiles?: string[];
    isAtAll?: boolean;
  };
}

// 消息格式化器接口
export interface MessageFormatter {
  formatMessage(payload: WebhookPayload, config?: BotConfig): any;
  getHeaders(config?: BotConfig): Record<string, string>;
}

// Telegram Bot 消息格式化器
export class TelegramFormatter implements MessageFormatter {
  formatMessage(payload: WebhookPayload, config?: BotConfig): any {
    const { telegram } = config || {};
    const { event, data, timestamp } = payload;
    
    let message = '';
    
    switch (event) {
      case 'payment.success':
        message = `🎉 <b>支付成功通知</b>\n\n` +
          `💰 金额: ${data.amount} ${data.currency}\n` +
          `📧 用户: ${data.userEmail || '游客用户'}\n` +
          `🛍️ 服务: ${data.serviceName}\n` +
          `📦 套餐: ${data.planName}\n` +
          `🆔 订阅ID: ${data.subscriptionId}\n` +
          `⏰ 时间: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      case 'delivery.completed':
        message = `📦 <b>发货完成通知</b>\n\n` +
          `🛍️ 服务: ${data.serviceName}\n` +
          `📦 套餐: ${data.planName}\n` +
          `📧 用户: ${data.userEmail || '游客用户'}\n` +
          `🆔 订阅ID: ${data.subscriptionId}\n` +
          `📝 发货信息: ${data.adminNotes || '无'}\n` +
          `⏰ 时间: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      default:
        message = `📢 <b>系统通知</b>\n\n` +
          `🔔 事件: ${event}\n` +
          `⏰ 时间: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +
          `📄 数据: <code>${JSON.stringify(data, null, 2)}</code>`;
    }
    
    return {
      chat_id: telegram?.chatId,
      text: message,
      parse_mode: telegram?.parseMode || 'HTML',
      disable_web_page_preview: telegram?.disableWebPagePreview || true
    };
  }
  
  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json'
    };
  }
}

// 钉钉群机器人消息格式化器
export class DingTalkFormatter implements MessageFormatter {
  formatMessage(payload: WebhookPayload, config?: BotConfig): any {
    const { dingtalk } = config || {};
    const { event, data, timestamp } = payload;
    
    let title = '';
    let text = '';
    
    switch (event) {
      case 'payment.success':
        title = '💰 支付成功通知';
        text = `### 支付成功通知\n\n` +
          `- **金额**: ${data.amount} ${data.currency}\n` +
          `- **用户**: ${data.userEmail || '游客用户'}\n` +
          `- **服务**: ${data.serviceName}\n` +
          `- **套餐**: ${data.planName}\n` +
          `- **订阅ID**: ${data.subscriptionId}\n` +
          `- **时间**: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      case 'delivery.completed':
        title = '📦 发货完成通知';
        text = `### 发货完成通知\n\n` +
          `- **服务**: ${data.serviceName}\n` +
          `- **套餐**: ${data.planName}\n` +
          `- **用户**: ${data.userEmail || '游客用户'}\n` +
          `- **订阅ID**: ${data.subscriptionId}\n` +
          `- **发货信息**: ${data.adminNotes || '无'}\n` +
          `- **时间**: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      default:
        title = '📢 系统通知';
        text = `### 系统通知\n\n` +
          `- **事件**: ${event}\n` +
          `- **时间**: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +
          `- **数据**: \`\`\`json\n${JSON.stringify(data, null, 2)}\n\`\`\``;
    }
    
    const message: any = {
      msgtype: 'markdown',
      markdown: {
        title,
        text
      }
    };
    
    // 添加@功能
    if (dingtalk?.atMobiles?.length || dingtalk?.atUserIds?.length || dingtalk?.isAtAll) {
      message.at = {
        atMobiles: dingtalk.atMobiles || [],
        atUserIds: dingtalk.atUserIds || [],
        isAtAll: dingtalk.isAtAll || false
      };
    }
    
    return message;
  }
  
  getHeaders(config?: BotConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    // 添加签名验证
    if (config?.dingtalk?.secret) {
      const timestamp = Date.now();
      const secret = config.dingtalk.secret;
      const stringToSign = `${timestamp}\n${secret}`;
      
      // 这里需要实现HMAC-SHA256签名
      // 在实际使用时需要添加crypto库
      headers['timestamp'] = timestamp.toString();
    }
    
    return headers;
  }
}

// 企业微信群机器人消息格式化器
export class WeChatWorkFormatter implements MessageFormatter {
  formatMessage(payload: WebhookPayload, config?: BotConfig): any {
    const { wechatWork } = config || {};
    const { event, data, timestamp } = payload;
    
    let content = '';
    
    switch (event) {
      case 'payment.success':
        content = `💰 支付成功通知\n\n` +
          `金额: ${data.amount} ${data.currency}\n` +
          `用户: ${data.userEmail || '游客用户'}\n` +
          `服务: ${data.serviceName}\n` +
          `套餐: ${data.planName}\n` +
          `订阅ID: ${data.subscriptionId}\n` +
          `时间: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      case 'delivery.completed':
        content = `📦 发货完成通知\n\n` +
          `服务: ${data.serviceName}\n` +
          `套餐: ${data.planName}\n` +
          `用户: ${data.userEmail || '游客用户'}\n` +
          `订阅ID: ${data.subscriptionId}\n` +
          `发货信息: ${data.adminNotes || '无'}\n` +
          `时间: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      default:
        content = `📢 系统通知\n\n` +
          `事件: ${event}\n` +
          `时间: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +
          `数据: ${JSON.stringify(data, null, 2)}`;
    }
    
    const message: any = {
      msgtype: 'text',
      text: {
        content
      }
    };
    
    // 添加@功能
    if (wechatWork?.mentionedList?.length || wechatWork?.mentionedMobileList?.length) {
      message.text.mentioned_list = wechatWork.mentionedList || [];
      message.text.mentioned_mobile_list = wechatWork.mentionedMobileList || [];
    }
    
    return message;
  }
  
  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json'
    };
  }
}

// 飞书机器人消息格式化器
export class FeishuFormatter implements MessageFormatter {
  formatMessage(payload: WebhookPayload, config?: BotConfig): any {
    const { feishu } = config || {};
    const { event, data, timestamp } = payload;
    
    let title = '';
    let content = '';
    
    switch (event) {
      case 'payment.success':
        title = '💰 支付成功通知';
        content = `**金额**: ${data.amount} ${data.currency}\n` +
          `**用户**: ${data.userEmail || '游客用户'}\n` +
          `**服务**: ${data.serviceName}\n` +
          `**套餐**: ${data.planName}\n` +
          `**订阅ID**: ${data.subscriptionId}\n` +
          `**时间**: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      case 'delivery.completed':
        title = '📦 发货完成通知';
        content = `**服务**: ${data.serviceName}\n` +
          `**套餐**: ${data.planName}\n` +
          `**用户**: ${data.userEmail || '游客用户'}\n` +
          `**订阅ID**: ${data.subscriptionId}\n` +
          `**发货信息**: ${data.adminNotes || '无'}\n` +
          `**时间**: ${new Date(timestamp).toLocaleString('zh-CN')}`;
        break;
        
      default:
        title = '📢 系统通知';
        content = `**事件**: ${event}\n` +
          `**时间**: ${new Date(timestamp).toLocaleString('zh-CN')}\n` +
          `**数据**: \`\`\`\n${JSON.stringify(data, null, 2)}\n\`\`\``;
    }
    
    const message: any = {
      msg_type: 'interactive',
      card: {
        elements: [
          {
            tag: 'div',
            text: {
              content,
              tag: 'lark_md'
            }
          }
        ],
        header: {
          title: {
            content: title,
            tag: 'plain_text'
          }
        }
      }
    };
    
    return message;
  }
  
  getHeaders(config?: BotConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    // 添加签名验证
    if (config?.feishu?.secret) {
      const timestamp = Math.floor(Date.now() / 1000);
      headers['timestamp'] = timestamp.toString();
    }
    
    return headers;
  }
}

// 自定义格式化器
export class CustomFormatter implements MessageFormatter {
  formatMessage(payload: WebhookPayload): any {
    return payload;
  }
  
  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json'
    };
  }
}

// 格式化器工厂
export class WebhookFormatterFactory {
  static getFormatter(webhookType: WebhookType): MessageFormatter {
    switch (webhookType) {
      case 'telegram_bot':
        return new TelegramFormatter();
      case 'dingtalk_bot':
        return new DingTalkFormatter();
      case 'wechat_work_bot':
        return new WeChatWorkFormatter();
      case 'feishu_bot':
        return new FeishuFormatter();
      case 'custom':
      default:
        return new CustomFormatter();
    }
  }
}
