import { getDbService } from './db';
import type { WebhookConfig } from './db';
import { WebhookFormatterFactory } from './webhook-formatters';
import type { BotConfig } from './webhook-formatters';
import { enqueueWebhook, getWebhookQueueStatus } from './webhook-queue';

// Webhook事件类型
export const WEBHOOK_EVENTS = {
  PAYMENT_SUCCESS: 'payment.success',
  PAYMENT_FAILED: 'payment.failed',
  SUBSCRIPTION_CREATED: 'subscription.created',
  SUBSCRIPTION_ACTIVATED: 'subscription.activated',
  SUBSCRIPTION_EXPIRED: 'subscription.expired',
  DELIVERY_COMPLETED: 'delivery.completed',
  USER_REGISTERED: 'user.registered',
} as const;

export type WebhookEventType = typeof WEBHOOK_EVENTS[keyof typeof WEBHOOK_EVENTS];

// Webhook负载接口
export interface WebhookPayload {
  event: WebhookEventType;
  timestamp: string;
  data: any;
  metadata?: {
    environment?: string;
    version?: string;
    source?: string;
    test?: boolean;
    [key: string]: any;
  };
}

// Webhook服务类
export class WebhookService {
  private dbService: any;

  constructor(env: any) {
    this.dbService = getDbService(env);
  }

  // 发送webhook通知 - 异步非阻塞
  async sendWebhook(eventType: WebhookEventType, data: any, metadata?: any): Promise<void> {
    // 立即返回，不等待webhook发送完成
    this.sendWebhookAsync(eventType, data, metadata).catch(error => {
      console.error('异步webhook发送失败:', error);
    });
  }

  // 发送webhook通知 - 同步等待完成（用于测试等需要等待结果的场景）
  async sendWebhookSync(eventType: WebhookEventType, data: any, metadata?: any): Promise<void> {
    await this.sendWebhookAsync(eventType, data, metadata);
  }

  // 发送webhook通知 - 使用队列系统（推荐用于生产环境）
  sendWebhookQueued(eventType: WebhookEventType, data: any, metadata?: any, priority?: number): string {
    console.log(`将webhook加入队列: ${eventType}`);
    return enqueueWebhook(eventType, data, metadata, priority);
  }

  // 获取队列状态
  getQueueStatus() {
    return getWebhookQueueStatus();
  }

  // 实际的webhook发送逻辑 - 异步执行
  private async sendWebhookAsync(eventType: WebhookEventType, data: any, metadata?: any): Promise<void> {
    try {
      // 获取支持该事件的活跃webhook配置
      const webhookConfigs = await this.dbService.getActiveWebhookConfigs(eventType);

      if (webhookConfigs.length === 0) {
        console.log(`没有找到支持事件 ${eventType} 的webhook配置`);
        return;
      }

      // 构建payload
      const payload: WebhookPayload = {
        event: eventType,
        timestamp: new Date().toISOString(),
        data,
        metadata: {
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
          source: 'virtual-delivery-system',
          ...metadata
        }
      };

      console.log(`开始异步发送webhook: ${eventType} 到 ${webhookConfigs.length} 个配置`);

      // 并发发送到所有配置的webhook，但不等待结果
      const promises = webhookConfigs.map((config: WebhookConfig) =>
        this.sendToWebhookWithErrorHandling(config, payload)
      );

      // 使用Promise.allSettled确保所有webhook都会被尝试发送
      const results = await Promise.allSettled(promises);

      // 统计发送结果
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`Webhook发送完成: ${successful} 成功, ${failed} 失败`);
    } catch (error) {
      console.error('发送webhook通知失败:', error);
    }
  }

  // 带错误处理的webhook发送
  private async sendToWebhookWithErrorHandling(config: WebhookConfig, payload: WebhookPayload): Promise<void> {
    try {
      await this.sendToWebhook(config, payload);
    } catch (error) {
      console.error(`Webhook发送到 ${config.name} 失败:`, error);
      // 错误已经在sendToWebhook中记录，这里只需要防止Promise.allSettled报错
    }
  }

  // 发送到单个webhook
  private async sendToWebhook(config: WebhookConfig, payload: WebhookPayload): Promise<void> {
    let attemptCount = 0;
    let lastError: string | null = null;

    while (attemptCount < config.retry_count) {
      attemptCount++;
      
      try {
        const response = await this.makeWebhookRequest(config, payload);
        
        // 记录成功日志
        await this.dbService.createWebhookLog({
          webhookConfigId: config.id,
          eventType: payload.event,
          payload,
          responseStatus: response.status,
          responseBody: await response.text(),
          attemptCount,
          success: response.ok
        });

        if (response.ok) {
          console.log(`Webhook发送成功: ${config.name} (${payload.event})`);
          return;
        } else {
          lastError = `HTTP ${response.status}: ${response.statusText}`;
        }
      } catch (error: any) {
        lastError = error.message || 'Unknown error';
        console.error(`Webhook发送失败 (尝试 ${attemptCount}/${config.retry_count}):`, error);
      }

      // 如果不是最后一次尝试，等待一段时间再重试
      if (attemptCount < config.retry_count) {
        await this.delay(Math.pow(2, attemptCount) * 1000); // 指数退避
      }
    }

    // 记录最终失败日志
    await this.dbService.createWebhookLog({
      webhookConfigId: config.id,
      eventType: payload.event,
      payload,
      errorMessage: lastError,
      attemptCount,
      success: false
    });

    console.error(`Webhook最终发送失败: ${config.name} (${payload.event}) - ${lastError}`);
  }

  // 发送HTTP请求
  private async makeWebhookRequest(config: WebhookConfig, payload: WebhookPayload): Promise<Response> {
    // 获取对应的格式化器
    const formatter = WebhookFormatterFactory.getFormatter(config.webhook_type);

    // 解析机器人配置
    let botConfig: BotConfig | undefined;
    if (config.bot_config) {
      try {
        botConfig = JSON.parse(config.bot_config);
      } catch (error) {
        console.error('解析机器人配置失败:', error);
      }
    }

    // 格式化消息
    const formattedPayload = formatter.formatMessage(payload, botConfig);
    const formatterHeaders = formatter.getHeaders(botConfig);

    const headers: Record<string, string> = {
      'User-Agent': 'VirtualDeliverySystem/1.0',
      ...formatterHeaders
    };

    // 添加签名验证
    if (config.secret_token) {
      const signature = await this.generateSignature(JSON.stringify(formattedPayload), config.secret_token);
      headers['X-Webhook-Signature'] = signature;
    }

    // 添加自定义头部
    if (config.headers) {
      try {
        const customHeaders = JSON.parse(config.headers);
        Object.assign(headers, customHeaders);
      } catch (error) {
        console.error('解析自定义头部失败:', error);
      }
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout_seconds * 1000);

    try {
      const response = await fetch(config.webhook_url, {
        method: 'POST',
        headers,
        body: JSON.stringify(formattedPayload),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  // 生成HMAC签名
  private async generateSignature(payload: string, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(payload));
    const hashArray = Array.from(new Uint8Array(signature));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return `sha256=${hashHex}`;
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 测试webhook配置
  async testWebhook(webhookId: number): Promise<{ success: boolean; message: string }> {
    try {
      const webhookConfigs = await this.dbService.getAllWebhookConfigs();
      const config = webhookConfigs.find((w: WebhookConfig) => w.id === webhookId);

      if (!config) {
        return { success: false, message: 'Webhook配置不存在' };
      }

      const testPayload: WebhookPayload = {
        event: 'test.webhook' as WebhookEventType,
        timestamp: new Date().toISOString(),
        data: {
          message: 'This is a test webhook',
          webhook_id: webhookId,
          webhook_name: config.name
        },
        metadata: {
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
          source: 'virtual-delivery-system',
          test: true
        }
      };

      // 测试时使用同步发送，等待结果
      await this.sendToWebhook(config, testPayload);
      return { success: true, message: 'Webhook测试完成，请查看日志' };
    } catch (error: any) {
      return { success: false, message: error.message || 'Webhook测试失败' };
    }
  }
}

// 便捷函数：发送特定事件的webhook - 异步非阻塞
export function sendPaymentSuccessWebhook(env: any, data: {
  subscriptionId: number;
  paymentId: number;
  amount: number;
  currency: string;
  userEmail?: string;
  serviceName: string;
  planName: string;
}) {
  const webhookService = new WebhookService(env);
  // 异步发送，不阻塞业务流程
  webhookService.sendWebhook(WEBHOOK_EVENTS.PAYMENT_SUCCESS, data);
}

export function sendDeliveryCompletedWebhook(env: any, data: {
  subscriptionId: number;
  userEmail?: string;
  serviceName: string;
  planName: string;
  adminNotes: string;
  deliveredBy: number;
}) {
  const webhookService = new WebhookService(env);
  // 异步发送，不阻塞业务流程
  webhookService.sendWebhook(WEBHOOK_EVENTS.DELIVERY_COMPLETED, data);
}

export function sendSubscriptionActivatedWebhook(env: any, data: {
  subscriptionId: number;
  userEmail?: string;
  serviceName: string;
  planName: string;
  startDate: string;
  endDate: string;
}) {
  const webhookService = new WebhookService(env);
  // 异步发送，不阻塞业务流程
  webhookService.sendWebhook(WEBHOOK_EVENTS.SUBSCRIPTION_ACTIVATED, data);
}

// 同步版本的便捷函数 - 用于需要等待webhook完成的场景（如测试）
export async function sendPaymentSuccessWebhookSync(env: any, data: {
  subscriptionId: number;
  paymentId: number;
  amount: number;
  currency: string;
  userEmail?: string;
  serviceName: string;
  planName: string;
}) {
  const webhookService = new WebhookService(env);
  await webhookService.sendWebhookSync(WEBHOOK_EVENTS.PAYMENT_SUCCESS, data);
}

export async function sendDeliveryCompletedWebhookSync(env: any, data: {
  subscriptionId: number;
  userEmail?: string;
  serviceName: string;
  planName: string;
  adminNotes: string;
  deliveredBy: number;
}) {
  const webhookService = new WebhookService(env);
  await webhookService.sendWebhookSync(WEBHOOK_EVENTS.DELIVERY_COMPLETED, data);
}

export async function sendSubscriptionActivatedWebhookSync(env: any, data: {
  subscriptionId: number;
  userEmail?: string;
  serviceName: string;
  planName: string;
  startDate: string;
  endDate: string;
}) {
  const webhookService = new WebhookService(env);
  await webhookService.sendWebhookSync(WEBHOOK_EVENTS.SUBSCRIPTION_ACTIVATED, data);
}
