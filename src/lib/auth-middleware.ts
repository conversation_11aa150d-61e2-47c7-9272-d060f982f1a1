import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

export interface AuthenticatedUser {
  id: number;
  username: string;
  email?: string;
  name?: string;
  role: 'super_admin' | 'agent' | 'user';
  permissions: string[];
  parent_admin_id?: number;
  is_active: boolean;
}

export interface AuthResult {
  success: boolean;
  user?: AuthenticatedUser;
  error?: string;
  status?: number;
}

// 验证用户身份
export async function authenticateUser(request: NextRequest): Promise<AuthResult> {
  try {
    // 从cookie中获取token
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      return { success: false, error: '未登录', status: 401 };
    }

    // 验证JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;
    
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return { success: false, error: 'Token无效', status: 401 };
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 查找用户信息
    const userResult = await db.prepare(`
      SELECT 
        u.id, u.username, u.email, u.name, u.is_active,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      LEFT JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!userResult) {
      return { success: false, error: '用户不存在', status: 404 };
    }

    // 解析权限
    let permissions: string[] = [];
    if (userResult.permissions) {
      try {
        permissions = JSON.parse(userResult.permissions);
      } catch (error) {
        console.error('解析权限失败:', error);
      }
    }

    const user: AuthenticatedUser = {
      id: userResult.id,
      username: userResult.username,
      email: userResult.email,
      name: userResult.name,
      role: (userResult.role as 'super_admin' | 'agent') || 'user',
      permissions: permissions,
      parent_admin_id: userResult.parent_admin_id,
      is_active: userResult.is_active
    };

    return { success: true, user };

  } catch (error: any) {
    console.error('用户认证失败:', error);
    return { 
      success: false, 
      error: error.message || '认证失败', 
      status: 500 
    };
  }
}

// 验证管理员权限
export async function authenticateAdmin(request: NextRequest): Promise<AuthResult> {
  const authResult = await authenticateUser(request);
  
  if (!authResult.success || !authResult.user) {
    return authResult;
  }

  // 检查是否是管理员
  if (authResult.user.role === 'user') {
    return { success: false, error: '无管理员权限', status: 403 };
  }

  return authResult;
}

// 验证超级管理员权限
export async function authenticateSuperAdmin(request: NextRequest): Promise<AuthResult> {
  const authResult = await authenticateAdmin(request);
  
  if (!authResult.success || !authResult.user) {
    return authResult;
  }

  // 检查是否是超级管理员
  if (authResult.user.role !== 'super_admin') {
    return { success: false, error: '需要超级管理员权限', status: 403 };
  }

  return authResult;
}

// 检查用户是否有特定权限
export function hasPermission(user: AuthenticatedUser, permission: string): boolean {
  // 超级管理员拥有所有权限
  if (user.role === 'super_admin') {
    return true;
  }

  // 检查用户权限列表
  return user.permissions.includes(permission);
}

// 检查用户是否可以管理指定用户
export async function canManageUser(
  adminUser: AuthenticatedUser, 
  targetUserId: number
): Promise<boolean> {
  try {
    // 超级管理员可以管理所有用户
    if (adminUser.role === 'super_admin') {
      return true;
    }

    // 代理只能管理自己邀请的用户
    if (adminUser.role === 'agent') {
      const { env } = await getCloudflareContext({ async: true });
      const db = env.DB;

      const targetUser = await db.prepare(`
        SELECT 
          u.id, u.invited_by,
          a.role, a.parent_admin_id
        FROM users u
        LEFT JOIN admins a ON u.id = a.user_id
        WHERE u.id = ?
      `).bind(targetUserId).first();

      if (!targetUser) {
        return false;
      }

      // 不能管理管理员
      if (targetUser.role) {
        return false;
      }

      // 检查是否是直接邀请的用户
      if (targetUser.invited_by === adminUser.id) {
        return true;
      }

      // 检查是否是下级代理邀请的用户
      if (targetUser.invited_by) {
        const inviter = await db.prepare(`
          SELECT 
            a.parent_admin_id
          FROM users u
          JOIN admins a ON u.id = a.user_id
          WHERE u.id = ?
        `).bind(targetUser.invited_by).first();

        // 如果邀请人的上级是当前代理
        if (inviter?.parent_admin_id) {
          const parentAdmin = await db.prepare(`
            SELECT user_id FROM admins WHERE id = ?
          `).bind(inviter.parent_admin_id).first();
          
          return parentAdmin?.user_id === adminUser.id;
        }
      }
    }

    return false;
  } catch (error) {
    console.error('检查管理权限失败:', error);
    return false;
  }
}

// 权限常量
export const PERMISSIONS = {
  // 用户管理
  MANAGE_USERS: 'manage_users',
  MANAGE_INVITED_USERS: 'manage_invited_users',
  
  // 管理员管理
  MANAGE_ADMINS: 'manage_admins',
  
  // 钱包管理
  VIEW_ALL_WALLETS: 'view_all_wallets',
  VIEW_INVITED_WALLETS: 'view_invited_wallets',
  
  // 交易管理
  VIEW_ALL_TRANSACTIONS: 'view_all_transactions',
  VIEW_INVITED_TRANSACTIONS: 'view_invited_transactions',
  
  // 系统配置
  SYSTEM_CONFIG: 'system_config',
  
  // 邀请码管理
  CREATE_INVITE_CODES: 'create_invite_codes',
  MANAGE_ALL_INVITE_CODES: 'manage_all_invite_codes'
} as const;

// 默认权限配置
export const DEFAULT_PERMISSIONS = {
  super_admin: [
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_ADMINS,
    PERMISSIONS.VIEW_ALL_WALLETS,
    PERMISSIONS.VIEW_ALL_TRANSACTIONS,
    PERMISSIONS.SYSTEM_CONFIG,
    PERMISSIONS.CREATE_INVITE_CODES,
    PERMISSIONS.MANAGE_ALL_INVITE_CODES
  ],
  agent: [
    PERMISSIONS.MANAGE_INVITED_USERS,
    PERMISSIONS.VIEW_INVITED_WALLETS,
    PERMISSIONS.VIEW_INVITED_TRANSACTIONS,
    PERMISSIONS.CREATE_INVITE_CODES
  ]
} as const;
