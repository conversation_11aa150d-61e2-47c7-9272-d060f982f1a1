// 多钱包支持库
// 支持 TronLink, Klever, TokenPocket, BitKeep 等钱包

// 扩展 Window 接口以支持多钱包
declare global {
  interface Window {
    tronWeb?: any;
    tronLink?: any;
    kleverWeb?: any;
    bitkeep?: any;
    okxwallet?: any;
  }
}

export interface WalletInfo {
  name: string;
  id: string;
  icon: string;
  downloadUrl: string;
  isInstalled: boolean;
  isConnected: boolean;
  priority: number; // 优先级，数字越小优先级越高
}

export interface WalletProvider {
  connect(): Promise<string>;
  disconnect(): Promise<void>;
  getAddress(): Promise<string>;
  getBalance(currency: 'TRX' | 'USDT'): Promise<number>;
  sendTransaction(config: any): Promise<string>;
  isConnected(): boolean;
}

// 检测已安装的钱包
export function detectInstalledWallets(): WalletInfo[] {
  const wallets: WalletInfo[] = [
    {
      name: 'TronLink',
      id: 'tronlink',
      icon: '🔗',
      downloadUrl: 'https://www.tronlink.org/',
      isInstalled: !!window.tronWeb || !!window.tronLink,
      isConnected: !!window.tronWeb?.ready,
      priority: 1
    },
    {
      name: 'Klever',
      id: 'klever',
      icon: '🔷',
      downloadUrl: 'https://klever.io/',
      isInstalled: !!window.kleverWeb,
      isConnected: !!window.kleverWeb?.isConnected,
      priority: 2
    },
    {
      name: 'TokenPocket',
      id: 'tokenpocket',
      icon: '💼',
      downloadUrl: 'https://www.tokenpocket.pro/',
      isInstalled: !!window.tronWeb && window.tronWeb.defaultAddress?.name === 'TokenPocket',
      isConnected: !!window.tronWeb?.ready && window.tronWeb.defaultAddress?.name === 'TokenPocket',
      priority: 3
    },
    {
      name: 'BitKeep',
      id: 'bitkeep',
      icon: '🔐',
      downloadUrl: 'https://bitkeep.com/',
      isInstalled: !!window.bitkeep?.tronWeb,
      isConnected: !!window.bitkeep?.tronWeb?.ready,
      priority: 4
    },
    {
      name: 'OKX Wallet',
      id: 'okx',
      icon: '⭕',
      downloadUrl: 'https://www.okx.com/web3',
      isInstalled: !!window.okxwallet?.tronLink,
      isConnected: !!window.okxwallet?.tronLink?.ready,
      priority: 5
    },
    {
      name: 'Trust Wallet',
      id: 'trust',
      icon: '🛡️',
      downloadUrl: 'https://trustwallet.com/',
      isInstalled: !!window.tronWeb && window.tronWeb.defaultAddress?.name === 'TrustWallet',
      isConnected: !!window.tronWeb?.ready && window.tronWeb.defaultAddress?.name === 'TrustWallet',
      priority: 6
    }
  ];

  // 按优先级排序
  return wallets.sort((a, b) => a.priority - b.priority);
}

// TronLink 钱包提供者
export class TronLinkProvider implements WalletProvider {
  async connect(): Promise<string> {
    if (!window.tronWeb) {
      throw new Error('TronLink 未安装');
    }

    if (window.tronLink) {
      await window.tronLink.request({
        method: 'tron_requestAccounts'
      });
    }

    // 等待连接
    let attempts = 0;
    while (attempts < 10) {
      if (window.tronWeb?.ready) {
        return window.tronWeb.defaultAddress.base58;
      }
      await new Promise(resolve => setTimeout(resolve, 500));
      attempts++;
    }

    throw new Error('TronLink 连接超时');
  }

  async disconnect(): Promise<void> {
    // TronLink 没有直接的断开连接方法
    console.log('TronLink 断开连接');
  }

  async getAddress(): Promise<string> {
    if (!window.tronWeb?.ready) {
      throw new Error('TronLink 未连接');
    }
    return window.tronWeb.defaultAddress.base58;
  }

  async getBalance(currency: 'TRX' | 'USDT'): Promise<number> {
    const tronWeb = window.tronWeb;
    if (!tronWeb?.ready) {
      throw new Error('TronLink 未连接');
    }

    if (currency === 'TRX') {
      const balance = await tronWeb.trx.getBalance(tronWeb.defaultAddress.base58);
      return tronWeb.fromSun(balance);
    } else {
      // USDT 合约地址（根据网络选择）
      const contractAddress = tronWeb.fullHost.includes('nile') 
        ? 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'
        : 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
      
      const contract = await tronWeb.contract().at(contractAddress);
      const balance = await contract.balanceOf(tronWeb.defaultAddress.base58).call();
      return parseFloat(tronWeb.toBigNumber(balance).div(1000000).toString());
    }
  }

  async sendTransaction(config: any): Promise<string> {
    const tronWeb = window.tronWeb;
    if (!tronWeb?.ready) {
      throw new Error('TronLink 未连接');
    }

    if (config.currency === 'TRX') {
      const transaction = await tronWeb.transactionBuilder.sendTrx(
        config.toAddress,
        tronWeb.toSun(config.amount),
        tronWeb.defaultAddress.base58
      );
      const signedTx = await tronWeb.trx.sign(transaction);
      const result = await tronWeb.trx.sendRawTransaction(signedTx);
      return result.txid;
    } else {
      // USDT 转账
      const contractAddress = tronWeb.fullHost.includes('nile')
        ? 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'
        : 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
      
      const contract = await tronWeb.contract().at(contractAddress);
      const result = await contract.transfer(
        config.toAddress,
        tronWeb.toBigNumber(config.amount).times(1000000)
      ).send();
      return result;
    }
  }

  isConnected(): boolean {
    return !!window.tronWeb?.ready;
  }
}

// Klever 钱包提供者
export class KleverProvider implements WalletProvider {
  async connect(): Promise<string> {
    if (!window.kleverWeb) {
      throw new Error('Klever 钱包未安装');
    }

    const result = await window.kleverWeb.initialize();
    if (result.success) {
      return result.address;
    }
    throw new Error('Klever 连接失败');
  }

  async disconnect(): Promise<void> {
    if (window.kleverWeb) {
      await window.kleverWeb.disconnect();
    }
  }

  async getAddress(): Promise<string> {
    if (!window.kleverWeb?.isConnected) {
      throw new Error('Klever 未连接');
    }
    return window.kleverWeb.address;
  }

  async getBalance(currency: 'TRX' | 'USDT'): Promise<number> {
    if (!window.kleverWeb?.isConnected) {
      throw new Error('Klever 未连接');
    }
    
    const balance = await window.kleverWeb.getBalance(currency);
    return parseFloat(balance);
  }

  async sendTransaction(config: any): Promise<string> {
    if (!window.kleverWeb?.isConnected) {
      throw new Error('Klever 未连接');
    }

    const result = await window.kleverWeb.sendTransaction({
      to: config.toAddress,
      amount: config.amount,
      currency: config.currency
    });

    if (result.success) {
      return result.txHash;
    }
    throw new Error('Klever 交易失败');
  }

  isConnected(): boolean {
    return !!window.kleverWeb?.isConnected;
  }
}

// 钱包管理器
export class WalletManager {
  private currentProvider: WalletProvider | null = null;
  private currentWalletId: string | null = null;

  async connectWallet(walletId: string): Promise<string> {
    const wallets = detectInstalledWallets();
    const wallet = wallets.find(w => w.id === walletId);

    if (!wallet) {
      throw new Error(`钱包 ${walletId} 未找到`);
    }

    if (!wallet.isInstalled) {
      throw new Error(`钱包 ${wallet.name} 未安装`);
    }

    let provider: WalletProvider;

    switch (walletId) {
      case 'tronlink':
      case 'tokenpocket':
      case 'trust':
        provider = new TronLinkProvider();
        break;
      case 'klever':
        provider = new KleverProvider();
        break;
      default:
        // 对于其他钱包，尝试使用 TronLink 兼容模式
        provider = new TronLinkProvider();
        break;
    }

    const address = await provider.connect();
    this.currentProvider = provider;
    this.currentWalletId = walletId;

    return address;
  }

  async disconnect(): Promise<void> {
    if (this.currentProvider) {
      await this.currentProvider.disconnect();
      this.currentProvider = null;
      this.currentWalletId = null;
    }
  }

  getCurrentProvider(): WalletProvider | null {
    return this.currentProvider;
  }

  getCurrentWalletId(): string | null {
    return this.currentWalletId;
  }

  isConnected(): boolean {
    return this.currentProvider?.isConnected() || false;
  }
}

// 全局钱包管理器实例
export const walletManager = new WalletManager();

// 检查钱包状态
export function checkWalletStatus() {
  const installedWallets = detectInstalledWallets();
  const hasAnyWallet = installedWallets.some(w => w.isInstalled);
  
  // 推荐优先级: TronLink > Klever > 其他
  const recommendedWallet = installedWallets.find(w => w.isInstalled) || 
                           installedWallets.find(w => w.id === 'tronlink') || 
                           installedWallets[0];

  return {
    hasAnyWallet,
    installedWallets,
    recommendedWallet
  };
}

// 移动端检测
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 获取移动端钱包链接
export function getMobileWalletLinks() {
  return {
    tronlink: {
      android: 'https://play.google.com/store/apps/details?id=com.tronlinkpro.wallet',
      ios: 'https://apps.apple.com/app/tronlink/id1453530188'
    },
    klever: {
      android: 'https://play.google.com/store/apps/details?id=io.klever.wallet',
      ios: 'https://apps.apple.com/app/klever-wallet/id1565552712'
    },
    tokenpocket: {
      android: 'https://play.google.com/store/apps/details?id=vip.mytokenpocket',
      ios: 'https://apps.apple.com/app/tokenpocket/id1436028697'
    }
  };
}
