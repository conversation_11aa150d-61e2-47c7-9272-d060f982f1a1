'use client';

import { useEffect } from 'react';

export default function NoZoom() {
  useEffect(() => {
    // 防止双指缩放
    const preventZoom = (e: TouchEvent) => {
      if (e.touches.length > 1) {
        e.preventDefault();
      }
    };

    // 防止iOS Safari输入框自动缩放
    const preventInputZoom = () => {
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content',
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover'
        );
      }
    };

    // 防止双击缩放，但允许输入框操作
    let lastTouchEnd = 0;
    const preventDoubleTapZoom = (e: TouchEvent) => {
      const target = e.target as HTMLElement;

      // 允许输入框、文本域、按钮等表单元素的正常操作
      if (target && (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'SELECT' ||
        target.closest('input') ||
        target.closest('textarea') ||
        target.closest('button') ||
        target.closest('[role="button"]') ||
        target.closest('[role="textbox"]') ||
        target.closest('.heroui-input') ||
        target.closest('.heroui-button')
      )) {
        return; // 不阻止表单元素的操作
      }

      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) {
        e.preventDefault();
      }
      lastTouchEnd = now;
    };

    // 防止键盘缩放 (Ctrl + +/-, Ctrl + 滚轮)
    const preventKeyboardZoom = (e: KeyboardEvent) => {
      if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
        e.preventDefault();
      }
    };

    // 防止滚轮缩放
    const preventWheelZoom = (e: WheelEvent) => {
      if (e.ctrlKey) {
        e.preventDefault();
      }
    };

    // 处理输入框焦点事件，防止自动缩放
    const handleInputFocus = (e: FocusEvent) => {
      const target = e.target as HTMLElement;
      if (target && (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.closest('input') ||
        target.closest('textarea')
      )) {
        // 强制设置viewport防止缩放
        preventInputZoom();

        // 延迟执行，确保viewport设置生效
        setTimeout(() => {
          preventInputZoom();
        }, 100);
      }
    };

    const handleInputBlur = () => {
      // 输入框失焦后重新设置viewport
      setTimeout(() => {
        preventInputZoom();
      }, 300);
    };

    // 初始化viewport设置
    preventInputZoom();

    // 添加事件监听器
    document.addEventListener('touchstart', preventZoom, { passive: false });
    document.addEventListener('touchmove', preventZoom, { passive: false });
    document.addEventListener('touchend', preventDoubleTapZoom, { passive: false });
    document.addEventListener('keydown', preventKeyboardZoom);
    document.addEventListener('wheel', preventWheelZoom, { passive: false });
    document.addEventListener('focusin', handleInputFocus, { passive: true });
    document.addEventListener('focusout', handleInputBlur, { passive: true });

    // 清理函数
    return () => {
      document.removeEventListener('touchstart', preventZoom);
      document.removeEventListener('touchmove', preventZoom);
      document.removeEventListener('touchend', preventDoubleTapZoom);
      document.removeEventListener('keydown', preventKeyboardZoom);
      document.removeEventListener('wheel', preventWheelZoom);
      document.removeEventListener('focusin', handleInputFocus);
      document.removeEventListener('focusout', handleInputBlur);
    };
  }, []);

  return null; // 这个组件不渲染任何内容
}
