'use client';

import { HeroUIProvider } from "@heroui/react";
import SessionProvider from "@/components/SessionProvider";
import PWAInstaller from "@/components/PWAInstaller";

export default function Providers({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <HeroUIProvider>
      <SessionProvider>
        <PWAInstaller />
        {children}
      </SessionProvider>
    </HeroUIProvider>
  );
}
