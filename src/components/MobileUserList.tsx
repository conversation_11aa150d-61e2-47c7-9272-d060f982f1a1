'use client';

import { <PERSON>, <PERSON>B<PERSON>, <PERSON>, <PERSON><PERSON>, Divider } from '@heroui/react';

interface User {
  id: number;
  username: string;
  email?: string;
  name?: string;
  role: 'super_admin' | 'agent' | 'user';
  is_active: boolean;
  created_at: string;
  invite_code?: string;
  invited_by_username?: string;
}

interface MobileUserListProps {
  users: User[];
  currentUser: User;
  onToggleStatus: (userId: number, currentStatus: boolean) => void;
}

export default function MobileUserList({ users, currentUser, onToggleStatus }: MobileUserListProps) {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin': return 'danger';
      case 'agent': return 'primary';
      default: return 'default';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'super_admin': return '超级管理员';
      case 'agent': return '代理';
      default: return '普通用户';
    }
  };

  return (
    <div className="space-y-3">
      {users.map((user) => (
        <Card key={user.id} className="w-full">
          <CardBody className="p-4">
            {/* 用户基本信息 */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-lg">{user.username}</h3>
                  <Chip
                    size="sm"
                    color={getRoleColor(user.role)}
                    variant="flat"
                  >
                    {getRoleText(user.role)}
                  </Chip>
                </div>
                {user.name && (
                  <p className="text-sm text-gray-600 mb-1">{user.name}</p>
                )}
                {user.email && (
                  <p className="text-sm text-gray-500">{user.email}</p>
                )}
              </div>
              
              {/* 状态和操作 */}
              <div className="flex flex-col items-end gap-2">
                <Chip
                  size="sm"
                  color={user.is_active ? 'success' : 'default'}
                  variant="flat"
                >
                  {user.is_active ? '活跃' : '禁用'}
                </Chip>
                
                {currentUser.role === 'super_admin' && user.id !== currentUser.id && (
                  <Button
                    size="sm"
                    color={user.is_active ? 'danger' : 'success'}
                    variant="light"
                    onPress={() => onToggleStatus(user.id, user.is_active)}
                    className="min-w-16"
                  >
                    {user.is_active ? '禁用' : '启用'}
                  </Button>
                )}
              </div>
            </div>

            <Divider className="my-3" />

            {/* 详细信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">注册时间:</span>
                <p className="font-medium">
                  {new Date(user.created_at).toLocaleDateString('zh-CN')}
                </p>
              </div>
              
              {user.invite_code && (
                <div>
                  <span className="text-gray-500">邀请码:</span>
                  <p className="font-medium font-mono text-xs">{user.invite_code}</p>
                </div>
              )}
              
              {user.invited_by_username && (
                <div className="col-span-2">
                  <span className="text-gray-500">邀请人:</span>
                  <p className="font-medium">{user.invited_by_username}</p>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      ))}
      
      {users.length === 0 && (
        <Card>
          <CardBody className="text-center py-8">
            <p className="text-gray-500">暂无用户数据</p>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
