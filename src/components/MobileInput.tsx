'use client';

import { Input, InputProps } from '@heroui/react';
import { useEffect, useRef } from 'react';

interface MobileInputProps extends InputProps {
  preventZoom?: boolean;
}

export default function MobileInput({ preventZoom = true, ...props }: MobileInputProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!preventZoom) return;

    const handleFocus = () => {
      // 防止iOS Safari自动缩放
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover'
        );
      }

      // 确保输入框字体大小至少16px
      if (inputRef.current) {
        const computedStyle = window.getComputedStyle(inputRef.current);
        const fontSize = parseFloat(computedStyle.fontSize);
        if (fontSize < 16) {
          inputRef.current.style.fontSize = '16px';
        }
      }
    };

    const handleBlur = () => {
      // 输入框失焦后延迟重置viewport
      setTimeout(() => {
        const viewport = document.querySelector('meta[name=viewport]');
        if (viewport) {
          viewport.setAttribute('content', 
            'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover'
          );
        }
      }, 300);
    };

    const inputElement = inputRef.current;
    if (inputElement) {
      inputElement.addEventListener('focus', handleFocus);
      inputElement.addEventListener('blur', handleBlur);

      return () => {
        inputElement.removeEventListener('focus', handleFocus);
        inputElement.removeEventListener('blur', handleBlur);
      };
    }
  }, [preventZoom]);

  return (
    <Input
      {...props}
      ref={inputRef}
      classNames={{
        input: [
          'text-base', // 确保字体大小至少16px
          'sm:text-sm', // 在大屏幕上使用较小字体
          ...(Array.isArray(props.classNames?.input) ? props.classNames.input : [props.classNames?.input].filter(Boolean))
        ],
        inputWrapper: [
          ...(Array.isArray(props.classNames?.inputWrapper) ? props.classNames.inputWrapper : [props.classNames?.inputWrapper].filter(Boolean))
        ]
      }}
      style={{
        fontSize: '16px', // 强制设置字体大小
        ...props.style
      }}
    />
  );
}
