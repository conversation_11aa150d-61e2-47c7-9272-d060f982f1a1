'use client';

import { useEffect, useRef } from 'react';
import QRCode from 'qrcode';

interface QRCodeProps {
  value: string;
  size?: number;
  className?: string;
}

export default function QRCodeComponent({ value, size = 200, className = '' }: QRCodeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (canvasRef.current && value) {
      QRCode.toCanvas(canvasRef.current, value, {
        width: size,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      }).catch((error) => {
        console.error('生成二维码失败:', error);
      });
    }
  }, [value, size]);

  if (!value) {
    return (
      <div 
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={{ width: size, height: size }}
      >
        <p className="text-gray-500 text-sm">无效地址</p>
      </div>
    );
  }

  return (
    <canvas 
      ref={canvasRef}
      className={`border rounded-lg ${className}`}
      style={{ maxWidth: '100%', height: 'auto' }}
    />
  );
}
