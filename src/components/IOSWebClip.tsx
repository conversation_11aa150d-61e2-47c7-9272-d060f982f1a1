'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Card, CardBody } from '@heroui/react';

interface IOSWebClipProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function IOSWebClip({ isOpen, onClose }: IOSWebClipProps) {
  const [step, setStep] = useState(1);

  const steps = [
    {
      title: '第1步：点击分享按钮',
      description: '在Safari浏览器底部找到分享按钮',
      icon: '📤',
      image: '/instructions/ios-step1.png',
      instruction: '点击Safari底部工具栏中的分享按钮（方形带箭头向上的图标）'
    },
    {
      title: '第2步：找到"添加到主屏幕"',
      description: '在分享菜单中向下滚动',
      icon: '📱',
      image: '/instructions/ios-step2.png',
      instruction: '在弹出的分享菜单中，向下滚动找到"添加到主屏幕"选项'
    },
    {
      title: '第3步：点击"添加到主屏幕"',
      description: '选择添加到主屏幕选项',
      icon: '➕',
      image: '/instructions/ios-step3.png',
      instruction: '点击"添加到主屏幕"选项，系统会显示应用预览'
    },
    {
      title: '第4步：确认安装',
      description: '确认应用名称并完成安装',
      icon: '✅',
      image: '/instructions/ios-step4.png',
      instruction: '确认应用名称为"TRON钱包"，然后点击右上角的"添加"按钮'
    }
  ];

  const currentStep = steps[step - 1];

  const nextStep = () => {
    if (step < steps.length) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleClose = () => {
    setStep(1);
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-xl font-bold">iOS 安装指导</h2>
          <p className="text-sm text-gray-600">将TRON钱包添加到iPhone/iPad主屏幕</p>
        </ModalHeader>
        
        <ModalBody>
          {/* 进度指示器 */}
          <div className="flex justify-center mb-6">
            <div className="flex space-x-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    index + 1 <= step ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* 当前步骤 */}
          <Card className="mb-6">
            <CardBody className="text-center p-6">
              <div className="text-4xl mb-4">{currentStep.icon}</div>
              <h3 className="text-lg font-semibold mb-2">{currentStep.title}</h3>
              <p className="text-gray-600 mb-4">{currentStep.description}</p>
              
              {/* 步骤图片占位符 */}
              <div className="bg-gray-100 rounded-lg p-8 mb-4">
                <div className="text-6xl text-gray-400 mb-2">📱</div>
                <p className="text-sm text-gray-500">
                  {currentStep.instruction}
                </p>
              </div>
            </CardBody>
          </Card>

          {/* 详细说明 */}
          <Card className="bg-blue-50">
            <CardBody className="p-4">
              <h4 className="font-semibold text-blue-800 mb-2">💡 提示</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 确保使用Safari浏览器（其他浏览器不支持此功能）</li>
                <li>• 安装后应用图标会出现在主屏幕上</li>
                <li>• 应用将以全屏模式运行，提供原生应用体验</li>
                <li>• 支持离线使用部分功能</li>
              </ul>
            </CardBody>
          </Card>

          {/* 常见问题 */}
          {step === steps.length && (
            <Card className="bg-green-50 mt-4">
              <CardBody className="p-4">
                <h4 className="font-semibold text-green-800 mb-2">🎉 安装完成！</h4>
                <div className="text-sm text-green-700 space-y-2">
                  <p>恭喜！TRON钱包已成功添加到您的主屏幕。</p>
                  
                  <div className="mt-3">
                    <p className="font-medium">接下来您可以：</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>从主屏幕点击TRON钱包图标启动应用</li>
                      <li>享受全屏的原生应用体验</li>
                      <li>使用所有钱包功能：创建、导入、转账、收款</li>
                      <li>即使在弱网环境下也能正常使用</li>
                    </ul>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}
        </ModalBody>
        
        <ModalFooter>
          <div className="flex justify-between w-full">
            <Button
              variant="bordered"
              onPress={prevStep}
              isDisabled={step === 1}
            >
              上一步
            </Button>
            
            <div className="flex gap-2">
              {step < steps.length ? (
                <Button
                  color="primary"
                  onPress={nextStep}
                >
                  下一步
                </Button>
              ) : (
                <Button
                  color="success"
                  onPress={handleClose}
                >
                  完成
                </Button>
              )}
              
              <Button
                variant="light"
                onPress={handleClose}
              >
                关闭
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
