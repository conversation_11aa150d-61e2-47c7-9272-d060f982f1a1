'use client';

import { <PERSON>, CardBody, <PERSON>, But<PERSON>, Divider } from '@heroui/react';

interface Wallet {
  id: number;
  user_id: number;
  name: string;
  address: string;
  wallet_type: 'created' | 'imported';
  network: 'mainnet' | 'nile' | 'shasta';
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  username: string;
  email?: string;
  user_name?: string;
}

interface User {
  role: 'super_admin' | 'agent' | 'user';
}

interface MobileWalletListProps {
  wallets: Wallet[];
  currentUser: User;
  onToggleStatus: (walletId: number, currentStatus: boolean) => void;
}

export default function MobileWalletList({ wallets, currentUser, onToggleStatus }: MobileWalletListProps) {
  const getNetworkColor = (network: string) => {
    switch (network) {
      case 'mainnet': return 'success';
      case 'nile': return 'primary';
      case 'shasta': return 'warning';
      default: return 'default';
    }
  };

  const getNetworkText = (network: string) => {
    switch (network) {
      case 'mainnet': return '主网';
      case 'nile': return 'Nile';
      case 'shasta': return 'Shasta';
      default: return network;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-6)}`;
  };

  return (
    <div className="space-y-3">
      {wallets.map((wallet) => (
        <Card key={wallet.id} className="w-full">
          <CardBody className="p-4">
            {/* 钱包基本信息 */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-lg">{wallet.name}</h3>
                  {wallet.is_default && (
                    <Chip size="sm" color="warning" variant="flat">
                      默认
                    </Chip>
                  )}
                </div>
                <p className="text-sm font-mono text-gray-600 mb-1">
                  {formatAddress(wallet.address)}
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">用户:</span>
                  <span className="text-sm font-medium">{wallet.username}</span>
                  {wallet.user_name && (
                    <span className="text-xs text-gray-400">({wallet.user_name})</span>
                  )}
                </div>
              </div>
              
              {/* 状态和操作 */}
              <div className="flex flex-col items-end gap-2">
                <Chip
                  size="sm"
                  color={wallet.is_active ? 'success' : 'danger'}
                  variant="flat"
                >
                  {wallet.is_active ? '正常' : '禁用'}
                </Chip>
                
                {currentUser.role === 'super_admin' && (
                  <Button
                    size="sm"
                    color={wallet.is_active ? 'danger' : 'success'}
                    variant="light"
                    onPress={() => onToggleStatus(wallet.id, wallet.is_active)}
                    className="min-w-16"
                  >
                    {wallet.is_active ? '禁用' : '启用'}
                  </Button>
                )}
              </div>
            </div>

            <Divider className="my-3" />

            {/* 详细信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">类型:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={wallet.wallet_type === 'created' ? 'primary' : 'secondary'}
                    variant="flat"
                  >
                    {wallet.wallet_type === 'created' ? '创建' : '导入'}
                  </Chip>
                </div>
              </div>
              
              <div>
                <span className="text-gray-500">网络:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={getNetworkColor(wallet.network)}
                    variant="flat"
                  >
                    {getNetworkText(wallet.network)}
                  </Chip>
                </div>
              </div>
              
              <div className="col-span-2">
                <span className="text-gray-500">创建时间:</span>
                <p className="font-medium">
                  {new Date(wallet.created_at).toLocaleDateString('zh-CN')}
                </p>
              </div>
              
              {/* 完整地址 */}
              <div className="col-span-2">
                <span className="text-gray-500">完整地址:</span>
                <p className="font-mono text-xs bg-gray-100 p-2 rounded mt-1 break-all">
                  {wallet.address}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      ))}
      
      {wallets.length === 0 && (
        <Card>
          <CardBody className="text-center py-8">
            <p className="text-gray-500">暂无钱包数据</p>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
