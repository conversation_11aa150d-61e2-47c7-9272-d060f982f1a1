'use client';

import { useEffect } from 'react';

export default function FontFallback() {
  useEffect(() => {
    // 检查字体是否加载成功
    const checkFontLoading = () => {
      const testElement = document.createElement('div');
      testElement.style.fontFamily = 'Geist, system-ui';
      testElement.style.position = 'absolute';
      testElement.style.visibility = 'hidden';
      testElement.style.fontSize = '16px';
      testElement.textContent = 'Test';
      
      document.body.appendChild(testElement);
      
      // 如果字体加载失败，添加回退样式
      setTimeout(() => {
        const computedStyle = window.getComputedStyle(testElement);
        const fontFamily = computedStyle.fontFamily;
        
        if (!fontFamily.includes('Geist')) {
          console.warn('Geist font failed to load, using system fonts');
          document.documentElement.style.setProperty(
            '--font-geist-sans', 
            'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, "Helvetica Neue", Arial, sans-serif'
          );
          document.documentElement.style.setProperty(
            '--font-geist-mono', 
            'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Inconsolata, "Roboto Mono", monospace'
          );
        }
        
        document.body.removeChild(testElement);
      }, 1000);
    };

    // 延迟检查，确保字体有时间加载
    setTimeout(checkFontLoading, 2000);
  }, []);

  return null;
}
