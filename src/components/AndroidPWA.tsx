'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ontent, <PERSON>dalHeader, <PERSON>dalBody, <PERSON>dal<PERSON>ooter, Card, CardBody, Tabs, Tab } from '@heroui/react';

interface AndroidPWAProps {
  isOpen: boolean;
  onClose: () => void;
  onInstall?: () => void;
  hasInstallPrompt: boolean;
}

export default function AndroidPWA({ isOpen, onClose, onInstall, hasInstallPrompt }: AndroidPWAProps) {
  const [selectedTab, setSelectedTab] = useState('auto');

  const handleAutoInstall = async () => {
    if (onInstall) {
      await onInstall();
    }
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-xl font-bold">Android PWA 安装</h2>
          <p className="text-sm text-gray-600">将TRON钱包安装为原生应用</p>
        </ModalHeader>
        
        <ModalBody>
          <Tabs 
            selectedKey={selectedTab} 
            onSelectionChange={(key) => setSelectedTab(key as string)}
            className="w-full"
          >
            {/* 自动安装 */}
            {hasInstallPrompt && (
              <Tab key="auto" title="一键安装">
                <Card className="bg-green-50">
                  <CardBody className="text-center p-6">
                    <div className="text-4xl mb-4">🚀</div>
                    <h3 className="text-lg font-semibold mb-2">一键安装PWA应用</h3>
                    <p className="text-gray-600 mb-4">
                      您的浏览器支持PWA自动安装，点击下方按钮即可快速安装
                    </p>
                    
                    <Button
                      color="success"
                      size="lg"
                      onPress={handleAutoInstall}
                      className="w-full mb-4"
                      startContent={<span className="text-xl">⬇️</span>}
                    >
                      立即安装应用
                    </Button>
                    
                    <div className="text-sm text-green-700">
                      <p className="font-medium mb-2">安装后您将获得：</p>
                      <ul className="text-left space-y-1">
                        <li>• 独立的应用图标</li>
                        <li>• 全屏应用体验</li>
                        <li>• 快速启动速度</li>
                        <li>• 离线功能支持</li>
                        <li>• 系统通知推送</li>
                      </ul>
                    </div>
                  </CardBody>
                </Card>
              </Tab>
            )}
            
            {/* Chrome 安装 */}
            <Tab key="chrome" title="Chrome 安装">
              <div className="space-y-4">
                <Card>
                  <CardBody className="p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-xl">🌐</span>
                      方法一：地址栏安装
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>查看Chrome地址栏右侧的安装图标 ⬇️</li>
                      <li>点击安装图标</li>
                      <li>在弹出的对话框中点击"安装"</li>
                      <li>应用将添加到桌面和应用抽屉</li>
                    </ol>
                  </CardBody>
                </Card>

                <Card>
                  <CardBody className="p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-xl">⋮</span>
                      方法二：菜单安装
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>点击Chrome右上角的三点菜单 ⋮</li>
                      <li>选择"安装应用"或"添加到主屏幕"</li>
                      <li>确认应用名称为"TRON钱包"</li>
                      <li>点击"安装"完成安装</li>
                    </ol>
                  </CardBody>
                </Card>

                <Card className="bg-blue-50">
                  <CardBody className="p-4">
                    <h4 className="font-semibold text-blue-800 mb-2">💡 Chrome 安装提示</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• 确保Chrome版本在76以上</li>
                      <li>• 安装后可在应用抽屉中找到</li>
                      <li>• 支持桌面快捷方式</li>
                      <li>• 可以像原生应用一样使用</li>
                    </ul>
                  </CardBody>
                </Card>
              </div>
            </Tab>
            
            {/* 其他浏览器 */}
            <Tab key="other" title="其他浏览器">
              <div className="space-y-4">
                <Card>
                  <CardBody className="p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-xl">🦊</span>
                      Firefox 浏览器
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>点击地址栏右侧的"+"图标</li>
                      <li>选择"安装此站点为应用"</li>
                      <li>确认安装</li>
                    </ol>
                  </CardBody>
                </Card>

                <Card>
                  <CardBody className="p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-xl">🌐</span>
                      Edge 浏览器
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>点击地址栏右侧的安装图标</li>
                      <li>或通过菜单选择"应用" → "安装此站点为应用"</li>
                      <li>确认安装</li>
                    </ol>
                  </CardBody>
                </Card>

                <Card>
                  <CardBody className="p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      <span className="text-xl">🔧</span>
                      其他Android浏览器
                    </h4>
                    <ol className="list-decimal list-inside space-y-2 text-sm">
                      <li>查找浏览器菜单中的"添加到主屏幕"选项</li>
                      <li>或查看地址栏的安装提示</li>
                      <li>按照浏览器提示完成安装</li>
                    </ol>
                  </CardBody>
                </Card>

                <Card className="bg-yellow-50">
                  <CardBody className="p-4">
                    <h4 className="font-semibold text-yellow-800 mb-2">⚠️ 兼容性说明</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• 推荐使用Chrome获得最佳体验</li>
                      <li>• 部分浏览器可能不支持完整PWA功能</li>
                      <li>• 如遇问题，请尝试更新浏览器版本</li>
                    </ul>
                  </CardBody>
                </Card>
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
        
        <ModalFooter>
          <div className="flex justify-between w-full">
            <Button
              variant="bordered"
              onPress={onClose}
            >
              稍后安装
            </Button>
            
            <div className="flex gap-2">
              {hasInstallPrompt && selectedTab === 'auto' && (
                <Button
                  color="success"
                  onPress={handleAutoInstall}
                >
                  立即安装
                </Button>
              )}
              
              <Button
                variant="light"
                onPress={onClose}
              >
                关闭
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
