'use client';

import { useState, useEffect } from 'react';
import { 
  detectInstalledWallets, 
  checkWalletStatus, 
  walletManager, 
  isMobileDevice, 
  getMobileWalletLinks,
  type WalletInfo 
} from '@/lib/multi-wallet';

interface MultiWalletSelectorProps {
  onWalletConnected: (address: string, walletId: string) => void;
  onError: (error: string) => void;
}

export default function MultiWalletSelector({ onWalletConnected, onError }: MultiWalletSelectorProps) {
  const [wallets, setWallets] = useState<WalletInfo[]>([]);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [connectedWallet, setConnectedWallet] = useState<string | null>(null);
  const [userAddress, setUserAddress] = useState<string>('');
  const [showAllWallets, setShowAllWallets] = useState(false);

  useEffect(() => {
    updateWalletList();
    
    // 定期检查钱包状态
    const interval = setInterval(updateWalletList, 2000);
    return () => clearInterval(interval);
  }, []);

  const updateWalletList = () => {
    const installedWallets = detectInstalledWallets();
    setWallets(installedWallets);

    // 检查是否有已连接的钱包
    const connectedWalletId = walletManager.getCurrentWalletId();
    if (connectedWalletId && walletManager.isConnected()) {
      setConnectedWallet(connectedWalletId);
      
      // 获取地址
      const provider = walletManager.getCurrentProvider();
      if (provider) {
        provider.getAddress().then(address => {
          setUserAddress(address);
        }).catch(console.error);
      }
    }
  };

  const connectWallet = async (walletId: string) => {
    try {
      setConnecting(walletId);
      
      const address = await walletManager.connectWallet(walletId);
      
      setConnectedWallet(walletId);
      setUserAddress(address);
      onWalletConnected(address, walletId);
      
    } catch (error: any) {
      console.error('连接钱包失败:', error);
      onError(error.message || '连接钱包失败');
    } finally {
      setConnecting(null);
    }
  };

  const disconnectWallet = async () => {
    try {
      await walletManager.disconnect();
      setConnectedWallet(null);
      setUserAddress('');
    } catch (error) {
      console.error('断开钱包失败:', error);
    }
  };

  const openWalletDownload = (wallet: WalletInfo) => {
    if (isMobileDevice()) {
      const mobileLinks = getMobileWalletLinks();
      const isAndroid = /Android/i.test(navigator.userAgent);
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
      
      if (wallet.id === 'tronlink' && mobileLinks.tronlink) {
        if (isAndroid) {
          window.open(mobileLinks.tronlink.android, '_blank');
        } else if (isIOS) {
          window.open(mobileLinks.tronlink.ios, '_blank');
        } else {
          window.open(wallet.downloadUrl, '_blank');
        }
      } else if (wallet.id === 'klever' && mobileLinks.klever) {
        if (isAndroid) {
          window.open(mobileLinks.klever.android, '_blank');
        } else if (isIOS) {
          window.open(mobileLinks.klever.ios, '_blank');
        } else {
          window.open(wallet.downloadUrl, '_blank');
        }
      } else {
        window.open(wallet.downloadUrl, '_blank');
      }
    } else {
      window.open(wallet.downloadUrl, '_blank');
    }
  };

  const installedWallets = wallets.filter(w => w.isInstalled);
  const notInstalledWallets = wallets.filter(w => !w.isInstalled);
  const displayWallets = showAllWallets ? wallets : installedWallets.slice(0, 3);

  if (connectedWallet && userAddress) {
    const wallet = wallets.find(w => w.id === connectedWallet);
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">已连接钱包</h3>
          <button
            onClick={disconnectWallet}
            className="text-sm text-red-600 hover:text-red-700"
          >
            断开连接
          </button>
        </div>
        
        <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200">
          <span className="text-2xl">{wallet?.icon}</span>
          <div>
            <div className="font-medium text-green-900">{wallet?.name}</div>
            <div className="text-sm text-green-700 font-mono">
              {userAddress.slice(0, 6)}...{userAddress.slice(-4)}
            </div>
          </div>
          <div className="ml-auto">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              已连接
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">选择钱包</h3>
      
      {installedWallets.length > 0 ? (
        <div className="space-y-3 mb-4">
          <h4 className="text-sm font-medium text-gray-700">已安装的钱包</h4>
          {displayWallets.filter(w => w.isInstalled).map((wallet) => (
            <button
              key={wallet.id}
              onClick={() => connectWallet(wallet.id)}
              disabled={connecting === wallet.id}
              className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="text-2xl">{wallet.icon}</span>
              <div className="flex-1 text-left">
                <div className="font-medium text-gray-900">{wallet.name}</div>
                <div className="text-sm text-gray-500">
                  {wallet.isConnected ? '已连接' : '点击连接'}
                </div>
              </div>
              {connecting === wallet.id && (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              )}
              {wallet.isConnected && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  已连接
                </span>
              )}
            </button>
          ))}
        </div>
      ) : (
        <div className="text-center py-6">
          <div className="text-gray-500 mb-4">
            <span className="text-4xl">📱</span>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">未检测到钱包</h4>
          <p className="text-sm text-gray-600 mb-4">
            请安装支持的 TRON 钱包以继续支付
          </p>
        </div>
      )}

      {notInstalledWallets.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">推荐钱包</h4>
          {notInstalledWallets.slice(0, showAllWallets ? undefined : 3).map((wallet) => (
            <button
              key={wallet.id}
              onClick={() => openWalletDownload(wallet)}
              className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl opacity-50">{wallet.icon}</span>
              <div className="flex-1 text-left">
                <div className="font-medium text-gray-900">{wallet.name}</div>
                <div className="text-sm text-gray-500">点击安装</div>
              </div>
              <span className="text-sm text-blue-600">安装</span>
            </button>
          ))}
        </div>
      )}

      {wallets.length > 3 && (
        <button
          onClick={() => setShowAllWallets(!showAllWallets)}
          className="w-full mt-3 text-sm text-blue-600 hover:text-blue-700"
        >
          {showAllWallets ? '显示更少' : `显示更多钱包 (${wallets.length - 3})`}
        </button>
      )}

      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <div className="text-sm text-blue-800">
          <strong>提示：</strong> 
          {isMobileDevice() 
            ? '在移动设备上，建议使用钱包应用内的浏览器访问此页面'
            : '确保您的钱包扩展已启用并解锁'
          }
        </div>
      </div>
    </div>
  );
}
