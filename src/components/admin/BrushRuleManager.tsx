'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  <PERSON><PERSON>,
  Textarea,
  <PERSON>witch,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from "@heroui/react";

interface BrushRule {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  burst_probability: number;
  burst_order_range: string;
  min_commission_rate: number;
  max_commission_rate: number;
  daily_order_limit: number;
  min_order_interval: number;
  max_order_interval: number;
  config_json?: string;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

export default function BrushRuleManager() {
  const [rules, setRules] = useState<BrushRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingRule, setEditingRule] = useState<BrushRule | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const [form, setForm] = useState({
    name: '',
    description: '',
    isActive: true,
    burstProbability: 0.1,
    burstOrderRange: '3-7',
    minCommissionRate: 0.03,
    maxCommissionRate: 0.08,
    dailyOrderLimit: 50,
    minOrderInterval: 30,
    maxOrderInterval: 300
  });

  useEffect(() => {
    loadRules();
  }, []);

  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/brush-rules');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRules(result.data);
        }
      }
    } catch (error) {
      console.error('加载刷单规则失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingRule(null);
    setForm({
      name: '',
      description: '',
      isActive: true,
      burstProbability: 0.1,
      burstOrderRange: '3-7',
      minCommissionRate: 0.03,
      maxCommissionRate: 0.08,
      dailyOrderLimit: 50,
      minOrderInterval: 30,
      maxOrderInterval: 300
    });
    setShowModal(true);
  };

  const handleEdit = (rule: BrushRule) => {
    setEditingRule(rule);
    setForm({
      name: rule.name,
      description: rule.description || '',
      isActive: rule.is_active,
      burstProbability: rule.burst_probability,
      burstOrderRange: rule.burst_order_range,
      minCommissionRate: rule.min_commission_rate,
      maxCommissionRate: rule.max_commission_rate,
      dailyOrderLimit: rule.daily_order_limit,
      minOrderInterval: rule.min_order_interval,
      maxOrderInterval: rule.max_order_interval
    });
    setShowModal(true);
  };

  const handleSubmit = async () => {
    if (!form.name) {
      alert('规则名称不能为空');
      return;
    }

    if (form.minCommissionRate >= form.maxCommissionRate) {
      alert('最小佣金比例必须小于最大佣金比例');
      return;
    }

    if (form.minOrderInterval >= form.maxOrderInterval) {
      alert('最小下单间隔必须小于最大下单间隔');
      return;
    }

    if (!/^\d+-\d+$/.test(form.burstOrderRange)) {
      alert('爆单订单范围格式无效，应为 "数字-数字"');
      return;
    }

    try {
      setSubmitting(true);
      const url = '/api/admin/brush-rules';
      const method = editingRule ? 'PUT' : 'POST';
      
      const body: any = {
        name: form.name,
        description: form.description || undefined,
        burstProbability: form.burstProbability,
        burstOrderRange: form.burstOrderRange,
        minCommissionRate: form.minCommissionRate,
        maxCommissionRate: form.maxCommissionRate,
        dailyOrderLimit: form.dailyOrderLimit,
        minOrderInterval: form.minOrderInterval,
        maxOrderInterval: form.maxOrderInterval
      };

      if (editingRule) {
        body.id = editingRule.id;
        body.isActive = form.isActive;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert(editingRule ? '规则更新成功' : '规则创建成功');
          setShowModal(false);
          await loadRules();
        } else {
          alert(`操作失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert('操作失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">刷单规则配置</h2>
        <Button color="primary" onPress={handleCreate}>
          添加规则
        </Button>
      </div>

      <Card>
        <CardBody>
          <Table aria-label="刷单规则列表">
            <TableHeader>
              <TableColumn>规则名称</TableColumn>
              <TableColumn>爆单设置</TableColumn>
              <TableColumn>佣金比例</TableColumn>
              <TableColumn>订单限制</TableColumn>
              <TableColumn>状态</TableColumn>
              <TableColumn>创建时间</TableColumn>
              <TableColumn>操作</TableColumn>
            </TableHeader>
            <TableBody>
              {rules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{rule.name}</p>
                      {rule.description && (
                        <p className="text-sm text-gray-500">{rule.description}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>概率: {(rule.burst_probability * 100).toFixed(1)}%</p>
                      <p>范围: 第{rule.burst_order_range}单</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{(rule.min_commission_rate * 100).toFixed(1)}% - {(rule.max_commission_rate * 100).toFixed(1)}%</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>每日: {rule.daily_order_limit}单</p>
                      <p>间隔: {rule.min_order_interval}-{rule.max_order_interval}秒</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Chip
                      color={rule.is_active ? 'success' : 'default'}
                      variant="flat"
                      size="sm"
                    >
                      {rule.is_active ? '启用' : '禁用'}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {formatDate(rule.created_at)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button
                      size="sm"
                      variant="light"
                      onPress={() => handleEdit(rule)}
                    >
                      编辑
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* 添加/编辑规则模态框 */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">
              {editingRule ? '编辑规则' : '添加规则'}
            </h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="规则名称"
                  placeholder="请输入规则名称"
                  value={form.name}
                  onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                  isRequired
                />
                
                {editingRule && (
                  <div className="flex items-center gap-2">
                    <Switch
                      isSelected={form.isActive}
                      onValueChange={(checked) => setForm(prev => ({ ...prev, isActive: checked }))}
                    />
                    <span>启用规则</span>
                  </div>
                )}
              </div>
              
              <Textarea
                label="规则描述"
                placeholder="请输入规则描述"
                value={form.description}
                onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
              />

              <div className="space-y-4">
                <h4 className="font-semibold">爆单设置</h4>
                
                <div>
                  <label className="text-sm font-medium">爆单概率: {(form.burstProbability * 100).toFixed(1)}%</label>
                  <Slider
                    value={form.burstProbability * 100}
                    onChange={(value) => setForm(prev => ({ ...prev, burstProbability: (value as number) / 100 }))}
                    min={0}
                    max={50}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
                
                <Input
                  label="爆单订单范围"
                  placeholder="例如: 3-7"
                  value={form.burstOrderRange}
                  onChange={(e) => setForm(prev => ({ ...prev, burstOrderRange: e.target.value }))}
                  description="格式: 数字-数字，表示第几单到第几单可能爆单"
                />
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">佣金设置</h4>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">最小佣金比例: {(form.minCommissionRate * 100).toFixed(1)}%</label>
                    <Slider
                      value={form.minCommissionRate * 100}
                      onChange={(value) => setForm(prev => ({ ...prev, minCommissionRate: (value as number) / 100 }))}
                      min={1}
                      max={20}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">最大佣金比例: {(form.maxCommissionRate * 100).toFixed(1)}%</label>
                    <Slider
                      value={form.maxCommissionRate * 100}
                      onChange={(value) => setForm(prev => ({ ...prev, maxCommissionRate: (value as number) / 100 }))}
                      min={1}
                      max={20}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold">订单限制</h4>
                
                <Input
                  label="每日订单限制"
                  type="number"
                  value={form.dailyOrderLimit.toString()}
                  onChange={(e) => setForm(prev => ({ ...prev, dailyOrderLimit: parseInt(e.target.value) || 0 }))}
                  endContent={<span className="text-gray-500">单</span>}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="最小下单间隔"
                    type="number"
                    value={form.minOrderInterval.toString()}
                    onChange={(e) => setForm(prev => ({ ...prev, minOrderInterval: parseInt(e.target.value) || 0 }))}
                    endContent={<span className="text-gray-500">秒</span>}
                  />
                  
                  <Input
                    label="最大下单间隔"
                    type="number"
                    value={form.maxOrderInterval.toString()}
                    onChange={(e) => setForm(prev => ({ ...prev, maxOrderInterval: parseInt(e.target.value) || 0 }))}
                    endContent={<span className="text-gray-500">秒</span>}
                  />
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowModal(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              isLoading={submitting}
            >
              {editingRule ? '更新' : '创建'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
