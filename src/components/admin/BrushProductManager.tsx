'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Switch,
  Spinner
} from "@heroui/react";

interface Product {
  id: number;
  name: string;
  image?: string;
  price: number; // 已转换为元
  description?: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export default function BrushProductManager() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const [form, setForm] = useState({
    name: '',
    image: '',
    price: '',
    description: '',
    status: true
  });

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 转换价格为元
          const formattedProducts = result.data.map((product: any) => ({
            ...product,
            price: product.price / 100
          }));
          setProducts(formattedProducts);
        }
      }
    } catch (error) {
      console.error('加载商品失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingProduct(null);
    setForm({
      name: '',
      image: '',
      price: '',
      description: '',
      status: true
    });
    setShowModal(true);
  };

  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setForm({
      name: product.name,
      image: product.image || '',
      price: product.price.toString(),
      description: product.description || '',
      status: product.status === 1
    });
    setShowModal(true);
  };

  const handleSubmit = async () => {
    if (!form.name || !form.price) {
      alert('商品名称和价格不能为空');
      return;
    }

    const price = parseFloat(form.price);
    if (isNaN(price) || price <= 0) {
      alert('请输入有效的价格');
      return;
    }

    try {
      setSubmitting(true);
      const url = editingProduct ? '/api/products' : '/api/products';
      const method = editingProduct ? 'PUT' : 'POST';
      
      const body: any = {
        name: form.name,
        image: form.image || undefined,
        price: price,
        description: form.description || undefined,
        status: form.status ? 1 : 0
      };

      if (editingProduct) {
        body.id = editingProduct.id;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert(editingProduct ? '商品更新成功' : '商品创建成功');
          setShowModal(false);
          await loadProducts();
        } else {
          alert(`操作失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert('操作失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (product: Product) => {
    if (!confirm(`确定要删除商品"${product.name}"吗？`)) {
      return;
    }

    try {
      const response = await fetch(`/api/products?id=${product.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('商品删除成功');
          await loadProducts();
        } else {
          alert(`删除失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`删除失败: ${error.error}`);
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败，请重试');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">商品管理</h2>
        <Button color="primary" onPress={handleCreate}>
          添加商品
        </Button>
      </div>

      <Card>
        <CardBody>
          <Table aria-label="商品列表">
            <TableHeader>
              <TableColumn>商品信息</TableColumn>
              <TableColumn>价格</TableColumn>
              <TableColumn>状态</TableColumn>
              <TableColumn>创建时间</TableColumn>
              <TableColumn>操作</TableColumn>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      {product.image && (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                      )}
                      <div>
                        <p className="font-medium">{product.name}</p>
                        {product.description && (
                          <p className="text-sm text-gray-500 line-clamp-2">
                            {product.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-semibold text-red-600">
                      ¥{product.price.toFixed(2)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Chip
                      color={product.status === 1 ? 'success' : 'default'}
                      variant="flat"
                      size="sm"
                    >
                      {product.status === 1 ? '上架' : '下架'}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {formatDate(product.created_at)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="light"
                        onPress={() => handleEdit(product)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="light"
                        onPress={() => handleDelete(product)}
                      >
                        删除
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* 添加/编辑商品模态框 */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">
              {editingProduct ? '编辑商品' : '添加商品'}
            </h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="商品名称"
                placeholder="请输入商品名称"
                value={form.name}
                onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                isRequired
              />
              
              <Input
                label="商品图片"
                placeholder="请输入图片URL"
                value={form.image}
                onChange={(e) => setForm(prev => ({ ...prev, image: e.target.value }))}
              />
              
              <Input
                label="商品价格"
                placeholder="请输入价格（元）"
                type="number"
                value={form.price}
                onChange={(e) => setForm(prev => ({ ...prev, price: e.target.value }))}
                endContent={<span className="text-gray-500">元</span>}
                isRequired
              />
              
              <Textarea
                label="商品描述"
                placeholder="请输入商品描述"
                value={form.description}
                onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
              
              <div className="flex items-center gap-2">
                <Switch
                  isSelected={form.status}
                  onValueChange={(checked) => setForm(prev => ({ ...prev, status: checked }))}
                />
                <span>商品上架</span>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowModal(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              isLoading={submitting}
            >
              {editingProduct ? '更新' : '创建'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
