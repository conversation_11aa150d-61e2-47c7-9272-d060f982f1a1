'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Select,
  SelectItem,
  Spinner,
  Divider
} from "@heroui/react";

interface MonitorStatus {
  mainnet: {
    running: boolean;
    startTime: string | null;
  };
  nile: {
    running: boolean;
    startTime: string | null;
  };
}

export default function RechargeMonitorPanel() {
  const [status, setStatus] = useState<MonitorStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [operating, setOperating] = useState(false);
  const [showManualModal, setShowManualModal] = useState(false);
  const [submittingManual, setSubmittingManual] = useState(false);

  const [manualForm, setManualForm] = useState({
    txHash: '',
    userId: '',
    amount: '',
    fromAddress: '',
    toAddress: '',
    network: 'mainnet'
  });

  useEffect(() => {
    loadStatus();
    // 每30秒刷新一次状态
    const interval = setInterval(loadStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/recharge-monitor');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setStatus(result.data);
        }
      }
    } catch (error) {
      console.error('加载监控状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMonitorAction = async (action: 'start' | 'stop', network: 'mainnet' | 'nile') => {
    try {
      setOperating(true);
      const response = await fetch('/api/admin/recharge-monitor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, network }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert(result.message);
          await loadStatus();
        } else {
          alert(`操作失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('操作失败:', error);
      alert('操作失败，请重试');
    } finally {
      setOperating(false);
    }
  };

  const handleManualRecharge = async () => {
    if (!manualForm.txHash || !manualForm.userId || !manualForm.amount || !manualForm.fromAddress || !manualForm.toAddress) {
      alert('请填写完整的充值信息');
      return;
    }

    const amount = parseFloat(manualForm.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('请输入有效的充值金额');
      return;
    }

    const userId = parseInt(manualForm.userId);
    if (isNaN(userId) || userId <= 0) {
      alert('请输入有效的用户ID');
      return;
    }

    try {
      setSubmittingManual(true);
      const response = await fetch('/api/admin/recharge-monitor', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          txHash: manualForm.txHash,
          userId: userId,
          amount: amount,
          fromAddress: manualForm.fromAddress,
          toAddress: manualForm.toAddress,
          network: manualForm.network
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('手动充值处理成功');
          setShowManualModal(false);
          setManualForm({
            txHash: '',
            userId: '',
            amount: '',
            fromAddress: '',
            toAddress: '',
            network: 'mainnet'
          });
        } else {
          alert(`处理失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`处理失败: ${error.error}`);
      }
    } catch (error) {
      console.error('手动充值失败:', error);
      alert('处理失败，请重试');
    } finally {
      setSubmittingManual(false);
    }
  };

  const formatTime = (timeString: string | null) => {
    if (!timeString) return '-';
    return new Date(timeString).toLocaleString('zh-CN');
  };

  const getRunningTime = (startTime: string | null) => {
    if (!startTime) return '-';
    const start = new Date(startTime).getTime();
    const now = Date.now();
    const diff = now - start;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}小时${minutes}分钟`;
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">充值监控管理</h2>
        <div className="flex gap-3">
          <Button onPress={loadStatus}>
            刷新状态
          </Button>
          <Button color="primary" onPress={() => setShowManualModal(true)}>
            手动处理充值
          </Button>
        </div>
      </div>

      {/* 监控状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 主网监控 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center w-full">
              <h3 className="text-lg font-semibold">主网监控 (Mainnet)</h3>
              <Chip
                color={status?.mainnet.running ? 'success' : 'default'}
                variant="flat"
              >
                {status?.mainnet.running ? '运行中' : '已停止'}
              </Chip>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">启动时间:</p>
                  <p className="font-medium">{formatTime(status?.mainnet.startTime || null)}</p>
                </div>
                <div>
                  <p className="text-gray-500">运行时长:</p>
                  <p className="font-medium">{getRunningTime(status?.mainnet.startTime || null)}</p>
                </div>
              </div>
              
              <Divider />
              
              <div className="flex gap-2">
                {status?.mainnet.running ? (
                  <Button
                    color="danger"
                    size="sm"
                    onPress={() => handleMonitorAction('stop', 'mainnet')}
                    isLoading={operating}
                  >
                    停止监控
                  </Button>
                ) : (
                  <Button
                    color="success"
                    size="sm"
                    onPress={() => handleMonitorAction('start', 'mainnet')}
                    isLoading={operating}
                  >
                    启动监控
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 测试网监控 */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center w-full">
              <h3 className="text-lg font-semibold">测试网监控 (Nile)</h3>
              <Chip
                color={status?.nile.running ? 'success' : 'default'}
                variant="flat"
              >
                {status?.nile.running ? '运行中' : '已停止'}
              </Chip>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">启动时间:</p>
                  <p className="font-medium">{formatTime(status?.nile.startTime || null)}</p>
                </div>
                <div>
                  <p className="text-gray-500">运行时长:</p>
                  <p className="font-medium">{getRunningTime(status?.nile.startTime || null)}</p>
                </div>
              </div>
              
              <Divider />
              
              <div className="flex gap-2">
                {status?.nile.running ? (
                  <Button
                    color="danger"
                    size="sm"
                    onPress={() => handleMonitorAction('stop', 'nile')}
                    isLoading={operating}
                  >
                    停止监控
                  </Button>
                ) : (
                  <Button
                    color="success"
                    size="sm"
                    onPress={() => handleMonitorAction('start', 'nile')}
                    isLoading={operating}
                  >
                    启动监控
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 监控说明 */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">监控说明</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-3 text-sm">
            <p>• 充值监控服务会实时监控TRON区块链上的USDT转账交易</p>
            <p>• 当检测到转账到平台钱包地址的交易时，会自动为用户充值到内部账户</p>
            <p>• 监控服务每30秒检查一次新区块，确保及时处理充值</p>
            <p>• 建议在生产环境中保持主网监控运行，测试网监控可按需启动</p>
            <p>• 如果自动监控出现问题，可以使用"手动处理充值"功能</p>
          </div>
        </CardBody>
      </Card>

      {/* 手动处理充值模态框 */}
      <Modal
        isOpen={showManualModal}
        onClose={() => setShowManualModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">手动处理充值</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  💡 当自动监控未能及时处理充值时，可以使用此功能手动处理用户充值。
                </p>
              </div>

              <Input
                label="交易哈希"
                placeholder="请输入USDT转账的交易哈希"
                value={manualForm.txHash}
                onChange={(e) => setManualForm(prev => ({ ...prev, txHash: e.target.value }))}
                isRequired
              />

              <Input
                label="用户ID"
                placeholder="请输入用户ID"
                type="number"
                value={manualForm.userId}
                onChange={(e) => setManualForm(prev => ({ ...prev, userId: e.target.value }))}
                isRequired
              />

              <Input
                label="充值金额"
                placeholder="请输入USDT金额"
                type="number"
                value={manualForm.amount}
                onChange={(e) => setManualForm(prev => ({ ...prev, amount: e.target.value }))}
                endContent={<span className="text-gray-500">USDT</span>}
                isRequired
              />

              <Input
                label="发送地址"
                placeholder="请输入用户钱包地址"
                value={manualForm.fromAddress}
                onChange={(e) => setManualForm(prev => ({ ...prev, fromAddress: e.target.value }))}
                isRequired
              />

              <Input
                label="接收地址"
                placeholder="请输入平台钱包地址"
                value={manualForm.toAddress}
                onChange={(e) => setManualForm(prev => ({ ...prev, toAddress: e.target.value }))}
                isRequired
              />

              <Select
                label="网络"
                selectedKeys={[manualForm.network]}
                onSelectionChange={(keys) => setManualForm(prev => ({ ...prev, network: Array.from(keys)[0] as string }))}
              >
                <SelectItem key="mainnet" value="mainnet">主网 (Mainnet)</SelectItem>
                <SelectItem key="nile" value="nile">测试网 (Nile)</SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowManualModal(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleManualRecharge}
              isLoading={submittingManual}
            >
              处理充值
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
