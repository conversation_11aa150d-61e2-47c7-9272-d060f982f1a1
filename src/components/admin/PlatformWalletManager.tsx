'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Select,
  SelectItem,
  Switch,
  Textarea,
  Spinner
} from "@heroui/react";

interface PlatformWallet {
  id: number;
  name: string;
  address: string;
  network: string;
  currency: string;
  is_active: boolean;
  is_default: boolean;
  description?: string;
  created_at: string;
  updated_at: string;
}

export default function PlatformWalletManager() {
  const [wallets, setWallets] = useState<PlatformWallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingWallet, setEditingWallet] = useState<PlatformWallet | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const [form, setForm] = useState({
    name: '',
    address: '',
    network: 'mainnet',
    currency: 'USDT',
    isActive: true,
    isDefault: false,
    description: ''
  });

  useEffect(() => {
    loadWallets();
  }, []);

  const loadWallets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/platform-wallets');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setWallets(result.data);
        }
      }
    } catch (error) {
      console.error('加载平台钱包失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingWallet(null);
    setForm({
      name: '',
      address: '',
      network: 'mainnet',
      currency: 'USDT',
      isActive: true,
      isDefault: false,
      description: ''
    });
    setShowModal(true);
  };

  const handleEdit = (wallet: PlatformWallet) => {
    setEditingWallet(wallet);
    setForm({
      name: wallet.name,
      address: wallet.address,
      network: wallet.network,
      currency: wallet.currency,
      isActive: wallet.is_active,
      isDefault: wallet.is_default,
      description: wallet.description || ''
    });
    setShowModal(true);
  };

  const handleSubmit = async () => {
    if (!form.name || !form.address) {
      alert('钱包名称和地址不能为空');
      return;
    }

    try {
      setSubmitting(true);
      const url = '/api/admin/platform-wallets';
      const method = editingWallet ? 'PUT' : 'POST';
      
      const body: any = {
        name: form.name,
        address: form.address,
        network: form.network,
        currency: form.currency,
        isActive: form.isActive,
        isDefault: form.isDefault,
        description: form.description || undefined
      };

      if (editingWallet) {
        body.id = editingWallet.id;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert(editingWallet ? '钱包更新成功' : '钱包创建成功');
          setShowModal(false);
          await loadWallets();
        } else {
          alert(`操作失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.error}`);
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert('操作失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (wallet: PlatformWallet) => {
    if (!confirm(`确定要删除钱包"${wallet.name}"吗？`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/platform-wallets?id=${wallet.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('钱包删除成功');
          await loadWallets();
        } else {
          alert(`删除失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`删除失败: ${error.error}`);
      }
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败，请重试');
    }
  };

  const networkOptions = [
    { key: 'mainnet', label: '主网 (Mainnet)' },
    { key: 'nile', label: '测试网 (Nile)' },
    { key: 'shasta', label: '测试网 (Shasta)' }
  ];

  const currencyOptions = [
    { key: 'USDT', label: 'USDT' },
    { key: 'TRX', label: 'TRX' }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">平台钱包管理</h2>
        <Button color="primary" onPress={handleCreate}>
          添加钱包
        </Button>
      </div>

      <Card>
        <CardBody>
          <Table aria-label="平台钱包列表">
            <TableHeader>
              <TableColumn>钱包信息</TableColumn>
              <TableColumn>地址</TableColumn>
              <TableColumn>网络/币种</TableColumn>
              <TableColumn>状态</TableColumn>
              <TableColumn>创建时间</TableColumn>
              <TableColumn>操作</TableColumn>
            </TableHeader>
            <TableBody>
              {wallets.map((wallet) => (
                <TableRow key={wallet.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{wallet.name}</p>
                      {wallet.description && (
                        <p className="text-sm text-gray-500">{wallet.description}</p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">
                      <p>{wallet.address.slice(0, 10)}...{wallet.address.slice(-8)}</p>
                      <Button
                        size="sm"
                        variant="light"
                        onPress={() => navigator.clipboard.writeText(wallet.address)}
                        className="text-xs p-0 h-auto"
                      >
                        复制完整地址
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <Chip size="sm" variant="flat" color="primary">
                        {wallet.network}
                      </Chip>
                      <Chip size="sm" variant="flat" color="secondary" className="ml-1">
                        {wallet.currency}
                      </Chip>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <Chip
                        color={wallet.is_active ? 'success' : 'default'}
                        variant="flat"
                        size="sm"
                      >
                        {wallet.is_active ? '启用' : '禁用'}
                      </Chip>
                      {wallet.is_default && (
                        <Chip color="warning" variant="flat" size="sm">
                          默认
                        </Chip>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-500">
                      {formatDate(wallet.created_at)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="light"
                        onPress={() => handleEdit(wallet)}
                      >
                        编辑
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="light"
                        onPress={() => handleDelete(wallet)}
                      >
                        删除
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* 添加/编辑钱包模态框 */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">
              {editingWallet ? '编辑钱包' : '添加钱包'}
            </h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="钱包名称"
                placeholder="请输入钱包名称"
                value={form.name}
                onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                isRequired
              />
              
              <Input
                label="钱包地址"
                placeholder="请输入TRON钱包地址"
                value={form.address}
                onChange={(e) => setForm(prev => ({ ...prev, address: e.target.value }))}
                isRequired
              />
              
              <div className="grid grid-cols-2 gap-4">
                <Select
                  label="网络"
                  selectedKeys={[form.network]}
                  onSelectionChange={(keys) => setForm(prev => ({ ...prev, network: Array.from(keys)[0] as string }))}
                  isDisabled={!!editingWallet}
                >
                  {networkOptions.map((option) => (
                    <SelectItem key={option.key} value={option.key}>
                      {option.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  label="币种"
                  selectedKeys={[form.currency]}
                  onSelectionChange={(keys) => setForm(prev => ({ ...prev, currency: Array.from(keys)[0] as string }))}
                  isDisabled={!!editingWallet}
                >
                  {currencyOptions.map((option) => (
                    <SelectItem key={option.key} value={option.key}>
                      {option.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              
              <Textarea
                label="描述"
                placeholder="请输入钱包描述（可选）"
                value={form.description}
                onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
              />
              
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <Switch
                    isSelected={form.isActive}
                    onValueChange={(checked) => setForm(prev => ({ ...prev, isActive: checked }))}
                  />
                  <span>启用钱包</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Switch
                    isSelected={form.isDefault}
                    onValueChange={(checked) => setForm(prev => ({ ...prev, isDefault: checked }))}
                  />
                  <span>设为默认钱包</span>
                </div>
              </div>
              
              {form.isDefault && (
                <div className="bg-yellow-50 p-3 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    ⚠️ 设为默认钱包后，该网络和币种的其他默认钱包将被取消默认状态。
                  </p>
                </div>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowModal(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleSubmit}
              isLoading={submitting}
            >
              {editingWallet ? '更新' : '创建'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
