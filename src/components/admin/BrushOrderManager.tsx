'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Input,
  Select,
  SelectItem,
  Pagination,
  Modal,
  ModalContent,
  <PERSON>dalHeader,
  ModalBody,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON><PERSON>a,
  Spinner
} from "@heroui/react";

interface BrushOrder {
  id: number;
  user_id: number;
  username: string;
  user_name: string;
  order_no: string;
  product_name: string;
  product_image?: string;
  product_price: number;
  quantity: number;
  total_amount: number;
  commission_rate: number;
  commission_amount: number;
  status: number;
  status_name: string;
  is_burst: boolean;
  burst_reason?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

interface OrderListResponse {
  orders: BrushOrder[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export default function BrushOrderManager() {
  const [orders, setOrders] = useState<BrushOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);

  // 筛选条件
  const [filters, setFilters] = useState({
    status: '',
    username: '',
    productName: '',
    startDate: '',
    endDate: ''
  });

  // 模态框状态
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<BrushOrder | null>(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusReason, setStatusReason] = useState('');
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    loadOrders();
  }, [page, filters]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await fetch(`/api/admin/brush-orders?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const data: OrderListResponse = result.data;
          setOrders(data.orders);
          setTotal(data.total);
          setTotalPages(data.totalPages);
        }
      }
    } catch (error) {
      console.error('加载订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (order: BrushOrder) => {
    setSelectedOrder(order);
    setNewStatus(order.status.toString());
    setStatusReason('');
    setShowStatusModal(true);
  };

  const handleUpdateStatus = async () => {
    if (!selectedOrder || !newStatus) return;

    try {
      setUpdating(true);
      const response = await fetch('/api/admin/brush-orders', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: selectedOrder.id,
          status: parseInt(newStatus),
          reason: statusReason || undefined
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('订单状态更新成功');
          setShowStatusModal(false);
          await loadOrders();
        } else {
          alert(`更新失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`更新失败: ${error.error}`);
      }
    } catch (error) {
      console.error('更新订单状态失败:', error);
      alert('更新失败，请重试');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'warning'; // 待付款
      case 1: return 'primary'; // 已付款
      case 2: return 'success'; // 已完成
      case 3: return 'default'; // 已取消
      case 4: return 'danger';  // 爆单
      default: return 'default';
    }
  };

  const statusOptions = [
    { key: '', label: '全部状态' },
    { key: '0', label: '待付款' },
    { key: '1', label: '已付款' },
    { key: '2', label: '已完成' },
    { key: '3', label: '已取消' },
    { key: '4', label: '爆单' }
  ];

  const statusUpdateOptions = [
    { key: '0', label: '待付款' },
    { key: '1', label: '已付款' },
    { key: '2', label: '已完成' },
    { key: '3', label: '已取消' },
    { key: '4', label: '爆单' }
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">订单管理</h2>
        <Button onPress={loadOrders}>
          刷新
        </Button>
      </div>

      {/* 筛选条件 */}
      <Card>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Select
              label="订单状态"
              selectedKeys={[filters.status]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, status: Array.from(keys)[0] as string }))}
            >
              {statusOptions.map((option) => (
                <SelectItem key={option.key} value={option.key}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
            
            <Input
              label="用户名"
              placeholder="搜索用户名"
              value={filters.username}
              onChange={(e) => setFilters(prev => ({ ...prev, username: e.target.value }))}
            />
            
            <Input
              label="商品名称"
              placeholder="搜索商品名称"
              value={filters.productName}
              onChange={(e) => setFilters(prev => ({ ...prev, productName: e.target.value }))}
            />
            
            <Input
              label="开始日期"
              type="date"
              value={filters.startDate}
              onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
            />
            
            <Input
              label="结束日期"
              type="date"
              value={filters.endDate}
              onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
            />
          </div>
        </CardBody>
      </Card>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center w-full">
            <h3 className="text-lg font-semibold">订单列表</h3>
            <span className="text-sm text-gray-500">共 {total} 条记录</span>
          </div>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="flex justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : (
            <>
              <Table aria-label="订单列表">
                <TableHeader>
                  <TableColumn>订单信息</TableColumn>
                  <TableColumn>用户</TableColumn>
                  <TableColumn>商品</TableColumn>
                  <TableColumn>金额</TableColumn>
                  <TableColumn>状态</TableColumn>
                  <TableColumn>时间</TableColumn>
                  <TableColumn>操作</TableColumn>
                </TableHeader>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{order.order_no}</p>
                          <p className="text-sm text-gray-500">ID: {order.id}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{order.user_name || order.username}</p>
                          <p className="text-sm text-gray-500">@{order.username}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          {/* 商品图片 */}
                          {order.product_image ? (
                            <img
                              src={order.product_image}
                              alt={order.product_name}
                              className="w-12 h-12 object-cover rounded-lg border border-gray-200"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <div className={`w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg border border-gray-200 flex items-center justify-center ${order.product_image ? 'hidden' : ''}`}>
                            <span className="text-lg">📦</span>
                          </div>

                          {/* 商品信息 */}
                          <div>
                            <p className="font-medium">{order.product_name}</p>
                            <p className="text-sm text-gray-500">
                              ¥{order.product_price.toFixed(2)} × {order.quantity}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">¥{order.total_amount.toFixed(2)}</p>
                          <p className="text-sm text-green-600">
                            佣金: ¥{order.commission_amount.toFixed(2)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Chip
                          color={getStatusColor(order.status)}
                          variant="flat"
                          size="sm"
                        >
                          {order.status_name}
                        </Chip>
                        {order.is_burst && (
                          <p className="text-xs text-red-600 mt-1">💥 爆单</p>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p>{formatDate(order.created_at)}</p>
                          {order.completed_at && (
                            <p className="text-gray-500">
                              完成: {formatDate(order.completed_at)}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="light"
                          onPress={() => handleStatusChange(order)}
                        >
                          修改状态
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-4">
                  <Pagination
                    total={totalPages}
                    page={page}
                    onChange={setPage}
                  />
                </div>
              )}
            </>
          )}
        </CardBody>
      </Card>

      {/* 修改状态模态框 */}
      <Modal
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        size="md"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">修改订单状态</h3>
          </ModalHeader>
          <ModalBody>
            {selectedOrder && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm text-gray-600">订单号: {selectedOrder.order_no}</p>
                  <p className="text-sm text-gray-600">商品: {selectedOrder.product_name}</p>
                  <p className="text-sm text-gray-600">用户: {selectedOrder.username}</p>
                  <p className="text-sm text-gray-600">当前状态: {selectedOrder.status_name}</p>
                </div>

                <Select
                  label="新状态"
                  selectedKeys={[newStatus]}
                  onSelectionChange={(keys) => setNewStatus(Array.from(keys)[0] as string)}
                >
                  {statusUpdateOptions.map((option) => (
                    <SelectItem key={option.key} value={option.key}>
                      {option.label}
                    </SelectItem>
                  ))}
                </Select>

                <Textarea
                  label="备注原因"
                  placeholder="请输入修改原因（可选）"
                  value={statusReason}
                  onChange={(e) => setStatusReason(e.target.value)}
                  rows={3}
                />
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowStatusModal(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleUpdateStatus}
              isLoading={updating}
            >
              确认修改
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
