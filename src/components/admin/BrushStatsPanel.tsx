'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  <PERSON>ner,
  Button,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Progress
} from "@heroui/react";

interface StatsOverview {
  orders: {
    total: number;
    completed: number;
    burst: number;
    completionRate: string;
    burstRate: string;
  };
  revenue: {
    totalAmount: number;
    totalCommission: number;
    avgCommissionRate: string;
  };
  users: {
    activeUsers: number;
    avgDailyOrders: number;
  };
  balance: {
    totalBalance: number;
    totalRecharged: number;
    totalSpent: number;
  };
}

interface UserRanking {
  username: string;
  name: string;
  orderCount: number;
  totalSpent: number;
  totalCommission: number;
  burstCount: number;
  burstRate: string;
}

interface ProductStats {
  productName: string;
  orderCount: number;
  totalSales: number;
  completedCount: number;
  burstCount: number;
  completionRate: string;
}

interface RechargeStats {
  totalRecharges: number;
  confirmedRecharges: number;
  confirmationRate: string;
  totalUSDT: number;
  totalInternal: number;
  avgRechargeAmount: number;
}

export default function BrushStatsPanel() {
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('today');
  const [overview, setOverview] = useState<StatsOverview | null>(null);
  const [userRanking, setUserRanking] = useState<UserRanking[]>([]);
  const [productStats, setProductStats] = useState<ProductStats[]>([]);
  const [rechargeStats, setRechargeStats] = useState<RechargeStats | null>(null);

  useEffect(() => {
    loadStats();
  }, [period]);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/brush-stats?period=${period}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setOverview(result.data.overview);
          setUserRanking(result.data.userRanking);
          setProductStats(result.data.productStats);
          setRechargeStats(result.data.rechargeStats);
        }
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const periodOptions = [
    { key: 'today', label: '今日' },
    { key: 'week', label: '本周' },
    { key: 'month', label: '本月' },
    { key: 'all', label: '全部' }
  ];

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 时间筛选 */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">数据统计</h2>
        <div className="flex gap-4 items-center">
          <Select
            label="统计周期"
            selectedKeys={[period]}
            onSelectionChange={(keys) => setPeriod(Array.from(keys)[0] as string)}
            className="w-32"
            size="sm"
          >
            {periodOptions.map((option) => (
              <SelectItem key={option.key} value={option.key}>
                {option.label}
              </SelectItem>
            ))}
          </Select>
          <Button size="sm" onPress={loadStats}>
            刷新
          </Button>
        </div>
      </div>

      {/* 概览卡片 */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardBody>
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600">{overview.orders.total}</p>
                <p className="text-sm text-gray-500">总订单数</p>
                <div className="mt-2">
                  <p className="text-xs text-green-600">完成率: {overview.orders.completionRate}%</p>
                  <p className="text-xs text-red-600">爆单率: {overview.orders.burstRate}%</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600">¥{overview.revenue.totalAmount.toFixed(2)}</p>
                <p className="text-sm text-gray-500">总交易额</p>
                <div className="mt-2">
                  <p className="text-xs text-blue-600">佣金: ¥{overview.revenue.totalCommission.toFixed(2)}</p>
                  <p className="text-xs text-gray-600">平均佣金率: {overview.revenue.avgCommissionRate}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="text-center">
                <p className="text-3xl font-bold text-purple-600">{overview.users.activeUsers}</p>
                <p className="text-sm text-gray-500">活跃用户</p>
                <div className="mt-2">
                  <p className="text-xs text-gray-600">平均日订单: {overview.users.avgDailyOrders.toFixed(1)}</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <div className="text-center">
                <p className="text-3xl font-bold text-orange-600">¥{overview.balance.totalBalance.toFixed(2)}</p>
                <p className="text-sm text-gray-500">用户总余额</p>
                <div className="mt-2">
                  <p className="text-xs text-green-600">总充值: ¥{overview.balance.totalRecharged.toFixed(2)}</p>
                  <p className="text-xs text-red-600">总消费: ¥{overview.balance.totalSpent.toFixed(2)}</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* 充值统计 */}
      {rechargeStats && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">充值统计</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <p className="text-xl font-bold">{rechargeStats.totalRecharges}</p>
                <p className="text-sm text-gray-500">总充值次数</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-green-600">{rechargeStats.confirmedRecharges}</p>
                <p className="text-sm text-gray-500">已确认</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold">{rechargeStats.confirmationRate}</p>
                <p className="text-sm text-gray-500">确认率</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-blue-600">{rechargeStats.totalUSDT.toFixed(2)}</p>
                <p className="text-sm text-gray-500">总USDT</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold text-purple-600">¥{rechargeStats.totalInternal.toFixed(2)}</p>
                <p className="text-sm text-gray-500">内部币</p>
              </div>
              <div className="text-center">
                <p className="text-xl font-bold">{rechargeStats.avgRechargeAmount.toFixed(2)}</p>
                <p className="text-sm text-gray-500">平均充值</p>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用户排行榜 */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">用户排行榜（按佣金收入）</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="用户排行榜">
              <TableHeader>
                <TableColumn>用户</TableColumn>
                <TableColumn>订单数</TableColumn>
                <TableColumn>佣金收入</TableColumn>
                <TableColumn>爆单率</TableColumn>
              </TableHeader>
              <TableBody>
                {userRanking.map((user, index) => (
                  <TableRow key={user.username}>
                    <TableCell>
                      <div>
                        <p className="font-medium">#{index + 1} {user.name}</p>
                        <p className="text-xs text-gray-500">@{user.username}</p>
                      </div>
                    </TableCell>
                    <TableCell>{user.orderCount}</TableCell>
                    <TableCell>
                      <span className="text-green-600 font-medium">
                        ¥{user.totalCommission.toFixed(2)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Chip
                        color={parseFloat(user.burstRate) > 20 ? 'danger' : 'success'}
                        variant="flat"
                        size="sm"
                      >
                        {user.burstRate}
                      </Chip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>

        {/* 商品销售统计 */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">商品销售统计</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="商品销售统计">
              <TableHeader>
                <TableColumn>商品名称</TableColumn>
                <TableColumn>订单数</TableColumn>
                <TableColumn>销售额</TableColumn>
                <TableColumn>完成率</TableColumn>
              </TableHeader>
              <TableBody>
                {productStats.map((product) => (
                  <TableRow key={product.productName}>
                    <TableCell>
                      <p className="font-medium">{product.productName}</p>
                    </TableCell>
                    <TableCell>{product.orderCount}</TableCell>
                    <TableCell>
                      <span className="text-blue-600 font-medium">
                        ¥{product.totalSales.toFixed(2)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress
                          value={parseFloat(product.completionRate)}
                          className="w-16"
                          size="sm"
                        />
                        <span className="text-sm">{product.completionRate}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
