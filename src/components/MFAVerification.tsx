'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON>ton, Input, Card, CardBody
} from '@heroui/react';

interface MFAVerificationProps {
  isOpen: boolean;
  onClose: () => void;
  onVerify: (code: string) => Promise<boolean>;
  title?: string;
  description?: string;
  isLoading?: boolean;
}

export default function MFAVerification({
  isOpen,
  onClose,
  onVerify,
  title = "MFA验证",
  description = "此操作需要多因素认证，请输入您的验证码",
  isLoading = false
}: MFAVerificationProps) {
  const [code, setCode] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [error, setError] = useState('');

  const handleVerify = async () => {
    if (!code || code.length !== 6) {
      setError('请输入6位验证码');
      return;
    }

    try {
      setVerifying(true);
      setError('');
      
      const success = await onVerify(code);
      
      if (success) {
        setCode('');
        onClose();
      } else {
        setError('验证码无效，请重新输入');
      }
    } catch (error) {
      console.error('MFA验证失败:', error);
      setError('验证失败，请重试');
    } finally {
      setVerifying(false);
    }
  };

  const handleClose = () => {
    setCode('');
    setError('');
    onClose();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && code.length === 6) {
      handleVerify();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="md"
      isDismissable={false}
      hideCloseButton={isLoading}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <span className="text-2xl">🔐</span>
            <span>{title}</span>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <p className="text-gray-600">
              {description}
            </p>
            
            <Card className="bg-blue-50">
              <CardBody className="py-3">
                <div className="flex items-start gap-2">
                  <span className="text-blue-600 text-lg">💡</span>
                  <div className="text-blue-700 text-sm">
                    <p className="font-medium mb-1">如何获取验证码：</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>打开您的认证应用（如Google Authenticator）</li>
                      <li>找到Virtual Delivery System的条目</li>
                      <li>输入显示的6位数字验证码</li>
                    </ul>
                  </div>
                </div>
              </CardBody>
            </Card>

            <div className="space-y-2">
              <Input
                label="验证码"
                placeholder="输入6位验证码"
                value={code}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                  setCode(value);
                  if (error) setError('');
                }}
                onKeyPress={handleKeyPress}
                maxLength={6}
                isInvalid={!!error}
                errorMessage={error}
                autoFocus
                size="lg"
                className="font-mono text-center"
              />
              
              {code.length > 0 && code.length < 6 && (
                <p className="text-sm text-gray-500">
                  还需要输入 {6 - code.length} 位数字
                </p>
              )}
            </div>

            <div className="bg-amber-50 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <span className="text-amber-600 text-lg">⚠️</span>
                <div className="text-amber-700 text-sm">
                  <p className="font-medium mb-1">安全提醒：</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>验证码每30秒更新一次</li>
                    <li>请勿与他人分享您的验证码</li>
                    <li>如果多次验证失败，请检查设备时间是否正确</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="danger"
            variant="light"
            onPress={handleClose}
            isDisabled={verifying || isLoading}
          >
            取消
          </Button>
          <Button
            color="primary"
            onPress={handleVerify}
            isLoading={verifying || isLoading}
            isDisabled={code.length !== 6}
          >
            验证
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
