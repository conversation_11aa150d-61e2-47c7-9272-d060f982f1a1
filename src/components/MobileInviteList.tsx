'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON>, Divider } from '@heroui/react';

interface InviteCode {
  id: number;
  code: string;
  created_by?: number;
  used_by?: number;
  is_active: boolean;
  max_uses: number;
  used_count: number;
  expires_at?: string;
  created_at: string;
  used_at?: string;
  creator_username?: string;
  user_username?: string;
}

interface MobileInviteListProps {
  inviteCodes: InviteCode[];
}

export default function MobileInviteList({ inviteCodes }: MobileInviteListProps) {
  const getStatusColor = (inviteCode: InviteCode) => {
    if (!inviteCode.is_active) return 'default';
    if (inviteCode.expires_at && new Date(inviteCode.expires_at) < new Date()) return 'danger';
    if (inviteCode.max_uses !== -1 && inviteCode.used_count >= inviteCode.max_uses) return 'warning';
    return 'success';
  };

  const getStatusText = (inviteCode: InviteCode) => {
    if (!inviteCode.is_active) return '已禁用';
    if (inviteCode.expires_at && new Date(inviteCode.expires_at) < new Date()) return '已过期';
    if (inviteCode.max_uses !== -1 && inviteCode.used_count >= inviteCode.max_uses) return '已用完';
    return '可用';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('邀请码已复制到剪贴板');
    } catch (error) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('邀请码已复制到剪贴板');
    }
  };

  return (
    <div className="space-y-3">
      {inviteCodes.map((invite) => (
        <Card key={invite.id} className="w-full">
          <CardBody className="p-4">
            {/* 邀请码基本信息 */}
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold text-lg font-mono">{invite.code}</h3>
                  <Chip
                    size="sm"
                    color={getStatusColor(invite)}
                    variant="flat"
                  >
                    {getStatusText(invite)}
                  </Chip>
                </div>
                
                {invite.creator_username && (
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm text-gray-500">创建者:</span>
                    <span className="text-sm font-medium">{invite.creator_username}</span>
                  </div>
                )}
              </div>
              
              {/* 复制按钮 */}
              <Button
                size="sm"
                color="primary"
                variant="light"
                onPress={() => copyToClipboard(invite.code)}
                className="min-w-16"
              >
                复制
              </Button>
            </div>

            <Divider className="my-3" />

            {/* 使用情况 */}
            <div className="grid grid-cols-2 gap-4 text-sm mb-3">
              <div>
                <span className="text-gray-500">使用次数:</span>
                <p className="font-medium">
                  {invite.used_count} / {invite.max_uses === -1 ? '无限' : invite.max_uses}
                </p>
              </div>
              
              <div>
                <span className="text-gray-500">创建时间:</span>
                <p className="font-medium">
                  {new Date(invite.created_at).toLocaleDateString('zh-CN')}
                </p>
              </div>
              
              {invite.expires_at && (
                <div className="col-span-2">
                  <span className="text-gray-500">过期时间:</span>
                  <p className="font-medium">
                    {new Date(invite.expires_at).toLocaleString('zh-CN')}
                  </p>
                </div>
              )}
            </div>

            {/* 使用记录 */}
            {invite.used_by && invite.user_username && (
              <>
                <Divider className="my-3" />
                <div className="bg-gray-50 p-3 rounded-lg">
                  <h4 className="text-sm font-semibold text-gray-700 mb-2">使用记录</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">使用者:</span>
                      <span className="font-medium">{invite.user_username}</span>
                    </div>
                    {invite.used_at && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">使用时间:</span>
                        <span className="font-medium">
                          {new Date(invite.used_at).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* 进度条 */}
            {invite.max_uses !== -1 && (
              <>
                <Divider className="my-3" />
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-500">使用进度</span>
                    <span className="font-medium">
                      {Math.round((invite.used_count / invite.max_uses) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min((invite.used_count / invite.max_uses) * 100, 100)}%`
                      }}
                    ></div>
                  </div>
                </div>
              </>
            )}
          </CardBody>
        </Card>
      ))}
      
      {inviteCodes.length === 0 && (
        <Card>
          <CardBody className="text-center py-8">
            <p className="text-gray-500">暂无邀请码数据</p>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
