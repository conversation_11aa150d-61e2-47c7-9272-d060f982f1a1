'use client';

import { useEffect, useState } from 'react';

export default function PWAInstaller() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    if ('serviceWorker' in navigator) {
      // 注册Service Worker
      navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })
      .then((registration) => {
        console.log('Service Worker registered successfully:', registration);
        
        // 检查更新
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // 有新版本可用
                console.log('New version available');
                
                // 可以在这里显示更新提示
                if (confirm('发现新版本，是否立即更新？')) {
                  newWorker.postMessage({ type: 'SKIP_WAITING' });
                  window.location.reload();
                }
              }
            });
          }
        });
      })
      .catch((error) => {
        console.error('Service Worker registration failed:', error);
      });

      // 监听Service Worker控制器变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker controller changed');
        window.location.reload();
      });
    }

    // 监听应用安装事件
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      
      // 可以在这里发送安装成功的分析事件
      if (typeof gtag !== 'undefined') {
        gtag('event', 'pwa_install', {
          event_category: 'engagement',
          event_label: 'PWA安装成功'
        });
      }
    });

    // 监听在线/离线状态
    const handleOnline = () => {
      console.log('App is online');
      // 可以在这里显示在线状态提示
    };

    const handleOffline = () => {
      console.log('App is offline');
      // 可以在这里显示离线状态提示
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // 只在客户端渲染
  if (!isClient) {
    return null;
  }

  return null; // 这个组件不渲染任何UI，只负责PWA功能
}
