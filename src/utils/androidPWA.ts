// Android PWA 安装工具

export interface PWAInstallEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export class AndroidPWAInstaller {
  private deferredPrompt: PWAInstallEvent | null = null;
  private isInstallable = false;

  constructor() {
    this.setupEventListeners();
  }

  // 设置事件监听器
  private setupEventListeners(): void {
    // 监听PWA安装提示事件
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.deferredPrompt = e as PWAInstallEvent;
      this.isInstallable = true;
      console.log('PWA安装提示已准备');
    });

    // 监听应用安装事件
    window.addEventListener('appinstalled', () => {
      console.log('PWA已安装');
      this.deferredPrompt = null;
      this.isInstallable = false;
    });
  }

  // 检测是否为Android设备
  static isAndroidDevice(): boolean {
    return /Android/.test(navigator.userAgent);
  }

  // 检测是否已安装PWA
  static async isInstalled(): Promise<boolean> {
    // 方法1: 检查display-mode
    if (window.matchMedia('(display-mode: standalone)').matches) {
      return true;
    }

    // 方法2: 检查已安装的相关应用
    if ('getInstalledRelatedApps' in navigator) {
      try {
        const relatedApps = await (navigator as any).getInstalledRelatedApps();
        return relatedApps.length > 0;
      } catch (error) {
        console.log('无法检查已安装的应用:', error);
      }
    }

    return false;
  }

  // 检查是否可以安装
  canInstall(): boolean {
    return this.isInstallable && this.deferredPrompt !== null;
  }

  // 直接安装PWA
  async install(): Promise<boolean> {
    if (!AndroidPWAInstaller.isAndroidDevice()) {
      throw new Error('此方法仅适用于Android设备');
    }

    // 检查是否已安装
    if (await AndroidPWAInstaller.isInstalled()) {
      alert('应用已经安装在您的设备上！');
      return true;
    }

    try {
      // 方法1: 使用PWA安装提示
      if (this.canInstall()) {
        return await this.installWithPrompt();
      }

      // 方法2: 尝试触发浏览器安装
      if (await this.triggerBrowserInstall()) {
        return true;
      }

      // 方法3: 引导用户手动安装
      this.showManualInstallGuide();
      return false;

    } catch (error) {
      console.error('Android PWA安装失败:', error);
      this.showManualInstallGuide();
      return false;
    }
  }

  // 使用PWA安装提示
  private async installWithPrompt(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      // 显示安装提示
      await this.deferredPrompt.prompt();
      
      // 等待用户选择
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('用户接受了PWA安装');
        alert('应用安装成功！您可以在主屏幕找到TRON钱包图标。');
        return true;
      } else {
        console.log('用户拒绝了PWA安装');
        alert('安装已取消。您仍可以通过浏览器菜单手动安装应用。');
        return false;
      }
    } catch (error) {
      console.error('PWA安装提示失败:', error);
      return false;
    } finally {
      this.deferredPrompt = null;
      this.isInstallable = false;
    }
  }

  // 尝试触发浏览器安装
  private async triggerBrowserInstall(): Promise<boolean> {
    try {
      // 方法1: 尝试使用Web App Install API
      if ('getInstalledRelatedApps' in navigator) {
        const relatedApps = await (navigator as any).getInstalledRelatedApps();
        if (relatedApps.length > 0) {
          alert('应用可能已经安装。请检查您的主屏幕或应用抽屉。');
          return true;
        }
      }

      // 方法2: 尝试创建安装链接
      const installLink = this.createInstallLink();
      if (installLink) {
        document.body.appendChild(installLink);
        installLink.click();
        document.body.removeChild(installLink);
        
        // 等待一下看是否触发了安装
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        return false; // 通常需要用户手动操作
      }

      return false;
    } catch (error) {
      console.log('浏览器安装触发失败:', error);
      return false;
    }
  }

  // 创建安装链接
  private createInstallLink(): HTMLAnchorElement | null {
    try {
      const link = document.createElement('a');
      
      // 尝试不同的安装URL格式
      const userAgent = navigator.userAgent;
      
      if (userAgent.includes('Chrome')) {
        // Chrome的安装URL
        link.href = `intent://${window.location.host}${window.location.pathname}#Intent;scheme=https;package=com.android.chrome;S.browser_fallback_url=${encodeURIComponent(window.location.href)};end`;
      } else if (userAgent.includes('Firefox')) {
        // Firefox的安装方式
        link.href = window.location.href;
        link.setAttribute('data-install', 'true');
      } else {
        // 通用方式
        link.href = window.location.href;
      }
      
      link.style.display = 'none';
      return link;
    } catch (error) {
      console.log('创建安装链接失败:', error);
      return null;
    }
  }

  // 显示手动安装指导
  private showManualInstallGuide(): void {
    const userAgent = navigator.userAgent;
    let message = '';

    if (userAgent.includes('Chrome')) {
      message = `
🤖 在Chrome中安装TRON钱包：

方法一：地址栏安装
• 点击地址栏右侧的安装图标 ⬇️
• 点击"安装"确认

方法二：菜单安装  
• 点击右上角菜单 ⋮
• 选择"安装应用"
• 确认安装

安装后可在主屏幕找到应用图标！
      `;
    } else if (userAgent.includes('Firefox')) {
      message = `
🦊 在Firefox中安装TRON钱包：

• 点击地址栏右侧的"+"图标
• 选择"安装此站点为应用"
• 确认安装

安装后可在应用抽屉找到应用！
      `;
    } else if (userAgent.includes('Edge')) {
      message = `
🌐 在Edge中安装TRON钱包：

• 点击地址栏右侧的安装图标
• 或通过菜单选择"应用"→"安装此站点为应用"
• 确认安装

安装后可在主屏幕找到应用图标！
      `;
    } else {
      message = `
📱 安装TRON钱包到主屏幕：

• 查找浏览器菜单中的"添加到主屏幕"选项
• 或查看地址栏的安装提示
• 按照浏览器提示完成安装

建议使用Chrome浏览器获得最佳体验！
      `;
    }

    alert(message);
  }

  // 获取安装状态
  getInstallStatus(): { canInstall: boolean; isInstallable: boolean } {
    return {
      canInstall: this.canInstall(),
      isInstallable: this.isInstallable
    };
  }
}

// 全局实例
let androidInstaller: AndroidPWAInstaller | null = null;

// 获取Android PWA安装器实例
export function getAndroidPWAInstaller(): AndroidPWAInstaller {
  if (!androidInstaller) {
    androidInstaller = new AndroidPWAInstaller();
  }
  return androidInstaller;
}

// 便捷安装函数
export async function installAndroidPWA(): Promise<boolean> {
  const installer = getAndroidPWAInstaller();
  return installer.install();
}
