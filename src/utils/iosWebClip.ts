// iOS WebClip 安装工具

export interface WebClipConfig {
  title: string;
  url: string;
  icon: string;
  description?: string;
}

export class IOSWebClipInstaller {
  private config: WebClipConfig;

  constructor(config: WebClipConfig) {
    this.config = config;
  }

  // 检测是否为iOS设备
  static isIOSDevice(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  // 检测是否为Safari浏览器
  static isSafari(): boolean {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  }

  // 检测是否已安装为WebClip
  static isInstalled(): boolean {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isWebApp = (window.navigator as any).standalone === true;
    return isStandalone || isWebApp;
  }

  // 尝试触发iOS添加到主屏幕
  async install(): Promise<boolean> {
    if (!IOSWebClipInstaller.isIOSDevice()) {
      throw new Error('此方法仅适用于iOS设备');
    }

    if (IOSWebClipInstaller.isInstalled()) {
      alert('应用已经安装在您的设备上！');
      return true;
    }

    try {
      // 方法1: 尝试使用iOS的私有API创建WebClip
      if (await this.tryNativeInstall()) {
        return true;
      }

      // 方法2: 尝试通过URL Scheme触发
      if (await this.tryURLSchemeInstall()) {
        return true;
      }

      // 方法3: 创建配置文件下载
      if (await this.tryConfigFileInstall()) {
        return true;
      }

      // 降级到用户提示
      this.showManualInstallPrompt();
      return false;

    } catch (error) {
      console.error('iOS WebClip安装失败:', error);
      this.showManualInstallPrompt();
      return false;
    }
  }

  // 方法1: 尝试使用原生API
  private async tryNativeInstall(): Promise<boolean> {
    try {
      // 检查是否有BeforeInstallPrompt事件（iOS不支持，但可以尝试）
      if ('serviceWorker' in navigator && 'BeforeInstallPromptEvent' in window) {
        return false;
      }

      // 尝试使用iOS的私有API
      const iOS = (window as any).iOS;
      if (iOS && iOS.addToHomeScreen) {
        iOS.addToHomeScreen(this.config);
        return true;
      }

      return false;
    } catch (error) {
      console.log('原生安装方法不可用:', error);
      return false;
    }
  }

  // 方法2: 尝试URL Scheme
  private async tryURLSchemeInstall(): Promise<boolean> {
    try {
      // 创建一个特殊的URL来尝试触发添加到主屏幕
      const webClipURL = this.generateWebClipURL();
      
      // 尝试打开URL
      const link = document.createElement('a');
      link.href = webClipURL;
      link.target = '_blank';
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 等待一下看是否成功
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return false; // 通常这种方法不会直接成功
    } catch (error) {
      console.log('URL Scheme方法失败:', error);
      return false;
    }
  }

  // 方法3: 创建配置文件
  private async tryConfigFileInstall(): Promise<boolean> {
    try {
      // 生成iOS配置文件内容
      const mobileConfig = this.generateMobileConfig();
      
      // 创建下载链接
      const blob = new Blob([mobileConfig], { type: 'application/x-apple-aspen-config' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${this.config.title}.mobileconfig`;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      // 显示安装提示
      alert(`配置文件已下载。请在"设置"应用中安装 ${this.config.title} 配置文件。`);
      
      return true;
    } catch (error) {
      console.log('配置文件方法失败:', error);
      return false;
    }
  }

  // 生成WebClip URL
  private generateWebClipURL(): string {
    const params = new URLSearchParams({
      title: this.config.title,
      url: this.config.url,
      icon: this.config.icon
    });
    
    return `data:text/html,<script>window.location.href='${this.config.url}'</script>`;
  }

  // 生成iOS配置文件
  private generateMobileConfig(): string {
    const uuid = this.generateUUID();
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PayloadContent</key>
    <array>
        <dict>
            <key>FullScreen</key>
            <true/>
            <key>Icon</key>
            <data>${this.getBase64Icon()}</data>
            <key>IsRemovable</key>
            <true/>
            <key>Label</key>
            <string>${this.config.title}</string>
            <key>PayloadDescription</key>
            <string>${this.config.description || this.config.title}</string>
            <key>PayloadDisplayName</key>
            <string>${this.config.title}</string>
            <key>PayloadIdentifier</key>
            <string>com.tronwallet.webclip.${uuid}</string>
            <key>PayloadType</key>
            <string>com.apple.webClip.managed</string>
            <key>PayloadUUID</key>
            <string>${uuid}</string>
            <key>PayloadVersion</key>
            <integer>1</integer>
            <key>Precomposed</key>
            <true/>
            <key>URL</key>
            <string>${this.config.url}</string>
        </dict>
    </array>
    <key>PayloadDescription</key>
    <string>${this.config.description || this.config.title}</string>
    <key>PayloadDisplayName</key>
    <string>${this.config.title}</string>
    <key>PayloadIdentifier</key>
    <string>com.tronwallet.${uuid}</string>
    <key>PayloadRemovalDisallowed</key>
    <false/>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string>${uuid}</string>
    <key>PayloadVersion</key>
    <integer>1</integer>
</dict>
</plist>`;
  }

  // 生成UUID
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // 获取Base64编码的图标
  private getBase64Icon(): string {
    // 这里应该返回图标的Base64编码
    // 为了简化，返回一个占位符
    return '';
  }

  // 显示手动安装提示
  private showManualInstallPrompt(): void {
    const message = `
📱 安装 ${this.config.title} 到主屏幕：

1. 点击Safari底部的分享按钮 📤
2. 向下滚动找到"添加到主屏幕"
3. 点击"添加到主屏幕"
4. 确认安装

安装后您将获得完整的应用体验！
    `;
    
    alert(message);
  }
}

// 便捷函数
export function installIOSWebClip(config: WebClipConfig): Promise<boolean> {
  const installer = new IOSWebClipInstaller(config);
  return installer.install();
}
