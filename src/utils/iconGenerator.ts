// 浏览器端图标生成器

export interface IconGeneratorOptions {
  svgContent: string;
  sizes: number[];
  backgroundColor?: string;
  padding?: number;
}

// 需要生成的图标尺寸
export const PWA_ICON_SIZES = [
  16, 32, 57, 60, 72, 76, 96, 114, 120, 128, 144, 152, 180, 192, 384, 512
];

// 从SVG生成PNG图标
export async function generateIconFromSVG(
  svgContent: string, 
  size: number, 
  backgroundColor: string = 'transparent',
  padding: number = 0
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    try {
      // 创建SVG Blob
      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      // 创建图片元素
      const img = new Image();
      
      img.onload = () => {
        try {
          // 创建Canvas
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error('无法获取Canvas上下文'));
            return;
          }
          
          // 设置Canvas尺寸
          canvas.width = size;
          canvas.height = size;
          
          // 设置背景色
          if (backgroundColor !== 'transparent') {
            ctx.fillStyle = backgroundColor;
            ctx.fillRect(0, 0, size, size);
          }
          
          // 计算绘制尺寸和位置（考虑padding）
          const drawSize = size - (padding * 2);
          const drawX = padding;
          const drawY = padding;
          
          // 绘制SVG图像
          ctx.drawImage(img, drawX, drawY, drawSize, drawSize);
          
          // 转换为Blob
          canvas.toBlob((blob) => {
            URL.revokeObjectURL(svgUrl);
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Canvas转换为Blob失败'));
            }
          }, 'image/png', 1.0);
          
        } catch (error) {
          URL.revokeObjectURL(svgUrl);
          reject(error);
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(svgUrl);
        reject(new Error('SVG图像加载失败'));
      };
      
      img.src = svgUrl;
      
    } catch (error) {
      reject(error);
    }
  });
}

// 下载图标文件
export function downloadIcon(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

// 生成所有PWA图标
export async function generateAllPWAIcons(
  svgContent: string,
  options: {
    backgroundColor?: string;
    padding?: number;
    downloadAll?: boolean;
  } = {}
): Promise<{ size: number; blob: Blob; filename: string }[]> {
  const {
    backgroundColor = 'transparent',
    padding = 0,
    downloadAll = false
  } = options;
  
  const results: { size: number; blob: Blob; filename: string }[] = [];
  
  console.log('🚀 开始生成PWA图标...');
  
  for (const size of PWA_ICON_SIZES) {
    try {
      console.log(`⏳ 生成 ${size}x${size} 图标...`);
      
      const blob = await generateIconFromSVG(svgContent, size, backgroundColor, padding);
      const filename = `icon-${size}x${size}.png`;
      
      results.push({ size, blob, filename });
      
      if (downloadAll) {
        downloadIcon(blob, filename);
      }
      
      console.log(`✅ ${filename} 生成成功 (${Math.round(blob.size / 1024)}KB)`);
      
    } catch (error) {
      console.error(`❌ 生成 ${size}x${size} 图标失败:`, error);
    }
  }
  
  console.log(`🎉 完成！共生成 ${results.length} 个图标`);
  
  return results;
}

// 生成Favicon
export async function generateFavicon(svgContent: string): Promise<Blob> {
  return generateIconFromSVG(svgContent, 32, 'transparent', 2);
}

// 生成Apple Touch图标
export async function generateAppleTouchIcon(svgContent: string): Promise<Blob> {
  return generateIconFromSVG(svgContent, 180, '#ffffff', 20);
}

// 预览图标
export function previewIcon(blob: Blob, size: number): HTMLImageElement {
  const img = document.createElement('img');
  img.src = URL.createObjectURL(blob);
  img.width = size;
  img.height = size;
  img.style.border = '1px solid #ccc';
  img.style.borderRadius = '8px';
  img.style.margin = '4px';
  
  // 清理URL
  img.onload = () => {
    setTimeout(() => {
      URL.revokeObjectURL(img.src);
    }, 1000);
  };
  
  return img;
}

// 获取SVG内容
export async function getSVGContent(svgPath: string): Promise<string> {
  try {
    const response = await fetch(svgPath);
    if (!response.ok) {
      throw new Error(`获取SVG失败: ${response.statusText}`);
    }
    return await response.text();
  } catch (error) {
    console.error('获取SVG内容失败:', error);
    throw error;
  }
}

// 验证SVG内容
export function validateSVG(svgContent: string): boolean {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgContent, 'image/svg+xml');
    const parserError = doc.querySelector('parsererror');
    
    if (parserError) {
      console.error('SVG解析错误:', parserError.textContent);
      return false;
    }
    
    const svgElement = doc.querySelector('svg');
    if (!svgElement) {
      console.error('未找到SVG元素');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('SVG验证失败:', error);
    return false;
  }
}

// 便捷函数：从SVG路径生成所有图标
export async function generateIconsFromPath(
  svgPath: string,
  options: {
    backgroundColor?: string;
    padding?: number;
    downloadAll?: boolean;
  } = {}
): Promise<{ size: number; blob: Blob; filename: string }[]> {
  try {
    console.log('📥 获取SVG内容...');
    const svgContent = await getSVGContent(svgPath);
    
    console.log('🔍 验证SVG格式...');
    if (!validateSVG(svgContent)) {
      throw new Error('SVG格式无效');
    }
    
    console.log('✅ SVG验证通过');
    
    return await generateAllPWAIcons(svgContent, options);
    
  } catch (error) {
    console.error('生成图标失败:', error);
    throw error;
  }
}
