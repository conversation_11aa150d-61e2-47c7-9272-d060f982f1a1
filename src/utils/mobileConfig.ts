// iOS Mobile Configuration 生成工具

export interface MobileConfigOptions {
  appName: string;
  appDescription: string;
  appUrl: string;
  iconBase64?: string;
  bundleId?: string;
}

// 生成UUID
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 获取图标的Base64编码
async function getIconBase64(iconUrl: string): Promise<string> {
  try {
    const response = await fetch(iconUrl);
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        // 移除data:image/png;base64,前缀
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('获取图标Base64失败:', error);
    return '';
  }
}

// 生成iOS Mobile Configuration文件内容
export async function generateMobileConfig(options: MobileConfigOptions): Promise<string> {
  const {
    appName,
    appDescription,
    appUrl,
    iconBase64,
    bundleId = 'com.tronwallet.app'
  } = options;

  const webClipUUID = generateUUID();
  const profileUUID = generateUUID();
  
  // 如果没有提供图标Base64，尝试获取
  let iconData = iconBase64;
  if (!iconData) {
    iconData = await getIconBase64('/icons/icon-180x180.png');
  }

  const mobileConfig = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PayloadContent</key>
    <array>
        <dict>
            <key>FullScreen</key>
            <true/>
            <key>Icon</key>
            <data>${iconData}</data>
            <key>IsRemovable</key>
            <true/>
            <key>Label</key>
            <string>${appName}</string>
            <key>PayloadDescription</key>
            <string>${appDescription}</string>
            <key>PayloadDisplayName</key>
            <string>Web Clip (${appName})</string>
            <key>PayloadIdentifier</key>
            <string>${bundleId}.webclip</string>
            <key>PayloadType</key>
            <string>com.apple.webClip.managed</string>
            <key>PayloadUUID</key>
            <string>${webClipUUID}</string>
            <key>PayloadVersion</key>
            <integer>1</integer>
            <key>Precomposed</key>
            <true/>
            <key>URL</key>
            <string>${appUrl}</string>
        </dict>
    </array>
    <key>PayloadDescription</key>
    <string>Install ${appName} on your iOS device</string>
    <key>PayloadDisplayName</key>
    <string>${appName}</string>
    <key>PayloadIdentifier</key>
    <string>${bundleId}</string>
    <key>PayloadOrganization</key>
    <string>${appName}</string>
    <key>PayloadRemovalDisallowed</key>
    <false/>
    <key>PayloadType</key>
    <string>Configuration</string>
    <key>PayloadUUID</key>
    <string>${profileUUID}</string>
    <key>PayloadVersion</key>
    <integer>1</integer>
</dict>
</plist>`;

  return mobileConfig;
}

// 下载Mobile Configuration文件
export function downloadMobileConfig(content: string, filename: string = 'app.mobileconfig'): void {
  try {
    const blob = new Blob([content], { type: 'application/x-apple-aspen-config' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    
    console.log('Mobile Configuration文件下载成功');
  } catch (error) {
    console.error('下载Mobile Configuration文件失败:', error);
    throw error;
  }
}

// 检测是否为iOS设备
export function isIOSDevice(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

// 检测是否为Safari浏览器
export function isSafari(): boolean {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('safari') && 
         !userAgent.includes('chrome') && 
         !userAgent.includes('crios') && 
         !userAgent.includes('edg') && 
         !userAgent.includes('vivaldi');
}

// 检测是否已安装为WebClip
export function isInstalledAsWebClip(): boolean {
  // 方法1: 检查display-mode
  if (window.matchMedia('(display-mode: standalone)').matches) {
    return true;
  }
  
  // 方法2: 检查navigator.standalone (iOS特有)
  if ((window.navigator as any).standalone === true) {
    return true;
  }
  
  return false;
}

// iOS WebClip安装主函数
export async function installIOSWebClip(options: MobileConfigOptions): Promise<boolean> {
  if (!isIOSDevice()) {
    throw new Error('此功能仅适用于iOS设备');
  }
  
  if (isInstalledAsWebClip()) {
    alert('应用已经安装在您的设备上！');
    return true;
  }
  
  if (!isSafari()) {
    alert('请使用Safari浏览器进行安装以获得最佳体验');
    return false;
  }
  
  try {
    // 生成Mobile Configuration文件
    const configContent = await generateMobileConfig(options);
    
    // 下载配置文件
    downloadMobileConfig(configContent, `${options.appName}.mobileconfig`);
    
    // 显示安装指导
    setTimeout(() => {
      alert(`配置文件已下载。请在"设置"应用中安装 ${options.appName} 配置文件，或者点击Safari底部的分享按钮📤，然后选择"添加到主屏幕"。`);
    }, 500);
    
    return true;
  } catch (error) {
    console.error('iOS WebClip安装失败:', error);
    
    // 降级到手动安装指导
    alert(`请点击Safari底部的分享按钮📤，然后选择"添加到主屏幕"来安装${options.appName}应用。`);
    
    return false;
  }
}

// 便捷安装函数
export async function installTronWalletIOS(): Promise<boolean> {
  const options: MobileConfigOptions = {
    appName: 'TRON钱包',
    appDescription: '安全、便捷的TRON区块链钱包应用',
    appUrl: window.location.origin,
    bundleId: 'com.tronwallet.app'
  };
  
  return installIOSWebClip(options);
}
