'use client';

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Divider } from '@heroui/react';
import { useAuth } from '@/components/SessionProvider';
import { useRouter } from 'next/navigation';

export default function AdminMobileTestPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [screenInfo, setScreenInfo] = useState({
    width: 0,
    height: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: false
  });

  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenInfo({
        width,
        height,
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024
      });
    };

    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    return () => window.removeEventListener('resize', updateScreenInfo);
  }, []);

  const testRoleRedirect = () => {
    // 模拟不同角色的跳转测试
    if (user?.role === 'super_admin' || user?.role === 'agent') {
      router.push('/admin');
    } else {
      router.push('/wallet');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-4 px-2 sm:py-8 sm:px-4">
      <div className="max-w-4xl mx-auto space-y-4">
        {/* 页面标题 */}
        <Card>
          <CardHeader>
            <h1 className="text-xl sm:text-2xl font-bold text-center w-full">
              管理后台移动端适配测试
            </h1>
          </CardHeader>
          <CardBody>
            <p className="text-center text-gray-600">
              测试管理后台在不同设备上的显示效果和角色跳转功能
            </p>
          </CardBody>
        </Card>

        {/* 当前用户信息 */}
        <Card className="bg-blue-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-blue-800">当前用户信息</h2>
          </CardHeader>
          <CardBody>
            {user ? (
              <div className="space-y-2">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                  <span className="font-medium">用户名:</span>
                  <span>{user.username}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                  <span className="font-medium">角色:</span>
                  <Chip
                    size="sm"
                    color={
                      user.role === 'super_admin' ? 'danger' :
                      user.role === 'agent' ? 'primary' : 'default'
                    }
                    variant="flat"
                  >
                    {user.role === 'super_admin' ? '超级管理员' :
                     user.role === 'agent' ? '代理' : '普通用户'}
                  </Chip>
                </div>
                {user.email && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                    <span className="font-medium">邮箱:</span>
                    <span className="text-sm">{user.email}</span>
                  </div>
                )}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                  <span className="font-medium">期望跳转:</span>
                  <span className="text-sm">
                    {user.role === 'super_admin' || user.role === 'agent' ? 
                      '管理后台 (/admin)' : '钱包页面 (/wallet)'}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">未登录</p>
            )}
          </CardBody>
        </Card>

        {/* 屏幕信息 */}
        <Card className="bg-green-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-green-800">屏幕信息</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">屏幕尺寸</h3>
                <div className="space-y-1 text-sm">
                  <div>宽度: {screenInfo.width}px</div>
                  <div>高度: {screenInfo.height}px</div>
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">设备类型</h3>
                <div className="space-y-1">
                  <Chip
                    size="sm"
                    color={screenInfo.isMobile ? 'success' : 'default'}
                    variant={screenInfo.isMobile ? 'solid' : 'bordered'}
                  >
                    移动端 (&lt;768px)
                  </Chip>
                  <Chip
                    size="sm"
                    color={screenInfo.isTablet ? 'success' : 'default'}
                    variant={screenInfo.isTablet ? 'solid' : 'bordered'}
                    className="ml-2"
                  >
                    平板 (768-1024px)
                  </Chip>
                  <Chip
                    size="sm"
                    color={screenInfo.isDesktop ? 'success' : 'default'}
                    variant={screenInfo.isDesktop ? 'solid' : 'bordered'}
                    className="ml-2"
                  >
                    桌面 (&gt;1024px)
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 功能测试 */}
        <Card className="bg-yellow-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-yellow-800">功能测试</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button
                color="primary"
                onPress={() => router.push('/admin')}
                className="w-full"
              >
                访问管理后台
              </Button>
              <Button
                color="secondary"
                onPress={() => router.push('/wallet')}
                className="w-full"
              >
                访问钱包页面
              </Button>
            </div>
            
            <Divider />
            
            <Button
              color="success"
              onPress={testRoleRedirect}
              className="w-full"
              variant="bordered"
            >
              测试角色跳转逻辑
            </Button>
            
            <div className="text-sm text-yellow-700">
              <p>• 管理员和代理应该跳转到管理后台</p>
              <p>• 普通用户应该跳转到钱包页面</p>
            </div>
          </CardBody>
        </Card>

        {/* 移动端适配说明 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">移动端适配说明</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-blue-800">布局适配</h3>
                <ul className="text-sm text-gray-700 space-y-1 mt-2">
                  <li>• 响应式头部布局：移动端垂直排列，桌面端水平排列</li>
                  <li>• 表格横向滚动：移动端可以左右滑动查看完整表格</li>
                  <li>• 隐藏非关键列：移动端隐藏邮箱和注册时间列</li>
                  <li>• 按钮自适应：移动端全宽按钮，桌面端固定宽度</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-green-800">角色跳转</h3>
                <ul className="text-sm text-gray-700 space-y-1 mt-2">
                  <li>• 登录API返回用户角色信息</li>
                  <li>• 前端根据角色自动跳转到对应页面</li>
                  <li>• 超级管理员和代理 → 管理后台</li>
                  <li>• 普通用户 → 钱包页面</li>
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-purple-800">技术实现</h3>
                <ul className="text-sm text-gray-700 space-y-1 mt-2">
                  <li>• Tailwind CSS响应式类名</li>
                  <li>• HeroUI组件自适应</li>
                  <li>• 条件渲染优化显示</li>
                  <li>• 角色信息持久化存储</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 测试结果 */}
        <Card className="bg-gray-50">
          <CardHeader>
            <h2 className="text-lg font-semibold">测试检查清单</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className={`w-4 h-4 rounded-full ${screenInfo.isMobile ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                <span>移动端布局正常显示</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-4 h-4 rounded-full ${user ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>用户登录状态正常</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-4 h-4 rounded-full ${user?.role ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>用户角色信息正确</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-4 h-4 rounded-full bg-blue-500"></span>
                <span>角色跳转逻辑待测试</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
