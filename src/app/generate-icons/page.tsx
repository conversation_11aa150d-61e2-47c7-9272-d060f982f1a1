'use client';

import { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Chip } from '@heroui/react';
import { generateIconsFromPath, PWA_ICON_SIZES, downloadIcon } from '@/utils/iconGenerator';

interface GeneratedIcon {
  size: number;
  blob: Blob;
  filename: string;
  url?: string;
}

export default function GenerateIconsPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generatedIcons, setGeneratedIcons] = useState<GeneratedIcon[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateIcons = async () => {
    setIsGenerating(true);
    setProgress(0);
    setError(null);
    setGeneratedIcons([]);

    try {
      console.log('🚀 开始生成图标...');
      
      // 生成图标
      const icons = await generateIconsFromPath('/icons/logo.svg', {
        backgroundColor: 'transparent',
        padding: 0,
        downloadAll: false // 不自动下载，让用户选择
      });

      // 为每个图标创建预览URL
      const iconsWithUrls = icons.map(icon => ({
        ...icon,
        url: URL.createObjectURL(icon.blob)
      }));

      setGeneratedIcons(iconsWithUrls);
      setProgress(100);
      
      console.log('🎉 所有图标生成完成！');
      
    } catch (error: any) {
      console.error('生成图标失败:', error);
      setError(error.message || '生成图标失败');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadIcon = (icon: GeneratedIcon) => {
    downloadIcon(icon.blob, icon.filename);
  };

  const handleDownloadAll = () => {
    generatedIcons.forEach(icon => {
      setTimeout(() => {
        downloadIcon(icon.blob, icon.filename);
      }, 100 * icon.size); // 错开下载时间
    });
  };

  const formatFileSize = (bytes: number): string => {
    return `${Math.round(bytes / 1024)}KB`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <Card>
          <CardHeader>
            <div className="text-center w-full">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                PWA 图标生成器
              </h1>
              <p className="text-gray-600">
                从SVG生成所有PWA所需的PNG图标尺寸
              </p>
            </div>
          </CardHeader>
        </Card>

        {/* SVG预览 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">源SVG图标</h2>
          </CardHeader>
          <CardBody className="text-center">
            <div className="inline-block p-4 bg-gray-100 rounded-lg">
              <img 
                src="/icons/logo.svg" 
                alt="Logo SVG" 
                className="w-24 h-24"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
              <div className="hidden text-gray-500">
                SVG文件未找到
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              源文件: /icons/logo.svg
            </p>
          </CardBody>
        </Card>

        {/* 生成控制 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">生成图标</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <p>将生成以下尺寸的PNG图标：</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {PWA_ICON_SIZES.map(size => (
                    <Chip key={size} size="sm" variant="flat">
                      {size}×{size}
                    </Chip>
                  ))}
                </div>
              </div>

              {isGenerating && (
                <div className="space-y-2">
                  <Progress 
                    value={progress} 
                    color="primary"
                    showValueLabel
                    label="生成进度"
                  />
                  <p className="text-sm text-gray-600">正在生成图标...</p>
                </div>
              )}

              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 font-medium">生成失败</p>
                  <p className="text-red-600 text-sm mt-1">{error}</p>
                </div>
              )}

              <div className="flex gap-3">
                <Button
                  color="primary"
                  onPress={handleGenerateIcons}
                  isLoading={isGenerating}
                  disabled={isGenerating}
                >
                  {isGenerating ? '生成中...' : '生成所有图标'}
                </Button>

                {generatedIcons.length > 0 && (
                  <Button
                    color="success"
                    variant="bordered"
                    onPress={handleDownloadAll}
                  >
                    下载所有图标
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 生成结果 */}
        {generatedIcons.length > 0 && (
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold">
                生成结果 ({generatedIcons.length} 个图标)
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {generatedIcons.map(icon => (
                  <div key={icon.size} className="text-center">
                    <div className="bg-gray-100 rounded-lg p-2 mb-2">
                      {icon.url && (
                        <img 
                          src={icon.url}
                          alt={`${icon.size}x${icon.size}`}
                          className="w-full h-auto max-w-16 mx-auto"
                        />
                      )}
                    </div>
                    <div className="text-xs space-y-1">
                      <p className="font-medium">{icon.size}×{icon.size}</p>
                      <p className="text-gray-500">{formatFileSize(icon.blob.size)}</p>
                      <Button
                        size="sm"
                        variant="bordered"
                        onPress={() => handleDownloadIcon(icon)}
                        className="text-xs h-6"
                      >
                        下载
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        )}

        {/* 使用说明 */}
        <Card className="bg-blue-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-blue-800">使用说明</h2>
          </CardHeader>
          <CardBody>
            <div className="text-sm text-blue-700 space-y-2">
              <h3 className="font-semibold">生成步骤：</h3>
              <ol className="list-decimal list-inside space-y-1">
                <li>确保 /icons/logo.svg 文件存在</li>
                <li>点击"生成所有图标"按钮</li>
                <li>等待生成完成</li>
                <li>下载需要的图标文件</li>
                <li>将图标文件放到 public/icons/ 目录</li>
              </ol>
              
              <h3 className="font-semibold mt-4">图标用途：</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>PWA应用图标 (192×192, 512×512)</li>
                <li>Apple Touch图标 (180×180)</li>
                <li>Favicon (16×16, 32×32)</li>
                <li>各种设备适配图标</li>
              </ul>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
