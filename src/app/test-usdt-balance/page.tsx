'use client';

import { useState } from 'react';
import { Button, Input, Card, CardBody, CardHeader, Select, SelectItem } from '@heroui/react';

export default function TestUsdtBalancePage() {
  const [address, setAddress] = useState('TAjz662ivGjK792yaiTaeJcthDD4wTtX4m');
  const [network, setNetwork] = useState<'mainnet' | 'nile' | 'shasta'>('nile');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testUsdtBalance = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug-usdt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: address,
          network: network
        }),
      });

      const data = await response.json();
      
      setResults({
        success: response.ok,
        data: data,
        timestamp: new Date().toLocaleString()
      });
      
    } catch (error) {
      setResults({
        success: false,
        error: error,
        timestamp: new Date().toLocaleString()
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">USDT余额调试工具</h1>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="TRON地址"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="输入TRON地址"
            description="默认为您提供的Nile测试网地址"
          />
          
          <Select
            label="网络"
            selectedKeys={[network]}
            onSelectionChange={(keys) => {
              const selectedNetwork = Array.from(keys)[0] as 'mainnet' | 'nile' | 'shasta';
              setNetwork(selectedNetwork);
            }}
          >
            <SelectItem key="mainnet">主网 (Mainnet)</SelectItem>
            <SelectItem key="nile">测试网 (Nile)</SelectItem>
            <SelectItem key="shasta">测试网 (Shasta)</SelectItem>
          </Select>
          
          <Button
            color="primary"
            onPress={testUsdtBalance}
            isLoading={loading}
            className="w-full"
          >
            调试USDT余额
          </Button>

          {results && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">调试结果</h2>
              
              {results.success ? (
                <div className="space-y-4">
                  {/* TRX余额 */}
                  {results.data.results.trx && (
                    <Card className="bg-blue-50">
                      <CardHeader>
                        <h3 className="font-semibold">TRX余额</h3>
                      </CardHeader>
                      <CardBody>
                        {results.data.results.trx.success ? (
                          <div>
                            <p>余额: {results.data.results.trx.balance} TRX</p>
                            <p className="text-sm text-gray-600">原始值: {results.data.results.trx.raw}</p>
                          </div>
                        ) : (
                          <p className="text-red-600">错误: {results.data.results.trx.error}</p>
                        )}
                      </CardBody>
                    </Card>
                  )}

                  {/* USDT合约测试结果 */}
                  {Object.entries(results.data.results).map(([key, result]: [string, any]) => {
                    if (key.startsWith('usdt_contract_')) {
                      return (
                        <Card key={key} className={result.success ? 'bg-green-50' : 'bg-red-50'}>
                          <CardHeader>
                            <h3 className="font-semibold">
                              USDT合约 {key.replace('usdt_contract_', '')}
                            </h3>
                          </CardHeader>
                          <CardBody>
                            <p className="text-sm text-gray-600 mb-2">
                              合约地址: {result.contractAddress}
                            </p>
                            {result.success ? (
                              <div>
                                <p className="text-lg font-bold text-green-600">
                                  余额: {result.balance} USDT
                                </p>
                                <p className="text-sm text-gray-600">
                                  原始值: {result.raw}
                                </p>
                                <p className="text-sm text-gray-600">
                                  数值: {result.balanceNumber}
                                </p>
                                {result.contractInfo && (
                                  <div className="mt-2 text-sm">
                                    <p>名称: {result.contractInfo.name}</p>
                                    <p>符号: {result.contractInfo.symbol}</p>
                                    <p>小数位: {result.contractInfo.decimals}</p>
                                  </div>
                                )}
                              </div>
                            ) : (
                              <p className="text-red-600">错误: {result.error}</p>
                            )}
                          </CardBody>
                        </Card>
                      );
                    }
                    return null;
                  })}

                  {/* 账户信息 */}
                  {results.data.results.accountInfo && (
                    <Card className="bg-gray-50">
                      <CardHeader>
                        <h3 className="font-semibold">账户信息</h3>
                      </CardHeader>
                      <CardBody>
                        <pre className="text-sm overflow-auto">
                          {JSON.stringify(results.data.results.accountInfo, null, 2)}
                        </pre>
                      </CardBody>
                    </Card>
                  )}
                </div>
              ) : (
                <Card className="bg-red-50">
                  <CardBody>
                    <p className="text-red-600">测试失败: {JSON.stringify(results.error)}</p>
                  </CardBody>
                </Card>
              )}

              <div className="text-sm text-gray-500">
                测试时间: {results.timestamp}
              </div>
            </div>
          )}

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">说明</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 此工具会测试多个可能的USDT合约地址</li>
              <li>• 显示原始余额数据和处理后的结果</li>
              <li>• 帮助诊断USDT余额获取问题</li>
              <li>• 您的地址: TAjz662ivGjK792yaiTaeJcthDD4wTtX4m</li>
            </ul>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
