'use client';

import { useEffect } from 'react';
import { useAuth } from '@/components/SessionProvider';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  But<PERSON>,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Spinner
} from "@heroui/react";

export default function Home() {
  const { user, loading } = useAuth();
  const router = useRouter();

  // 如果已登录，直接跳转到钱包页面
  useEffect(() => {
    if (loading) return;

    if (user) {
      router.push('/wallet');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (user) {
    return null; // 将重定向到钱包页面
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* 导航栏 */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">TRON钱包</h1>
            </div>
            <div className="flex items-center gap-4">
              <Button
                as={Link}
                href="/auth/signin"
                color="primary"
                variant="solid"
              >
                登录
              </Button>
              <Button
                as={Link}
                href="/auth/register"
                color="primary"
                variant="bordered"
              >
                注册
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* 英雄区域 */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            安全便捷的
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              TRON钱包
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款。
            安全可靠，操作简单，让数字资产管理变得轻松。
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              as={Link}
              href="/auth/register"
              color="primary"
              size="lg"
              className="px-8"
            >
              立即开始
            </Button>
            <Button
              as={Link}
              href="/auth/signin"
              color="default"
              variant="bordered"
              size="lg"
              className="px-8"
            >
              已有账号
            </Button>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <Card className="p-6">
            <CardBody className="text-center">
              <div className="text-4xl mb-4">🔐</div>
              <h3 className="text-xl font-semibold mb-3">安全可靠</h3>
              <p className="text-gray-600">
                采用先进的加密技术保护您的私钥和资产安全，支持MFA双重验证
              </p>
            </CardBody>
          </Card>

          <Card className="p-6">
            <CardBody className="text-center">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-semibold mb-3">快速转账</h3>
              <p className="text-gray-600">
                基于TRON网络的快速转账，低手续费，支持TRX和USDT等主流币种
              </p>
            </CardBody>
          </Card>

          <Card className="p-6">
            <CardBody className="text-center">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-xl font-semibold mb-3">简单易用</h3>
              <p className="text-gray-600">
                直观的用户界面，支持二维码收款，让数字资产管理变得简单
              </p>
            </CardBody>
          </Card>
        </div>

        {/* 支持的功能 */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <h2 className="text-3xl font-bold text-center mb-8">核心功能</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>创建和导入TRON钱包</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>TRX和USDT转账收款</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>实时余额查询</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>交易记录查看</span>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>二维码收款功能</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>多钱包管理</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>安全的私钥加密存储</span>
              </div>
              <div className="flex items-center gap-3">
                <Chip color="success" variant="flat">✓</Chip>
                <span>邀请码注册系统</span>
              </div>
            </div>
          </div>
        </div>

        {/* 底部CTA */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold mb-4">准备开始了吗？</h2>
          <p className="text-gray-600 mb-8">
            注册账号，创建您的第一个TRON钱包
          </p>
          <Button
            as={Link}
            href="/auth/register"
            color="primary"
            size="lg"
            className="px-12"
          >
            立即注册
          </Button>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p>&copy; 2024 TRON钱包. 保留所有权利.</p>
        </div>
      </footer>
    </div>
  );
}
