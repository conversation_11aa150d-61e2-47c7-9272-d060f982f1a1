'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Input,
  Spacer,
  Chip
} from "@heroui/react";

export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    inviteCode: '',
    name: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // 验证表单
    if (!formData.username.trim()) {
      setError('请输入用户名');
      return;
    }

    if (!formData.password.trim()) {
      setError('请输入密码');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    if (formData.password.length < 6) {
      setError('密码长度至少6位');
      return;
    }

    if (!formData.inviteCode.trim()) {
      setError('请输入邀请码');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username.trim(),
          email: formData.email.trim() || undefined,
          password: formData.password,
          inviteCode: formData.inviteCode.trim(),
          name: formData.name.trim() || undefined
        }),
      });

      const data = await response.json();

      if (response.ok) {
        alert('注册成功！请登录您的账户。');
        router.push('/auth/signin');
      } else {
        setError(data.error || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      setError('注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (error) setError('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-2xl">
          <CardHeader className="text-center pb-2">
            <div className="w-full">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">注册账户</h1>
              <p className="text-gray-600">创建您的TRON钱包账户</p>
            </div>
          </CardHeader>
          <CardBody className="px-8 py-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <Input
                label="用户名"
                placeholder="输入用户名"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                isRequired
                variant="bordered"
                description="用于登录的唯一标识"
              />

              <Input
                label="邮箱"
                type="email"
                placeholder="输入邮箱地址（可选）"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                variant="bordered"
                description="用于找回密码和接收通知"
              />

              <Input
                label="显示名称"
                placeholder="输入显示名称（可选）"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                variant="bordered"
                description="在应用中显示的名称"
              />

              <Input
                label="密码"
                type="password"
                placeholder="输入密码"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                isRequired
                variant="bordered"
                description="至少6位字符"
              />

              <Input
                label="确认密码"
                type="password"
                placeholder="再次输入密码"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                isRequired
                variant="bordered"
              />

              <Input
                label="邀请码"
                placeholder="输入邀请码"
                value={formData.inviteCode}
                onChange={(e) => handleInputChange('inviteCode', e.target.value)}
                isRequired
                variant="bordered"
                description="需要有效的邀请码才能注册"
                startContent={
                  <Chip size="sm" color="primary" variant="flat">
                    🎫
                  </Chip>
                }
              />

              <Spacer y={2} />

              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full"
                isLoading={loading}
                disabled={loading}
              >
                {loading ? '注册中...' : '注册账户'}
              </Button>
            </form>

            <Spacer y={6} />

            <div className="text-center">
              <p className="text-gray-600">
                已有账户？{' '}
                <Link href="/auth/signin" className="text-blue-600 hover:text-blue-500 font-medium">
                  立即登录
                </Link>
              </p>
            </div>

            <Spacer y={4} />

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Chip size="sm" color="primary" variant="flat">
                  💡
                </Chip>
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">关于邀请码</h4>
                  <p className="text-blue-700 text-sm">
                    邀请码用于确保平台安全性。如果您没有邀请码，请联系现有用户或管理员获取。
                  </p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        <div className="mt-8 text-center">
          <Link href="/" className="text-gray-500 hover:text-gray-700">
            ← 返回首页
          </Link>
        </div>
      </div>
    </div>
  );
}
