'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/SessionProvider';
import Link from 'next/link';
import {
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Spacer,
  Chip,
  Checkbox
} from "@heroui/react";

export default function SignInPage() {
  const router = useRouter();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    mfaCode: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showMFA, setShowMFA] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.username.trim()) {
      setError('请输入用户名');
      return;
    }

    if (!formData.password.trim()) {
      setError('请输入密码');
      return;
    }

    if (showMFA && !formData.mfaCode.trim()) {
      setError('请输入MFA验证码');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username.trim(),
          password: formData.password,
          mfaCode: formData.mfaCode.trim() || undefined,
          rememberMe
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.requireMFA && !showMFA) {
          setShowMFA(true);
          setError('');
        } else {
          // 登录成功，更新认证状态
          login(data.user);

          // 根据用户角色跳转到不同页面
          if (data.user.role === 'super_admin' || data.user.role === 'agent') {
            // 管理员和代理跳转到管理后台
            router.push('/admin');
          } else {
            // 普通用户跳转到钱包页面
            router.push('/wallet');
          }
        }
      } else {
        setError(data.error || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      setError('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (error) setError('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="shadow-2xl">
          <CardHeader className="text-center pb-2">
            <div className="w-full">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">登录账户</h1>
              <p className="text-gray-600">访问您的TRON钱包</p>
            </div>
          </CardHeader>
          <CardBody className="px-8 py-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <Input
                label="用户名"
                placeholder="输入用户名"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                isRequired
                variant="bordered"
                disabled={showMFA}
              />

              <Input
                label="密码"
                type="password"
                placeholder="输入密码"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                isRequired
                variant="bordered"
                disabled={showMFA}
              />

              {showMFA && (
                <div className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Chip size="sm" color="primary" variant="flat">
                        🔐
                      </Chip>
                      <span className="font-medium text-blue-900">需要MFA验证</span>
                    </div>
                    <p className="text-blue-700 text-sm">
                      请打开您的身份验证器应用，输入6位验证码
                    </p>
                  </div>

                  <Input
                    label="MFA验证码"
                    placeholder="输入6位验证码"
                    value={formData.mfaCode}
                    onChange={(e) => handleInputChange('mfaCode', e.target.value)}
                    isRequired
                    variant="bordered"
                    maxLength={6}
                    description="来自Google Authenticator或其他验证器应用"
                  />
                </div>
              )}

              {!showMFA && (
                <div className="flex items-center justify-between">
                  <Checkbox
                    isSelected={rememberMe}
                    onValueChange={setRememberMe}
                    size="sm"
                  >
                    记住我
                  </Checkbox>
                  <Link href="/auth/forgot-password" className="text-sm text-blue-600 hover:text-blue-500">
                    忘记密码？
                  </Link>
                </div>
              )}

              <Spacer y={2} />

              <Button
                type="submit"
                color="primary"
                size="lg"
                className="w-full"
                isLoading={loading}
                disabled={loading}
              >
                {loading ? '登录中...' : showMFA ? '验证并登录' : '登录'}
              </Button>

              {showMFA && (
                <Button
                  type="button"
                  variant="light"
                  size="lg"
                  className="w-full"
                  onPress={() => {
                    setShowMFA(false);
                    setFormData(prev => ({ ...prev, mfaCode: '' }));
                    setError('');
                  }}
                >
                  返回重新登录
                </Button>
              )}
            </form>

            <Spacer y={6} />

            <div className="text-center">
              <p className="text-gray-600">
                还没有账户？{' '}
                <Link href="/auth/register" className="text-blue-600 hover:text-blue-500 font-medium">
                  立即注册
                </Link>
              </p>
            </div>
          </CardBody>
        </Card>

        <div className="mt-8 text-center">
          <Link href="/" className="text-gray-500 hover:text-gray-700">
            ← 返回首页
          </Link>
        </div>
      </div>
    </div>
  );
}
