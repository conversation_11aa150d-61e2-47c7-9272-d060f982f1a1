@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-geist-sans: var(--font-geist-sans), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-geist-mono: var(--font-geist-mono), ui-monospace, SFMono-Regular, 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  /* 防止页面缩放 */
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 防止双击缩放 */
* {
  touch-action: manipulation;
}

/* 防止输入框缩放 */
input, textarea, select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  /* 防止iOS Safari自动缩放 */
  font-size: 16px !important;
  -webkit-text-size-adjust: 100%;
  -webkit-appearance: none;
  border-radius: 0;
}

/* 专门针对移动端的输入框样式 */
@media screen and (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100%;
    -webkit-appearance: none;
    -webkit-border-radius: 0;
    border-radius: 0;
  }

  /* HeroUI 输入框样式 */
  .heroui-input input,
  .heroui-textarea textarea {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100%;
    -webkit-appearance: none;
  }

  /* 防止iOS Safari输入框自动缩放的额外措施 */
  input:focus,
  textarea:focus,
  select:focus {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100%;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* 确保模态框中的输入框也不会缩放 */
  .heroui-modal input,
  .heroui-modal textarea {
    font-size: 16px !important;
    -webkit-text-size-adjust: 100%;
  }
}



/* 防止图片和按钮的默认行为 */
img, button {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
