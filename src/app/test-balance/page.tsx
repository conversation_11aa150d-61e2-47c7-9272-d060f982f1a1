'use client';

import { useState } from 'react';
import { Button, Input, Card, CardBody, CardHeader, Select, SelectItem } from '@heroui/react';

export default function TestBalancePage() {
  const [address, setAddress] = useState('TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'); // Nile测试网USDT合约
  const [network, setNetwork] = useState<'mainnet' | 'nile' | 'shasta'>('nile');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testBalance = async (currency: 'TRX' | 'USDT') => {
    setLoading(true);
    try {
      const response = await fetch('/api/wallets/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: address,
          currency: currency,
          network: network
        }),
      });

      const data = await response.json();
      
      setResults(prev => ({
        ...prev,
        [currency]: {
          success: response.ok,
          data: data,
          timestamp: new Date().toLocaleString()
        }
      }));
      
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [currency]: {
          success: false,
          error: error,
          timestamp: new Date().toLocaleString()
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">TRON余额测试</h1>
        </CardHeader>
        <CardBody className="space-y-4">
          <Input
            label="TRON地址"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="输入TRON地址"
          />

          <Select
            label="网络"
            selectedKeys={[network]}
            onSelectionChange={(keys) => {
              const selectedNetwork = Array.from(keys)[0] as 'mainnet' | 'nile' | 'shasta';
              setNetwork(selectedNetwork);
            }}
          >
            <SelectItem key="mainnet">主网 (Mainnet)</SelectItem>
            <SelectItem key="nile">测试网 (Nile)</SelectItem>
            <SelectItem key="shasta">测试网 (Shasta)</SelectItem>
          </Select>
          
          <div className="flex gap-4">
            <Button
              color="primary"
              onPress={() => testBalance('TRX')}
              isLoading={loading}
            >
              测试TRX余额
            </Button>
            
            <Button
              color="secondary"
              onPress={() => testBalance('USDT')}
              isLoading={loading}
            >
              测试USDT余额
            </Button>
          </div>

          {results && (
            <div className="space-y-4">
              {results.TRX && (
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">TRX余额结果</h3>
                  </CardHeader>
                  <CardBody>
                    <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                      {JSON.stringify(results.TRX, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}

              {results.USDT && (
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">USDT余额结果</h3>
                  </CardHeader>
                  <CardBody>
                    <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                      {JSON.stringify(results.USDT, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
