'use client';

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Divider } from '@heroui/react';
import { useRouter } from 'next/navigation';
import { installTronWalletIOS, isIOSDevice, isSafari } from '@/utils/mobileConfig';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export default function DownloadPage() {
  const router = useRouter();
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [installError, setInstallError] = useState<string | null>(null);
  const [deviceInfo, setDeviceInfo] = useState({
    isIOS: false,
    isAndroid: false,
    isMobile: false,
    browser: ''
  });


  useEffect(() => {
    // 检测设备和浏览器
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const isMobile = /Mobi|Android/i.test(userAgent);

    let browser = 'Unknown';
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';

    setDeviceInfo({ isIOS, isAndroid, isMobile, browser });

    // 检查是否已安装
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    // 监听PWA安装事件
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('PWA安装提示事件触发');
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
      setInstallError(null);
    };

    const handleAppInstalled = () => {
      console.log('PWA安装完成');
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
      setInstallError(null);
    };

    // 添加事件监听
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // 检查PWA安装条件
    console.log('检查PWA安装条件:', {
      isAndroid,
      browser,
      isSecure: location.protocol === 'https:' || location.hostname === 'localhost',
      hasServiceWorker: 'serviceWorker' in navigator,
      hasManifest: document.querySelector('link[rel="manifest"]') !== null
    });

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallPWA = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('用户接受了PWA安装');
      } else {
        console.log('用户拒绝了PWA安装');
      }

      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('PWA安装失败:', error);
    }
  };

  const handleIOSInstall = async () => {
    // iOS WebClip 直接安装
    if (!isIOSDevice()) {
      alert('此功能仅适用于iOS设备');
      return;
    }

    if (!isSafari()) {
      alert('请使用Safari浏览器进行安装以获得最佳体验');
      return;
    }

    try {
      // 使用Mobile Configuration安装工具
      const success = await installTronWalletIOS();

      if (success) {
        console.log('iOS WebClip安装成功');
      } else {
        console.log('iOS WebClip安装需要用户手动操作');
      }
    } catch (error) {
      console.error('iOS WebClip安装失败:', error);
      // 降级到简单提示
      alert('请点击Safari底部的分享按钮📤，然后选择"添加到主屏幕"来安装TRON钱包应用');
    }
  };

  const handleAndroidInstall = async () => {
    console.log('开始Android PWA安装');
    setInstallError(null);

    // 检查基本条件
    if (!deviceInfo.isAndroid) {
      const error = '此功能仅适用于Android设备';
      setInstallError(error);
      alert(error);
      return;
    }

    // 检查是否已安装
    if (isInstalled) {
      alert('应用已经安装在您的设备上！');
      return;
    }

    try {
      // 方法1: 使用PWA安装提示
      if (deferredPrompt) {
        console.log('使用PWA安装提示');

        // 显示安装提示
        await deferredPrompt.prompt();

        // 等待用户选择
        const choiceResult = await deferredPrompt.userChoice;

        console.log('用户选择结果:', choiceResult.outcome);

        if (choiceResult.outcome === 'accepted') {
          console.log('用户接受了PWA安装');
          // 不需要手动设置状态，appinstalled事件会处理
        } else {
          console.log('用户拒绝了PWA安装');
          setInstallError('用户取消了安装');
        }

        // 清理
        setDeferredPrompt(null);
        setIsInstallable(false);

      } else {
        // 方法2: 引导用户手动安装
        console.log('没有PWA安装提示，引导手动安装');

        let message = '';
        const userAgent = navigator.userAgent;

        if (userAgent.includes('Chrome')) {
          message = `请按以下步骤安装TRON钱包：

🔹 方法一：地址栏安装
1. 查看地址栏右侧的安装图标 ⬇️
2. 点击安装图标
3. 确认安装

🔹 方法二：菜单安装
1. 点击右上角菜单 ⋮
2. 选择"安装应用"或"添加到主屏幕"
3. 确认安装`;
        } else if (userAgent.includes('Firefox')) {
          message = `请按以下步骤安装TRON钱包：

1. 点击地址栏右侧的"+"图标
2. 选择"安装此站点为应用"
3. 确认安装`;
        } else if (userAgent.includes('Edge')) {
          message = `请按以下步骤安装TRON钱包：

1. 点击地址栏右侧的安装图标
2. 或通过菜单选择"应用"→"安装此站点为应用"
3. 确认安装`;
        } else {
          message = `请通过浏览器菜单中的"添加到主屏幕"或"安装应用"选项来安装TRON钱包。

建议使用Chrome浏览器获得最佳体验。`;
        }

        alert(message);
        setInstallError('需要手动安装');
      }

    } catch (error: any) {
      console.error('Android PWA安装失败:', error);
      const errorMessage = error.message || '安装失败，请重试';
      setInstallError(errorMessage);
      alert(`安装失败: ${errorMessage}\n\n请尝试通过浏览器菜单手动安装应用。`);
    }
  };

  const handleDesktopInstall = async () => {
    // 桌面端PWA安装
    if (deferredPrompt) {
      await handleInstallPWA();
    } else {
      const instructions = `
💻 在桌面端安装TRON钱包：

Chrome/Edge浏览器：
1️⃣ 点击地址栏右侧的安装图标 ⬇️
2️⃣ 或点击右上角菜单 → "安装TRON钱包"
3️⃣ 确认安装

Firefox浏览器：
1️⃣ 点击地址栏右侧的"+"图标
2️⃣ 选择"安装此站点为应用"
3️⃣ 确认安装

✨ 安装后您将获得：
• 桌面应用图标
• 独立窗口运行
• 快速启动
• 系统集成
      `;

      alert(instructions);
    }
  };

  const getInstallInstructions = () => {
    if (deviceInfo.isIOS) {
      return {
        title: 'iOS Safari 安装说明',
        steps: [
          '点击底部的分享按钮 📤',
          '向下滚动找到"添加到主屏幕"',
          '点击"添加到主屏幕"',
          '确认安装，应用将出现在主屏幕上'
        ],
        note: 'iOS设备需要使用Safari浏览器才能安装PWA'
      };
    } else if (deviceInfo.isAndroid) {
      return {
        title: 'Android 安装说明',
        steps: [
          '点击下方的"安装应用"按钮',
          '或者点击浏览器菜单中的"安装应用"',
          '确认安装提示',
          '应用将添加到主屏幕和应用抽屉'
        ],
        note: 'Android设备推荐使用Chrome浏览器获得最佳体验'
      };
    } else {
      return {
        title: '桌面端安装说明',
        steps: [
          '点击地址栏右侧的安装图标',
          '或者点击下方的"安装应用"按钮',
          '确认安装提示',
          '应用将添加到桌面和开始菜单'
        ],
        note: '桌面端推荐使用Chrome、Edge或Firefox浏览器'
      };
    }
  };

  const instructions = getInstallInstructions();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* 页面标题 */}
        <Card>
          <CardHeader>
            <div className="text-center w-full">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                下载 TRON 钱包
              </h1>
              <p className="text-gray-600">
                安装到您的设备，享受原生应用体验
              </p>
            </div>
          </CardHeader>
        </Card>

        {/* 应用状态 */}
        <Card className={isInstalled ? 'bg-green-50' : 'bg-blue-50'}>
          <CardBody className="text-center">
            {isInstalled ? (
              <div>
                <div className="text-4xl mb-3">✅</div>
                <h2 className="text-xl font-semibold text-green-800 mb-2">
                  应用已安装
                </h2>
                <p className="text-green-700 mb-4">
                  TRON钱包已成功安装到您的设备上
                </p>
                <Button
                  color="success"
                  onPress={() => router.push('/wallet')}
                  className="w-full sm:w-auto"
                >
                  打开钱包
                </Button>
              </div>
            ) : (
              <div>
                <div className="text-4xl mb-3">📱</div>
                <h2 className="text-xl font-semibold text-blue-800 mb-2">
                  安装 PWA 应用
                </h2>
                <p className="text-blue-700 mb-4">
                  将TRON钱包安装到您的设备，获得更好的使用体验
                </p>

                {/* 平台特定的安装按钮 */}
                <div className="space-y-3">
                  {deviceInfo.isIOS && (
                    <div className="space-y-2">
                      <Button
                        color="primary"
                        size="lg"
                        onPress={handleIOSInstall}
                        className="w-full"
                        startContent={<span className="text-xl">🍎</span>}
                      >
                        安装到 iOS 主屏幕
                      </Button>
                      {!isSafari() && (
                        <div className="text-center">
                          <Chip color="warning" variant="flat" size="sm">
                            请使用 Safari 浏览器安装！
                          </Chip>
                        </div>
                      )}
                    </div>
                  )}

                  {deviceInfo.isAndroid && (
                    <Button
                      color="primary"
                      size="lg"
                      onPress={handleAndroidInstall}
                      className="w-full"
                      startContent={<span className="text-xl">🤖</span>}
                    >
                      安装 Android 应用
                    </Button>
                  )}

                  {!deviceInfo.isMobile && (
                    <Button
                      color="primary"
                      size="lg"
                      onPress={handleDesktopInstall}
                      className="w-full"
                      startContent={<span className="text-xl">💻</span>}
                    >
                      {isInstallable ? '桌面端 安装PWA应用' : '桌面端 安装指导'}
                    </Button>
                  )}

                  {/* 通用安装按钮（备用） */}
                  {isInstallable && (
                    <Button
                      color="secondary"
                      size="lg"
                      onPress={handleInstallPWA}
                      className="w-full"
                      variant="bordered"
                      startContent={<span className="text-xl">⬇️</span>}
                    >
                      通用PWA安装
                    </Button>
                  )}
                </div>

                {/* 错误提示 */}
                {installError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-800 text-sm">
                      <span className="font-medium">安装提示: </span>
                      {installError}
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardBody>
        </Card>

        {/* 设备信息 */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">设备信息</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">设备类型:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={deviceInfo.isMobile ? 'primary' : 'default'}
                    variant="flat"
                  >
                    {deviceInfo.isIOS ? 'iOS' : 
                     deviceInfo.isAndroid ? 'Android' : 
                     deviceInfo.isMobile ? '移动设备' : '桌面设备'}
                  </Chip>
                </div>
              </div>
              
              <div>
                <span className="text-gray-500">浏览器:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={['Chrome', 'Edge', 'Safari'].includes(deviceInfo.browser) ? 'success' : 'warning'}
                    variant="flat"
                  >
                    {deviceInfo.browser}
                  </Chip>
                </div>
              </div>
              
              <div>
                <span className="text-gray-500">PWA支持:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={isInstallable || deviceInfo.isIOS ? 'success' : 'warning'}
                    variant="flat"
                  >
                    {isInstallable || deviceInfo.isIOS ? '支持' : '有限支持'}
                  </Chip>
                </div>
              </div>
              
              <div>
                <span className="text-gray-500">安装状态:</span>
                <div className="mt-1">
                  <Chip
                    size="sm"
                    color={isInstalled ? 'success' : 'default'}
                    variant="flat"
                  >
                    {isInstalled ? '已安装' : '未安装'}
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 安装说明 */}
        {!isInstalled && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">{instructions.title}</h3>
            </CardHeader>
            <CardBody>
              <ol className="list-decimal list-inside space-y-2 text-sm mb-4">
                {instructions.steps.map((step, index) => (
                  <li key={index} className="text-gray-700">{step}</li>
                ))}
              </ol>
              
              <div className="bg-yellow-50 p-3 rounded-lg">
                <p className="text-yellow-800 text-sm">
                  💡 {instructions.note}
                </p>
              </div>
            </CardBody>
          </Card>
        )}

        {/* PWA优势 */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">PWA应用优势</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div className="flex items-start gap-3">
                <span className="text-2xl">⚡</span>
                <div>
                  <h4 className="font-semibold">快速启动</h4>
                  <p className="text-gray-600">像原生应用一样快速启动</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <span className="text-2xl">📱</span>
                <div>
                  <h4 className="font-semibold">原生体验</h4>
                  <p className="text-gray-600">全屏显示，无浏览器界面</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <span className="text-2xl">🔒</span>
                <div>
                  <h4 className="font-semibold">安全可靠</h4>
                  <p className="text-gray-600">HTTPS加密，数据安全</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <span className="text-2xl">💾</span>
                <div>
                  <h4 className="font-semibold">离线缓存</h4>
                  <p className="text-gray-600">部分功能支持离线使用</p>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 操作按钮 */}
        <Card>
          <CardBody>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                color="primary"
                variant="bordered"
                onPress={() => router.push('/wallet')}
                className="flex-1"
              >
                直接使用网页版
              </Button>
              
              <Button
                color="secondary"
                variant="bordered"
                onPress={() => router.push('/')}
                className="flex-1"
              >
                返回首页
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>


    </div>
  );
}
