'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Input, Divider } from '@heroui/react';
import QRCodeComponent from '@/components/QRCode';

export default function TestFixesPage() {
  const [qrValue, setQrValue] = useState('TAjz662ivGjK792yaiTaeJcthDD4wTtX4m');
  const [testResults, setTestResults] = useState<any>(null);

  const testQRCode = () => {
    setTestResults({
      qrTest: {
        success: true,
        message: 'QR码组件测试成功',
        value: qrValue
      }
    });
  };

  const testRegistration = async () => {
    try {
      // 测试普通邀请码注册
      const normalResponse = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: 'testuser_' + Date.now(),
          password: 'test123456',
          email: '<EMAIL>',
          name: '测试用户',
          inviteCode: 'NORMAL_CODE' // 假设的普通邀请码
        })
      });

      const normalResult = await normalResponse.json();

      // 测试超级管理员邀请码注册
      const adminResponse = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: 'admin_' + Date.now(),
          password: 'admin123456',
          email: '<EMAIL>',
          name: '管理员用户',
          inviteCode: 'SUPER_ADMIN_INIT'
        })
      });

      const adminResult = await adminResponse.json();

      setTestResults({
        registrationTest: {
          normal: {
            success: normalResponse.ok,
            result: normalResult,
            expectedRole: 'user'
          },
          admin: {
            success: adminResponse.ok,
            result: adminResult,
            expectedRole: 'super_admin'
          }
        }
      });

    } catch (error) {
      setTestResults({
        registrationTest: {
          error: error
        }
      });
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">修复验证测试</h1>
        </CardHeader>
        <CardBody className="space-y-6">
          {/* QR码测试 */}
          <Card className="bg-blue-50">
            <CardHeader>
              <h2 className="text-lg font-semibold">问题1: 收款二维码显示测试</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <Input
                label="钱包地址"
                value={qrValue}
                onChange={(e) => setQrValue(e.target.value)}
                placeholder="输入TRON地址"
              />
              
              <div className="flex justify-center">
                <div className="bg-white p-4 rounded-lg border">
                  <QRCodeComponent 
                    value={qrValue} 
                    size={200}
                    className="mx-auto"
                  />
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  地址: {qrValue}
                </p>
              </div>

              <Button
                color="primary"
                onPress={testQRCode}
                className="w-full"
              >
                测试QR码生成
              </Button>
            </CardBody>
          </Card>

          {/* 注册逻辑测试 */}
          <Card className="bg-green-50">
            <CardHeader>
              <h2 className="text-lg font-semibold">问题2: 注册用户角色测试</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <h3 className="font-semibold">普通邀请码</h3>
                  </CardHeader>
                  <CardBody>
                    <ul className="text-sm space-y-1">
                      <li>• 邀请码: NORMAL_CODE</li>
                      <li>• 期望角色: user (普通用户)</li>
                      <li>• 不应创建管理员记录</li>
                    </ul>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader>
                    <h3 className="font-semibold">超级管理员邀请码</h3>
                  </CardHeader>
                  <CardBody>
                    <ul className="text-sm space-y-1">
                      <li>• 邀请码: SUPER_ADMIN_INIT</li>
                      <li>• 期望角色: super_admin</li>
                      <li>• 应创建管理员记录</li>
                    </ul>
                  </CardBody>
                </Card>
              </div>

              <Button
                color="secondary"
                onPress={testRegistration}
                className="w-full"
              >
                测试注册逻辑
              </Button>
            </CardBody>
          </Card>

          {/* 测试结果 */}
          {testResults && (
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold">测试结果</h2>
              </CardHeader>
              <CardBody>
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </CardBody>
            </Card>
          )}

          <Divider />

          {/* 修复说明 */}
          <Card className="bg-yellow-50">
            <CardHeader>
              <h2 className="text-lg font-semibold">修复说明</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-blue-800">问题1: 收款二维码没有显示</h3>
                  <ul className="text-sm text-blue-700 space-y-1 mt-2">
                    <li>• 安装了 qrcode 和 @types/qrcode 库</li>
                    <li>• 创建了 QRCodeComponent 组件</li>
                    <li>• 更新了钱包页面使用真实的QR码</li>
                    <li>• 支持自定义大小和样式</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-green-800">问题2: 邀请码注册角色错误</h3>
                  <ul className="text-sm text-green-700 space-y-1 mt-2">
                    <li>• 修复了注册逻辑，区分普通用户和管理员</li>
                    <li>• 只有 SUPER_ADMIN_INIT 邀请码创建超级管理员</li>
                    <li>• 其他邀请码注册的都是普通用户</li>
                    <li>• 添加了角色信息到注册响应</li>
                  </ul>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* 使用说明 */}
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold">使用说明</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold">收款二维码</h3>
                  <p className="text-sm text-gray-600">
                    在钱包页面点击"收款"按钮，现在会显示真实的二维码，
                    其他人可以扫描此二维码获取您的钱包地址进行转账。
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold">用户注册</h3>
                  <p className="text-sm text-gray-600">
                    使用 SUPER_ADMIN_INIT 邀请码注册的用户会成为超级管理员，
                    使用其他邀请码注册的用户都是普通用户，不会获得管理员权限。
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        </CardBody>
      </Card>
    </div>
  );
}
