'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardBody, CardH<PERSON><PERSON>, <PERSON>, Divider } from '@heroui/react';

interface NetworkResult {
  success: boolean;
  network: string;
  config?: {
    fullHost: string;
    hasApiKey: boolean;
    apiKeyPrefix: string;
  };
  testResults?: {
    latestBlock: number;
    testAddress: string;
    accountExists: boolean;
    trxBalance: string;
  };
  error?: string;
  timestamp: string;
}

interface NetworkTestResponse {
  success: boolean;
  message: string;
  summary: {
    totalNetworks: number;
    successfulNetworks: number;
    failedNetworks: number;
  };
  results: Record<string, NetworkResult>;
  recommendations: Record<string, string>;
}

export default function NetworkStatusPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<NetworkTestResponse | null>(null);

  const testNetworkConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-network-config');
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('网络配置测试失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testNetworkConfig();
  }, []);

  const getNetworkName = (network: string) => {
    switch (network) {
      case 'mainnet': return '主网 (Mainnet)';
      case 'nile': return 'Nile测试网';
      case 'shasta': return 'Shasta测试网';
      default: return network;
    }
  };

  const getNetworkColor = (success: boolean) => {
    return success ? 'success' : 'danger';
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <Card>
        <CardHeader className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">TRON网络配置状态</h1>
          <Button
            color="primary"
            onPress={testNetworkConfig}
            isLoading={loading}
          >
            刷新测试
          </Button>
        </CardHeader>
        <CardBody className="space-y-6">
          {results && (
            <>
              {/* 总览 */}
              <Card className="bg-blue-50">
                <CardHeader>
                  <h2 className="text-lg font-semibold">测试总览</h2>
                </CardHeader>
                <CardBody>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {results.summary.totalNetworks}
                      </div>
                      <div className="text-sm text-gray-600">总网络数</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {results.summary.successfulNetworks}
                      </div>
                      <div className="text-sm text-gray-600">成功连接</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {results.summary.failedNetworks}
                      </div>
                      <div className="text-sm text-gray-600">连接失败</div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* 网络详情 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(results.results).map(([network, result]) => (
                  <Card key={network} className={result.success ? 'border-green-200' : 'border-red-200'}>
                    <CardHeader className="flex justify-between items-center">
                      <h3 className="font-semibold">{getNetworkName(network)}</h3>
                      <Chip
                        color={getNetworkColor(result.success)}
                        variant="flat"
                        size="sm"
                      >
                        {result.success ? '正常' : '异常'}
                      </Chip>
                    </CardHeader>
                    <CardBody className="space-y-3">
                      {result.success && result.config && result.testResults ? (
                        <>
                          <div>
                            <div className="text-sm font-medium">网络配置</div>
                            <div className="text-xs text-gray-600">
                              节点: {result.config.fullHost}
                            </div>
                            <div className="text-xs text-gray-600">
                              API密钥: {result.config.hasApiKey ? '已配置' : '未配置'}
                            </div>
                            {result.config.hasApiKey && (
                              <div className="text-xs text-gray-600">
                                密钥前缀: {result.config.apiKeyPrefix}
                              </div>
                            )}
                          </div>

                          <Divider />

                          <div>
                            <div className="text-sm font-medium">测试结果</div>
                            <div className="text-xs text-gray-600">
                              最新区块: {result.testResults.latestBlock.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-600">
                              测试余额: {parseFloat(result.testResults.trxBalance).toLocaleString()} TRX
                            </div>
                            <div className="text-xs text-gray-600">
                              账户状态: {result.testResults.accountExists ? '存在' : '不存在'}
                            </div>
                          </div>
                        </>
                      ) : (
                        <div className="text-red-600 text-sm">
                          错误: {result.error}
                        </div>
                      )}

                      <div className="text-xs text-gray-500">
                        更新时间: {new Date(result.timestamp).toLocaleString()}
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>

              {/* 建议 */}
              <Card className="bg-yellow-50">
                <CardHeader>
                  <h2 className="text-lg font-semibold">配置建议</h2>
                </CardHeader>
                <CardBody>
                  <div className="space-y-2">
                    {Object.entries(results.recommendations).map(([key, value]) => (
                      <div key={key} className="text-sm">
                        <span className="font-medium">{key === 'note' ? '注意' : getNetworkName(key)}:</span>
                        <span className="ml-2 text-gray-600">{value}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>

              {/* API密钥配置说明 */}
              <Card>
                <CardHeader>
                  <h2 className="text-lg font-semibold">API密钥配置说明</h2>
                </CardHeader>
                <CardBody>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-green-800">✅ 当前配置</h3>
                      <ul className="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• 主网 (Mainnet): 使用API密钥提高请求限制</li>
                        <li>• Nile测试网: 无需API密钥，直接连接</li>
                        <li>• Shasta测试网: 无需API密钥，直接连接</li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="font-medium text-blue-800">🔧 环境变量</h3>
                      <ul className="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• TRON_API_KEY: 主网API密钥</li>
                        <li>• TRON_NILE_API_KEY: Nile测试网API密钥 (可选)</li>
                        <li>• TRON_SHASTA_API_KEY: Shasta测试网API密钥 (可选)</li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="font-medium text-orange-800">⚠️ 注意事项</h3>
                      <ul className="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• 测试网通常有更宽松的请求限制</li>
                        <li>• 如果测试网出现限制，可申请专用API密钥</li>
                        <li>• 生产环境建议使用主网API密钥</li>
                      </ul>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
