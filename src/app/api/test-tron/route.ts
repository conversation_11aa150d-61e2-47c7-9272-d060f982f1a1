import { NextRequest, NextResponse } from 'next/server';
import { createTronWebInstance, getAccountBalance } from '@/lib/tronweb-server';

// 测试TRON API连接
export async function GET(request: NextRequest) {
  try {
    console.log('开始测试TRON API连接...');
    
    // 测试创建TronWeb实例
    const tronWeb = createTronWebInstance('mainnet');
    
    // 测试地址（USDT合约地址）
    const testAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
    
    console.log(`测试地址: ${testAddress}`);
    
    // 测试获取账户信息
    const accountInfo = await tronWeb.trx.getAccount(testAddress);
    console.log('账户信息获取成功:', accountInfo);
    
    // 测试获取TRX余额
    const trxBalance = await tronWeb.trx.getBalance(testAddress);
    const trxAmount = tronWeb.fromSun(trxBalance);
    console.log(`TRX余额: ${trxAmount}`);
    
    // 测试获取最新区块
    const latestBlock = await tronWeb.trx.getCurrentBlock();
    console.log(`最新区块: ${latestBlock.block_header.raw_data.number}`);
    
    return NextResponse.json({
      success: true,
      message: 'TRON API连接测试成功',
      data: {
        testAddress: testAddress,
        accountInfo: {
          address: accountInfo.address,
          balance: trxAmount + ' TRX'
        },
        latestBlock: latestBlock.block_header.raw_data.number,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error: any) {
    console.error('TRON API测试失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        stack: error.stack?.split('\n').slice(0, 5) // 只返回前5行堆栈
      }
    }, { status: 500 });
  }
}
