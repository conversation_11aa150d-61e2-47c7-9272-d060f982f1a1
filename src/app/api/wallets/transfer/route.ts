import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createWalletService, KeyEncryption } from '@/lib/wallet-service';

// 转账
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { walletId, toAddress, amount, currency, password } = body as {
      walletId: number;
      toAddress: string;
      amount: number;
      currency: 'TRX' | 'USDT';
      password: string;
    };

    if (!walletId || !toAddress?.trim() || !amount || !currency) {
      return NextResponse.json({ error: '转账信息不完整' }, { status: 400 });
    }

    if (amount <= 0) {
      return NextResponse.json({ error: '转账金额必须大于0' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 使用认证用户的ID
    const userId = authResult.user.id;

    // 获取钱包信息
    const wallet = await db.prepare(`
      SELECT id, name, address, private_key_encrypted, wallet_type, network
      FROM wallets
      WHERE id = ? AND user_id = ? AND is_active = TRUE
    `).bind(walletId, userId).first();

    if (!wallet) {
      return NextResponse.json({ error: '钱包不存在' }, { status: 404 });
    }

    // 解密私钥
    let privateKey = null;
    if (wallet.private_key_encrypted && password) {
      try {
        privateKey = KeyEncryption.decrypt(wallet.private_key_encrypted, password);
      } catch (error) {
        return NextResponse.json({ error: '钱包密码错误' }, { status: 400 });
      }
    }

    if (!privateKey) {
      return NextResponse.json({ error: '无法获取私钥，请检查密码' }, { status: 400 });
    }

    // 执行转账
    const network = wallet.network || 'nile'; // 使用钱包的网络，默认为nile
    console.log(`执行转账 - 网络: ${network}, 钱包: ${wallet.address}`);

    const walletService = createWalletService(network as 'mainnet' | 'nile' | 'shasta');
    const transferResult = await walletService.transfer({
      fromAddress: wallet.address,
      toAddress: toAddress.trim(),
      amount: parseFloat(amount),
      currency: currency as 'TRX' | 'USDT',
      privateKey: privateKey
    });

    if (transferResult.success && transferResult.txHash) {
      // 记录交易到数据库
      await db.prepare(`
        INSERT INTO transactions (
          user_id, wallet_id, tx_hash, from_address, to_address,
          amount, currency, tx_type, status, network, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'send', 'pending', ?, ?)
      `).bind(
        userId,
        walletId,
        transferResult.txHash,
        wallet.address,
        toAddress.trim(),
        amount,
        currency,
        network,
        new Date().toISOString()
      ).run();

      // 异步等待交易确认
      setTimeout(async () => {
        try {
          const confirmed = await walletService.waitForConfirmation(transferResult.txHash!, 300000); // 5分钟超时
          const status = confirmed ? 'confirmed' : 'failed';
          
          await db.prepare(`
            UPDATE transactions 
            SET status = ?, confirmed_at = ?
            WHERE tx_hash = ?
          `).bind(
            status,
            confirmed ? new Date().toISOString() : null,
            transferResult.txHash
          ).run();
        } catch (error) {
          console.error('更新交易状态失败:', error);
        }
      }, 0);
    }

    return NextResponse.json(transferResult);
  } catch (error: any) {
    console.error('转账失败:', error);
    return NextResponse.json({ 
      error: error.message || '转账失败' 
    }, { status: 500 });
  }
}
