import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getAccountBalance } from '@/lib/tronweb-server';

// 获取钱包余额 (支持GET和POST)
export async function GET(request: NextRequest) {
  return handleBalanceRequest(request, 'GET');
}

export async function POST(request: NextRequest) {
  return handleBalanceRequest(request, 'POST');
}

async function handleBalanceRequest(request: NextRequest, method: 'GET' | 'POST') {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        success: false,
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    let address: string, currency: string, network: string;

    if (method === 'GET') {
      // GET请求从URL参数获取
      const { searchParams } = new URL(request.url);
      address = searchParams.get('address') || '';
      currency = searchParams.get('currency') || 'USDT';
      network = searchParams.get('network') || 'mainnet';
    } else {
      // POST请求从请求体获取
      const body = await request.json();
      const bodyData = body as {
        address: string;
        currency: string;
        network?: string;
      };
      address = bodyData.address;
      currency = bodyData.currency;
      network = bodyData.network || 'mainnet';
    }

    if (!address?.trim()) {
      return NextResponse.json({ success: false, error: '钱包地址不能为空' }, { status: 400 });
    }

    if (!currency || !['TRX', 'USDT'].includes(currency)) {
      return NextResponse.json({ success: false, error: '无效的币种' }, { status: 400 });
    }

    try {
      const balance = await getAccountBalance(address, currency as 'TRX' | 'USDT', network as 'mainnet' | 'nile' | 'shasta');
      
      return NextResponse.json({
        success: true,
        balance: balance,
        currency: currency,
        address: address,
        network: network || 'mainnet'
      });
    } catch (error: any) {
      console.error('获取余额失败:', error);
      return NextResponse.json({
        success: false,
        error: `获取${currency}余额失败: ${error.message}`
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('余额查询API失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message || '余额查询失败'
    }, { status: 500 });
  }
}
