import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createWalletService, KeyEncryption } from '@/lib/wallet-service';

// 创建新钱包
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { name, network = 'mainnet', password } = body as { name: string; network?: string; password?: string };

    if (!name?.trim()) {
      return NextResponse.json({ error: '钱包名称不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 使用认证用户的ID
    const userId = authResult.user.id;

    // 检查钱包名称是否重复
    const existingWallet = await db.prepare(`
      SELECT id FROM wallets WHERE user_id = ? AND name = ? AND is_active = TRUE
    `).bind(userId, name.trim()).first();

    if (existingWallet) {
      return NextResponse.json({ error: '钱包名称已存在' }, { status: 400 });
    }

    // 创建钱包
    const walletService = createWalletService(network as any);
    const walletInfo = await walletService.createWallet({ name: name.trim(), network: network as any });

    // 加密私钥（如果提供了密码）
    let encryptedPrivateKey = null;
    if (password && walletInfo.privateKey) {
      encryptedPrivateKey = KeyEncryption.encrypt(walletInfo.privateKey, password);
    }

    // 检查是否是用户的第一个钱包
    const walletCount = await db.prepare(`
      SELECT COUNT(*) as count FROM wallets WHERE user_id = ? AND is_active = TRUE
    `).bind(userId).first();

    const isFirstWallet = walletCount?.count === 0;

    // 保存钱包到数据库
    const result = await db.prepare(`
      INSERT INTO wallets (user_id, name, address, private_key_encrypted, wallet_type, network, is_default, is_active)
      VALUES (?, ?, ?, ?, 'created', ?, ?, TRUE)
    `).bind(
      userId,
      name.trim(),
      walletInfo.address,
      encryptedPrivateKey,
      network,
      isFirstWallet
    ).run();

    // 返回钱包信息（不包含私钥）
    const responseData = {
      id: result.meta.last_row_id,
      name: name.trim(),
      address: walletInfo.address,
      wallet_type: 'created',
      is_default: isFirstWallet,
      network: network
    };

    // 如果没有提供密码，在响应中包含私钥（仅此一次）
    if (!password && walletInfo.privateKey) {
      (responseData as any).privateKey = walletInfo.privateKey;
    }

    return NextResponse.json(responseData);
  } catch (error: any) {
    console.error('创建钱包失败:', error);
    return NextResponse.json({ 
      error: error.message || '创建钱包失败' 
    }, { status: 500 });
  }
}
