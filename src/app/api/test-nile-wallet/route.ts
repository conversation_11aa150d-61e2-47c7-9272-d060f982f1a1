import { NextRequest, NextResponse } from 'next/server';
import { createTronWebInstance, getAccountBalance } from '@/lib/tronweb-server';

// 测试Nile网络钱包功能
export async function GET(request: NextRequest) {
  try {
    console.log('开始测试Nile网络钱包功能...');
    
    // 创建Nile网络TronWeb实例
    const tronWeb = createTronWebInstance('nile');
    
    // 创建一个测试钱包
    const account = await tronWeb.createAccount();
    const testAddress = account.address.base58;
    const testPrivateKey = account.privateKey;
    
    console.log(`创建测试钱包 - 地址: ${testAddress}`);
    
    // 测试余额查询
    const trxBalance = await getAccountBalance(testAddress, 'TRX', 'nile');
    const usdtBalance = await getAccountBalance(testAddress, 'USDT', 'nile');
    
    console.log(`TRX余额: ${trxBalance}, USDT余额: ${usdtBalance}`);
    
    // 测试一个有余额的地址
    const richAddress = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'; // Nile测试网USDT合约
    const richTrxBalance = await getAccountBalance(richAddress, 'TRX', 'nile');
    const richUsdtBalance = await getAccountBalance(richAddress, 'USDT', 'nile');
    
    return NextResponse.json({
      success: true,
      message: 'Nile网络钱包功能测试成功',
      network: 'nile',
      data: {
        newWallet: {
          address: testAddress,
          privateKey: testPrivateKey.substring(0, 10) + '...', // 只显示前10位
          trxBalance: trxBalance,
          usdtBalance: usdtBalance
        },
        richWallet: {
          address: richAddress,
          trxBalance: richTrxBalance,
          usdtBalance: richUsdtBalance
        },
        networkInfo: {
          name: 'Nile Testnet',
          explorer: 'https://nile.tronscan.org',
          usdtContract: 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'
        },
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error: any) {
    console.error('Nile网络钱包测试失败:', error);
    
    return NextResponse.json({
      success: false,
      network: 'nile',
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        stack: error.stack?.split('\n').slice(0, 5)
      }
    }, { status: 500 });
  }
}
