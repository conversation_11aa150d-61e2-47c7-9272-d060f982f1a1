import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { createWalletService } from '@/lib/wallet-service';

// 测试钱包余额获取
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const network = searchParams.get('network') || 'nile';

    if (!address) {
      return NextResponse.json({ error: '请提供钱包地址' }, { status: 400 });
    }

    console.log(`测试获取钱包余额 - 地址: ${address}, 网络: ${network}`);

    try {
      const walletService = createWalletService(network);
      const balance = await walletService.getWalletBalance(address);
      
      console.log('余额获取成功:', balance);

      return NextResponse.json({
        success: true,
        data: {
          address,
          network,
          balance,
          hasUSDT: balance.USDT > 0,
          canRecharge: balance.USDT >= 10
        }
      });
    } catch (error: any) {
      console.error('余额获取失败:', error);
      return NextResponse.json({
        success: false,
        error: '余额获取失败',
        details: error.message
      });
    }
  } catch (error: any) {
    console.error('测试API失败:', error);
    return NextResponse.json({ 
      error: '测试失败', 
      details: error.message 
    }, { status: 500 });
  }
}
