import { NextRequest, NextResponse } from 'next/server';
import { createTronWebInstance } from '@/lib/tronweb-server';

// 调试USDT余额获取
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { address, network = 'nile' } = body as {
      address: string;
      network?: 'mainnet' | 'nile' | 'shasta';
    };

    if (!address?.trim()) {
      return NextResponse.json({ error: '地址不能为空' }, { status: 400 });
    }

    console.log(`调试USDT余额 - 地址: ${address}, 网络: ${network}`);

    const tronWeb = createTronWebInstance(network);
    
    // 已知的USDT合约地址
    const knownUsdtContracts = {
      mainnet: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
      nile: [
        'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf', // 当前配置的合约
        'TXLAQ63Xg1NAzckPwKHvzw7CSEmLMEqcdj', // 另一个可能的USDT合约
        'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'  // 可能的合约
      ],
      shasta: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'
    };

    const results: any = {};

    // 测试TRX余额
    try {
      const trxBalance = await tronWeb.trx.getBalance(address);
      const trxAmount = tronWeb.fromSun(trxBalance);
      results.trx = {
        success: true,
        balance: trxAmount,
        raw: trxBalance
      };
    } catch (error: any) {
      results.trx = {
        success: false,
        error: error.message
      };
    }

    // 测试不同的USDT合约
    const contractsToTest = network === 'nile' ? knownUsdtContracts.nile : [knownUsdtContracts[network]];
    
    for (let i = 0; i < contractsToTest.length; i++) {
      const contractAddress = contractsToTest[i];
      const contractKey = `usdt_contract_${i + 1}`;
      
      try {
        console.log(`测试USDT合约 ${i + 1}: ${contractAddress}`);
        
        // 检查合约是否存在
        const contractAccount = await tronWeb.trx.getAccount(contractAddress);
        
        if (!contractAccount.address) {
          results[contractKey] = {
            success: false,
            contractAddress: contractAddress,
            error: '合约不存在'
          };
          continue;
        }

        // 尝试获取合约信息
        const contract = await tronWeb.contract().at(contractAddress);
        
        // 尝试调用balanceOf
        const balance = await contract.balanceOf(address).call();
        
        console.log(`合约 ${contractAddress} 余额数据:`, balance);
        
        // 处理余额数据
        let balanceNumber = 0;
        let rawBalance = '';

        if (balance && typeof balance === 'object') {
          if (balance.toString) {
            rawBalance = balance.toString();
            balanceNumber = parseFloat(rawBalance);
          } else if (balance.toNumber) {
            balanceNumber = balance.toNumber();
            rawBalance = balanceNumber.toString();
          } else if (balance._hex) {
            balanceNumber = parseInt(balance._hex, 16);
            rawBalance = balance._hex;
          }
        } else if (typeof balance === 'string') {
          rawBalance = balance;
          balanceNumber = parseFloat(balance);
        } else if (typeof balance === 'number') {
          balanceNumber = balance;
          rawBalance = balance.toString();
        } else if (typeof balance === 'bigint') {
          rawBalance = balance.toString();
          balanceNumber = Number(balance);
        }

        const usdtAmount = balanceNumber / Math.pow(10, 6);

        results[contractKey] = {
          success: true,
          contractAddress: contractAddress,
          balance: usdtAmount,
          raw: rawBalance, // 使用字符串而不是原始对象
          balanceNumber: balanceNumber
        };

        // 尝试获取合约名称和符号
        try {
          const name = await contract.name().call();
          const symbol = await contract.symbol().call();
          const decimals = await contract.decimals().call();
          
          results[contractKey].contractInfo = {
            name: name,
            symbol: symbol,
            decimals: decimals.toString()
          };
        } catch (infoError) {
          console.log(`无法获取合约 ${contractAddress} 的信息:`, infoError);
        }

      } catch (error: any) {
        console.error(`合约 ${contractAddress} 测试失败:`, error);
        results[contractKey] = {
          success: false,
          contractAddress: contractAddress,
          error: error.message
        };
      }
    }

    // 获取账户的所有TRC20代币
    try {
      const accountInfo = await tronWeb.trx.getAccount(address);
      results.accountInfo = {
        address: accountInfo.address,
        balance: accountInfo.balance ? tronWeb.fromSun(accountInfo.balance) : 0,
        assetV2: accountInfo.assetV2 || {},
        frozenV2: accountInfo.frozenV2 || []
      };
    } catch (error: any) {
      results.accountInfo = {
        error: error.message
      };
    }

    return NextResponse.json({
      success: true,
      address: address,
      network: network,
      results: results,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('USDT余额调试失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack?.split('\n').slice(0, 5)
    }, { status: 500 });
  }
}
