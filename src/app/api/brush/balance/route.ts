import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取用户余额信息
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return NextResponse.json({ error: '刷单功能未启用' }, { status: 403 });
    }

    const userId = authResult.user.id;

    // 获取用户余额
    let userBalance = await dbService.getUserBalance(userId);
    if (!userBalance) {
      // 如果用户余额不存在，创建一个
      userBalance = await dbService.createUserBalance(userId);
    }

    // 获取用户刷单统计
    let userStats = await dbService.getUserBrushStats(userId);
    if (!userStats) {
      userStats = await dbService.createUserBrushStats(userId);
    }

    // 格式化返回数据（将分转换为元）
    const responseData = {
      balance: userBalance.balance / 100, // 可用余额
      frozenBalance: userBalance.frozen_balance / 100, // 冻结余额
      totalBalance: (userBalance.balance + userBalance.frozen_balance) / 100, // 总余额
      totalRecharged: userBalance.total_recharged / 100, // 累计充值
      totalSpent: userBalance.total_spent / 100, // 累计消费
      stats: {
        totalOrders: userStats.total_orders,
        completedOrders: userStats.completed_orders,
        burstOrders: userStats.burst_orders,
        totalCommission: userStats.total_commission / 100,
        todayOrders: userStats.today_orders,
        todayCommission: userStats.today_commission / 100,
        lastOrderAt: userStats.last_order_at
      }
    };

    return NextResponse.json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('获取用户余额失败:', error);
    return NextResponse.json({ error: '获取用户余额失败' }, { status: 500 });
  }
}

// 获取余额变动记录
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { action, limit = 50 } = body;

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    const userId = authResult.user.id;

    if (action === 'logs') {
      // 获取余额变动记录
      const logs = await dbService.getUserBalanceLogs(userId, limit);
      
      // 格式化记录
      const formattedLogs = logs.map(log => ({
        ...log,
        amount: log.amount / 100,
        balance_before: log.balance_before / 100,
        balance_after: log.balance_after / 100,
        type_name: getBalanceLogTypeName(log.type)
      }));

      return NextResponse.json({
        success: true,
        data: formattedLogs
      });
    } else if (action === 'recharge_records') {
      // 获取充值记录
      const records = await dbService.getUserRechargeRecords(userId, limit);
      
      // 格式化记录
      const formattedRecords = records.map(record => ({
        ...record,
        internal_amount: record.internal_amount / 100,
        status_name: getRechargeStatusName(record.status)
      }));

      return NextResponse.json({
        success: true,
        data: formattedRecords
      });
    } else {
      return NextResponse.json({ error: '无效的操作类型' }, { status: 400 });
    }
  } catch (error) {
    console.error('获取记录失败:', error);
    return NextResponse.json({ error: '获取记录失败' }, { status: 500 });
  }
}

// 辅助函数：获取余额变动类型名称
function getBalanceLogTypeName(type: number): string {
  const typeNames: { [key: number]: string } = {
    1: '充值',
    2: '消费',
    3: '佣金收入',
    4: '退款',
    5: '冻结',
    6: '解冻'
  };
  return typeNames[type] || '未知';
}

// 辅助函数：获取充值状态名称
function getRechargeStatusName(status: number): string {
  const statusNames: { [key: number]: string } = {
    0: '待确认',
    1: '已确认',
    2: '失败'
  };
  return statusNames[status] || '未知';
}
