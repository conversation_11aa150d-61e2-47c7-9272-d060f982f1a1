import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { getTransactionInfo, isValidTronAddress } from '@/lib/tronweb-server';

// 获取充值信息（平台钱包地址等）
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { searchParams } = new URL(request.url);
    const network = searchParams.get('network') || 'mainnet';

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return NextResponse.json({ error: '刷单功能未启用' }, { status: 403 });
    }

    // 获取平台收款钱包地址
    const platformWallet = await dbService.getDefaultPlatformWallet(network, 'USDT');
    if (!platformWallet) {
      return NextResponse.json({ error: '暂无可用的收款地址' }, { status: 500 });
    }

    // 获取汇率和最小充值金额
    const exchangeRateConfig = await dbService.getSystemConfig('brush_exchange_rate');
    const minRechargeConfig = await dbService.getSystemConfig('brush_min_recharge');

    const exchangeRate = exchangeRateConfig ? parseFloat(exchangeRateConfig.config_value) : 1.0;
    const minRecharge = minRechargeConfig ? parseFloat(minRechargeConfig.config_value) : 10;

    return NextResponse.json({
      success: true,
      data: {
        platformAddress: platformWallet.address,
        network: network,
        currency: 'USDT',
        exchangeRate: exchangeRate,
        minRecharge: minRecharge,
        description: `请向以上地址转入USDT，系统将自动为您充值到内部账户。汇率: 1 USDT = ${exchangeRate} 内部币，最小充值金额: ${minRecharge} USDT`
      }
    });
  } catch (error) {
    console.error('获取充值信息失败:', error);
    return NextResponse.json({ error: '获取充值信息失败' }, { status: 500 });
  }
}

// 手动提交充值记录（用户提交交易哈希）
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { txHash, amount, fromAddress, network = 'mainnet' } = body as {
      txHash: string;
      amount: number;
      fromAddress: string;
      network?: string;
    };

    if (!txHash || !amount || !fromAddress) {
      return NextResponse.json({
        success: false,
        error: '交易哈希、金额和发送地址不能为空'
      }, { status: 400 });
    }

    if (amount <= 0) {
      return NextResponse.json({
        success: false,
        error: '充值金额必须大于0'
      }, { status: 400 });
    }

    // 验证TRON地址格式
    if (!isValidTronAddress(fromAddress)) {
      return NextResponse.json({
        success: false,
        error: '发送地址格式无效'
      }, { status: 400 });
    }

    // 验证交易哈希格式（TRON交易哈希是64位十六进制字符串）
    if (!/^[a-fA-F0-9]{64}$/.test(txHash)) {
      return NextResponse.json({
        success: false,
        error: '交易哈希格式无效'
      }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return NextResponse.json({
        success: false,
        error: '刷单功能未启用'
      }, { status: 403 });
    }

    const userId = authResult.user.id;

    // 检查交易哈希是否已存在
    const existingRecord = await dbService.getRechargeRecordByTxHash(txHash);
    if (existingRecord) {
      return NextResponse.json({
        success: false,
        error: '该交易哈希已存在'
      }, { status: 400 });
    }

    // 获取平台收款地址
    const platformWallet = await dbService.getDefaultPlatformWallet(network, 'USDT');
    if (!platformWallet) {
      return NextResponse.json({
        success: false,
        error: '暂无可用的收款地址'
      }, { status: 500 });
    }

    // 验证交易真实性
    try {
      console.log(`验证交易: ${txHash} 在网络 ${network}`);
      const txInfo = await getTransactionInfo(txHash, network as 'mainnet' | 'nile' | 'shasta');

      if (!txInfo || !txInfo.id) {
        return NextResponse.json({
          success: false,
          error: '交易不存在或尚未确认，请稍后再试'
        }, { status: 400 });
      }

      // 检查交易是否成功
      if (txInfo.result !== 'SUCCESS') {
        return NextResponse.json({
          success: false,
          error: '交易执行失败'
        }, { status: 400 });
      }

      // TODO: 进一步验证交易详情
      // - 验证接收地址是否为平台地址
      // - 验证转账金额是否匹配
      // - 验证是否为USDT转账
      console.log('交易验证通过:', txInfo);

    } catch (error: any) {
      console.error('交易验证失败:', error);
      return NextResponse.json({
        success: false,
        error: '无法验证交易，请确认交易哈希正确且交易已确认'
      }, { status: 400 });
    }

    // 检查最小充值金额
    const minRechargeConfig = await dbService.getSystemConfig('brush_min_recharge');
    const minRecharge = minRechargeConfig ? parseFloat(minRechargeConfig.config_value) : 10;

    if (amount < minRecharge) {
      return NextResponse.json({
        success: false,
        error: `充值金额不能少于 ${minRecharge} USDT`
      }, { status: 400 });
    }

    // 获取汇率
    const exchangeRateConfig = await dbService.getSystemConfig('brush_exchange_rate');
    const exchangeRate = exchangeRateConfig ? parseFloat(exchangeRateConfig.config_value) : 1.0;
    
    // 计算内部金额（分）
    const internalAmount = Math.floor(amount * exchangeRate * 100);

    try {
      // 创建充值记录（待确认状态）
      const rechargeRecord = await dbService.createRechargeRecord({
        userId,
        txHash,
        fromAddress,
        toAddress: platformWallet.address,
        usdtAmount: amount,
        internalAmount,
        exchangeRate,
        network
      });

      return NextResponse.json({
        success: true,
        data: {
          id: rechargeRecord.id,
          txHash: rechargeRecord.tx_hash,
          amount: amount,
          internalAmount: internalAmount / 100,
          status: 0,
          statusName: '待确认',
          message: '充值记录已提交，请等待系统确认'
        }
      });

    } catch (error) {
      console.error('创建充值记录失败:', error);
      return NextResponse.json({
        success: false,
        error: '创建充值记录失败'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('提交充值记录失败:', error);
    return NextResponse.json({
      success: false,
      error: '提交充值记录失败'
    }, { status: 500 });
  }
}

// 确认充值（管理员或系统调用）
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { txHash, status, blockNumber } = body as {
      txHash: string;
      status: number;
      blockNumber?: string;
    };

    if (!txHash || status === undefined) {
      return NextResponse.json({
        success: false,
        error: '交易哈希和状态不能为空'
      }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 查找充值记录
    const rechargeRecord = await dbService.getRechargeRecordByTxHash(txHash);
    if (!rechargeRecord) {
      return NextResponse.json({ error: '充值记录不存在' }, { status: 404 });
    }

    if (rechargeRecord.status !== 0) {
      return NextResponse.json({ error: '该充值记录已处理' }, { status: 400 });
    }

    try {
      // 更新充值记录状态
      await dbService.updateRechargeRecordStatus(rechargeRecord.id, status, blockNumber ? parseInt(blockNumber) : undefined);

      if (status === 1) { // 确认充值
        // 处理用户充值
        const success = await dbService.processUserRecharge(
          rechargeRecord.user_id,
          rechargeRecord.usdt_amount,
          rechargeRecord.tx_hash,
          rechargeRecord.from_address,
          rechargeRecord.to_address,
          rechargeRecord.network
        );

        if (!success) {
          return NextResponse.json({ error: '处理用户充值失败' }, { status: 500 });
        }

        return NextResponse.json({
          success: true,
          message: '充值确认成功，用户余额已更新'
        });
      } else if (status === 2) { // 充值失败
        return NextResponse.json({
          success: true,
          message: '充值已标记为失败'
        });
      } else {
        return NextResponse.json({ error: '无效的状态值' }, { status: 400 });
      }

    } catch (error) {
      console.error('确认充值失败:', error);
      return NextResponse.json({ error: '确认充值失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('确认充值失败:', error);
    return NextResponse.json({ error: '确认充值失败' }, { status: 500 });
  }
}
