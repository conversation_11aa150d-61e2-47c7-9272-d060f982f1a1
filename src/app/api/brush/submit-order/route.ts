import { NextRequest } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-response';

// 提交刷单订单
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse(
        authResult.error || '未登录',
        authResult.status || 401
      );
    }

    const body = await request.json();
    const { orderId } = body as { orderId: number };

    if (!orderId) {
      return createErrorResponse('订单ID不能为空', 400);
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return createErrorResponse('刷单功能未启用', 403);
    }

    // 获取订单信息
    const order = await db.prepare(`
      SELECT * FROM brush_orders 
      WHERE id = ? AND user_id = ? AND status = 0
    `).bind(orderId, authResult.user.id).first() as any;

    if (!order) {
      return createErrorResponse('订单不存在或已处理', 404);
    }

    // 获取用户余额
    const userBalance = await db.prepare(`
      SELECT balance FROM user_balances WHERE user_id = ?
    `).bind(authResult.user.id).first() as { balance: number } | null;

    if (!userBalance || userBalance.balance < order.total_amount) {
      return createErrorResponse('余额不足', 400);
    }

    // 开始事务处理
    try {
      // 扣除用户余额
      const newBalance = userBalance.balance - order.total_amount;
      await db.prepare(`
        UPDATE user_balances 
        SET balance = ?, total_spent = total_spent + ?, updated_at = ?
        WHERE user_id = ?
      `).bind(
        newBalance,
        order.total_amount,
        new Date().toISOString(),
        authResult.user.id
      ).run();

      // 更新订单状态为已付款
      await db.prepare(`
        UPDATE brush_orders 
        SET status = 1, updated_at = ?
        WHERE id = ?
      `).bind(
        new Date().toISOString(),
        orderId
      ).run();

      // 记录余额变动
      await db.prepare(`
        INSERT INTO balance_logs (
          user_id, type, amount, balance_before, balance_after,
          related_id, related_type, description, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        authResult.user.id,
        2, // 2: 消费
        order.total_amount,
        userBalance.balance,
        newBalance,
        orderId,
        'brush_order',
        `刷单订单支付 - ${order.product_name}`,
        new Date().toISOString()
      ).run();

      // 更新用户刷单统计
      const today = new Date().toISOString().split('T')[0];
      await db.prepare(`
        INSERT INTO user_brush_stats (
          user_id, total_orders, total_spent, today_orders, today_spent,
          last_order_at, stats_date, created_at, updated_at
        ) VALUES (?, 1, ?, 1, ?, ?, ?, ?, ?)
        ON CONFLICT(user_id) DO UPDATE SET
          total_orders = total_orders + 1,
          total_spent = total_spent + ?,
          today_orders = CASE 
            WHEN stats_date = ? THEN today_orders + 1 
            ELSE 1 
          END,
          today_spent = CASE 
            WHEN stats_date = ? THEN today_spent + ? 
            ELSE ? 
          END,
          last_order_at = ?,
          stats_date = ?,
          updated_at = ?
      `).bind(
        authResult.user.id,
        order.total_amount,
        order.total_amount,
        new Date().toISOString(),
        today,
        new Date().toISOString(),
        new Date().toISOString(),
        order.total_amount,
        today,
        today,
        order.total_amount,
        order.total_amount,
        new Date().toISOString(),
        today,
        new Date().toISOString()
      ).run();

      // 如果不是爆单，设置自动完成时间（模拟订单处理）
      if (!order.is_burst) {
        // 随机1-5分钟后自动完成订单
        const completeTime = new Date(Date.now() + (1 + Math.random() * 4) * 60 * 1000);
        
        // 这里可以设置一个定时任务或者使用队列来处理订单完成
        // 暂时先标记为已付款状态，后续可以添加自动完成逻辑
      }

      return createSuccessResponse({
        orderId: order.id,
        orderNo: order.order_no,
        productName: order.product_name,
        totalAmount: order.total_amount / 100,
        newBalance: newBalance / 100,
        isBurst: order.is_burst === 1,
        message: order.is_burst === 1 
          ? '⚠️ 爆单订单已提交，请等待系统处理' 
          : '✅ 订单提交成功，系统正在处理中...'
      });

    } catch (error) {
      console.error('订单提交事务失败:', error);
      return createErrorResponse('订单提交失败，请重试', 500);
    }

  } catch (error) {
    console.error('提交订单失败:', error);
    return createErrorResponse('提交订单失败', 500);
  }
}
