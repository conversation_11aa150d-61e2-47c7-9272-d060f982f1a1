import { NextRequest } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-response';

// 获取刷单订单（系统自动匹配商品）
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse(
        authResult.error || '未登录',
        authResult.status || 401
      );
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return createErrorResponse('刷单功能未启用', 403);
    }

    // 获取用户余额
    const userBalance = await db.prepare(`
      SELECT balance FROM user_balances WHERE user_id = ?
    `).bind(authResult.user.id).first() as { balance: number } | null;

    if (!userBalance || userBalance.balance < 1000) { // 最少10元（1000分）
      return createErrorResponse('余额不足，请先充值', 400);
    }

    // 获取用户今日订单数
    const today = new Date().toISOString().split('T')[0];
    const todayOrdersResult = await db.prepare(`
      SELECT COUNT(*) as count FROM brush_orders 
      WHERE user_id = ? AND DATE(created_at) = ?
    `).bind(authResult.user.id, today).first() as { count: number };

    // 获取刷单规则
    const brushRule = await db.prepare(`
      SELECT * FROM brush_rules WHERE is_active = 1 ORDER BY id LIMIT 1
    `).first() as any;

    if (!brushRule) {
      return createErrorResponse('暂无可用的刷单规则', 500);
    }

    // 检查今日订单限制
    if (todayOrdersResult.count >= brushRule.daily_order_limit) {
      return createErrorResponse(`今日订单已达上限（${brushRule.daily_order_limit}单）`, 400);
    }

    // 随机选择一个商品
    const products = await db.prepare(`
      SELECT * FROM products WHERE status = 1 ORDER BY RANDOM() LIMIT 1
    `).first() as any;

    if (!products) {
      return createErrorResponse('暂无可用商品', 500);
    }

    // 检查用户余额是否足够购买该商品
    if (userBalance.balance < products.price) {
      return createErrorResponse('余额不足购买此商品', 400);
    }

    // 计算佣金比例（在规则范围内随机）
    const minRate = brushRule.min_commission_rate;
    const maxRate = brushRule.max_commission_rate;
    const commissionRate = minRate + Math.random() * (maxRate - minRate);
    const commissionAmount = Math.floor(products.price * commissionRate);

    // 判断是否为爆单
    const orderRange = brushRule.burst_order_range.split('-');
    const minOrder = parseInt(orderRange[0]);
    const maxOrder = parseInt(orderRange[1]);
    const isBurst = todayOrdersResult.count >= minOrder - 1 && 
                   todayOrdersResult.count <= maxOrder - 1 && 
                   Math.random() < brushRule.burst_probability;

    // 生成订单号
    const orderNo = 'BO' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();

    // 创建订单（待确认状态）
    const order = await db.prepare(`
      INSERT INTO brush_orders (
        user_id, product_id, order_no, product_name, product_image, product_price,
        quantity, total_amount, commission_rate, commission_amount,
        status, is_burst, burst_reason, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      RETURNING *
    `).bind(
      authResult.user.id,
      products.id,
      orderNo,
      products.name,
      products.image,
      products.price,
      1, // 数量固定为1
      products.price,
      commissionRate,
      commissionAmount,
      0, // 0: 待确认
      isBurst ? 1 : 0,
      isBurst ? '系统随机爆单' : null,
      new Date().toISOString(),
      new Date().toISOString()
    ).first() as any;

    // 返回订单信息
    return createSuccessResponse({
      order: {
        id: order.id,
        orderNo: order.order_no,
        productName: order.product_name,
        productPrice: order.product_price / 100, // 转换为元
        quantity: order.quantity,
        totalAmount: order.total_amount / 100, // 转换为元
        commissionRate: order.commission_rate,
        commissionAmount: order.commission_amount / 100, // 转换为元
        isBurst: order.is_burst === 1,
        burstReason: order.burst_reason,
        status: order.status,
        createdAt: order.created_at
      },
      product: {
        id: products.id,
        name: products.name,
        description: products.description,
        image: products.image,
        price: products.price / 100 // 转换为元
      },
      userBalance: userBalance.balance / 100, // 转换为元
      message: isBurst ? '⚠️ 爆单警告：此订单为爆单，需要额外支付但无佣金收入' : '✅ 订单生成成功，确认后可获得佣金收入'
    });

  } catch (error) {
    console.error('获取订单失败:', error);
    return createErrorResponse('获取订单失败', 500);
  }
}
