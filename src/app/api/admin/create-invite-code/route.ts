import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 生成随机邀请码
function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 创建邀请码
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;
    
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const body = await request.json();
    const { maxUses, expiresIn } = body as {
      maxUses: number;
      expiresIn: number; // 天数
    };

    // 验证参数
    if (maxUses < -1 || maxUses === 0) {
      return NextResponse.json({ error: '最大使用次数必须大于0或等于-1（无限制）' }, { status: 400 });
    }

    if (expiresIn < 0) {
      return NextResponse.json({ error: '有效期不能为负数' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT 
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    // 生成唯一的邀请码
    let inviteCode = '';
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      inviteCode = generateInviteCode();
      
      // 检查邀请码是否已存在
      const existingCode = await db.prepare(`
        SELECT id FROM invite_codes WHERE code = ?
      `).bind(inviteCode).first();

      if (!existingCode) {
        break;
      }
      
      attempts++;
    }

    if (attempts >= maxAttempts) {
      return NextResponse.json({ error: '生成邀请码失败，请重试' }, { status: 500 });
    }

    // 计算过期时间
    let expiresAt = null;
    if (expiresIn > 0) {
      const expireDate = new Date();
      expireDate.setDate(expireDate.getDate() + expiresIn);
      expiresAt = expireDate.toISOString();
    }

    // 创建邀请码
    const result = await db.prepare(`
      INSERT INTO invite_codes (code, created_by, is_active, max_uses, used_count, expires_at)
      VALUES (?, ?, TRUE, ?, 0, ?)
    `).bind(
      inviteCode,
      decoded.userId,
      maxUses,
      expiresAt
    ).run();

    return NextResponse.json({
      success: true,
      code: inviteCode,
      id: result.meta.last_row_id,
      maxUses: maxUses,
      expiresAt: expiresAt,
      message: '邀请码创建成功'
    });

  } catch (error: any) {
    console.error('创建邀请码失败:', error);
    return NextResponse.json({ 
      error: error.message || '创建邀请码失败' 
    }, { status: 500 });
  }
}
