import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取刷单统计数据
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'today'; // today, week, month, all

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 获取基础统计数据
      const stats = await getBrushStats(dbService, period);
      
      // 获取用户排行榜
      const userRanking = await getUserRanking(dbService, period);
      
      // 获取商品销售统计
      const productStats = await getProductStats(dbService, period);
      
      // 获取充值统计
      const rechargeStats = await getRechargeStats(dbService, period);

      return NextResponse.json({
        success: true,
        data: {
          overview: stats,
          userRanking,
          productStats,
          rechargeStats
        }
      });
    } catch (error) {
      console.error('获取刷单统计失败:', error);
      return NextResponse.json({ error: '获取刷单统计失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('获取刷单统计失败:', error);
    return NextResponse.json({ error: '获取刷单统计失败' }, { status: 500 });
  }
}

// 获取基础统计数据
async function getBrushStats(dbService: any, period: string) {
  const dateCondition = getDateCondition(period);
  
  // 订单统计
  const orderStatsQuery = `
    SELECT 
      COUNT(*) as total_orders,
      SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed_orders,
      SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as burst_orders,
      SUM(total_amount) as total_amount,
      SUM(commission_amount) as total_commission,
      AVG(commission_rate) as avg_commission_rate
    FROM brush_orders 
    ${dateCondition ? `WHERE ${dateCondition}` : ''}
  `;

  const { results: orderResults } = await dbService.db.prepare(orderStatsQuery).all();
  const orderStats = orderResults[0] as any;

  // 用户统计
  const userStatsQuery = `
    SELECT 
      COUNT(DISTINCT user_id) as active_users,
      AVG(today_orders) as avg_daily_orders
    FROM user_brush_stats
    ${period === 'today' ? 'WHERE stats_date = DATE("now")' : ''}
  `;

  const { results: userResults } = await dbService.db.prepare(userStatsQuery).all();
  const userStats = userResults[0] as any;

  // 余额统计
  const balanceStatsQuery = `
    SELECT 
      SUM(balance) as total_balance,
      SUM(total_recharged) as total_recharged,
      SUM(total_spent) as total_spent
    FROM user_balances
  `;

  const { results: balanceResults } = await dbService.db.prepare(balanceStatsQuery).all();
  const balanceStats = balanceResults[0] as any;

  return {
    orders: {
      total: orderStats.total_orders || 0,
      completed: orderStats.completed_orders || 0,
      burst: orderStats.burst_orders || 0,
      completionRate: orderStats.total_orders > 0 ? 
        ((orderStats.completed_orders || 0) / orderStats.total_orders * 100).toFixed(2) : '0.00',
      burstRate: orderStats.total_orders > 0 ? 
        ((orderStats.burst_orders || 0) / orderStats.total_orders * 100).toFixed(2) : '0.00'
    },
    revenue: {
      totalAmount: (orderStats.total_amount || 0) / 100, // 转换为元
      totalCommission: (orderStats.total_commission || 0) / 100,
      avgCommissionRate: ((orderStats.avg_commission_rate || 0) * 100).toFixed(2) + '%'
    },
    users: {
      activeUsers: userStats.active_users || 0,
      avgDailyOrders: parseFloat((userStats.avg_daily_orders || 0).toFixed(2))
    },
    balance: {
      totalBalance: (balanceStats.total_balance || 0) / 100,
      totalRecharged: (balanceStats.total_recharged || 0) / 100,
      totalSpent: (balanceStats.total_spent || 0) / 100
    }
  };
}

// 获取用户排行榜
async function getUserRanking(dbService: any, period: string) {
  const dateCondition = getDateCondition(period);
  
  const query = `
    SELECT 
      u.username,
      u.name,
      COUNT(bo.id) as order_count,
      SUM(bo.total_amount) as total_spent,
      SUM(bo.commission_amount) as total_commission,
      SUM(CASE WHEN bo.status = 4 THEN 1 ELSE 0 END) as burst_count
    FROM users u
    LEFT JOIN brush_orders bo ON u.id = bo.user_id
    ${dateCondition ? `WHERE ${dateCondition.replace('created_at', 'bo.created_at')}` : ''}
    GROUP BY u.id, u.username, u.name
    HAVING order_count > 0
    ORDER BY total_commission DESC
    LIMIT 20
  `;

  const { results } = await dbService.db.prepare(query).all();
  
  return results.map((row: any) => ({
    username: row.username,
    name: row.name || row.username,
    orderCount: row.order_count,
    totalSpent: (row.total_spent || 0) / 100,
    totalCommission: (row.total_commission || 0) / 100,
    burstCount: row.burst_count || 0,
    burstRate: row.order_count > 0 ? 
      ((row.burst_count || 0) / row.order_count * 100).toFixed(2) + '%' : '0.00%'
  }));
}

// 获取商品销售统计
async function getProductStats(dbService: any, period: string) {
  const dateCondition = getDateCondition(period);
  
  const query = `
    SELECT 
      p.name as product_name,
      COUNT(bo.id) as order_count,
      SUM(bo.total_amount) as total_sales,
      SUM(CASE WHEN bo.status = 2 THEN 1 ELSE 0 END) as completed_count,
      SUM(CASE WHEN bo.status = 4 THEN 1 ELSE 0 END) as burst_count
    FROM products p
    LEFT JOIN brush_orders bo ON p.id = bo.product_id
    ${dateCondition ? `WHERE ${dateCondition.replace('created_at', 'bo.created_at')}` : ''}
    GROUP BY p.id, p.name
    HAVING order_count > 0
    ORDER BY total_sales DESC
    LIMIT 10
  `;

  const { results } = await dbService.db.prepare(query).all();
  
  return results.map((row: any) => ({
    productName: row.product_name,
    orderCount: row.order_count,
    totalSales: (row.total_sales || 0) / 100,
    completedCount: row.completed_count || 0,
    burstCount: row.burst_count || 0,
    completionRate: row.order_count > 0 ? 
      ((row.completed_count || 0) / row.order_count * 100).toFixed(2) + '%' : '0.00%'
  }));
}

// 获取充值统计
async function getRechargeStats(dbService: any, period: string) {
  const dateCondition = getDateCondition(period);
  
  const query = `
    SELECT 
      COUNT(*) as total_recharges,
      SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as confirmed_recharges,
      SUM(CASE WHEN status = 1 THEN usdt_amount ELSE 0 END) as total_usdt,
      SUM(CASE WHEN status = 1 THEN internal_amount ELSE 0 END) as total_internal,
      AVG(CASE WHEN status = 1 THEN usdt_amount ELSE NULL END) as avg_recharge_amount
    FROM recharge_records
    ${dateCondition ? `WHERE ${dateCondition}` : ''}
  `;

  const { results } = await dbService.db.prepare(query).all();
  const stats = results[0] as any;

  return {
    totalRecharges: stats.total_recharges || 0,
    confirmedRecharges: stats.confirmed_recharges || 0,
    confirmationRate: stats.total_recharges > 0 ? 
      ((stats.confirmed_recharges || 0) / stats.total_recharges * 100).toFixed(2) + '%' : '0.00%',
    totalUSDT: parseFloat((stats.total_usdt || 0).toFixed(2)),
    totalInternal: (stats.total_internal || 0) / 100,
    avgRechargeAmount: parseFloat((stats.avg_recharge_amount || 0).toFixed(2))
  };
}

// 获取日期条件
function getDateCondition(period: string): string {
  switch (period) {
    case 'today':
      return 'DATE(created_at) = DATE("now")';
    case 'week':
      return 'created_at >= DATE("now", "-7 days")';
    case 'month':
      return 'created_at >= DATE("now", "-30 days")';
    case 'all':
    default:
      return '';
  }
}
