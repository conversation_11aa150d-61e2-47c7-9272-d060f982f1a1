import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 创建代理
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;
    
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const body = await request.json();
    const { username, email, password, name, permissions } = body as {
      username: string;
      email?: string;
      password: string;
      name?: string;
      permissions?: string[];
    };

    // 验证必填字段
    if (!username?.trim()) {
      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 });
    }

    if (!password?.trim()) {
      return NextResponse.json({ error: '密码不能为空' }, { status: 400 });
    }

    if (password.length < 6) {
      return NextResponse.json({ error: '密码长度至少6位' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT 
        u.id, u.username,
        a.id as admin_id, a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    // 只有超级管理员可以创建代理
    if (adminResult.role !== 'super_admin') {
      return NextResponse.json({ error: '只有超级管理员可以创建代理' }, { status: 403 });
    }

    // 检查用户名是否已存在
    const existingUser = await db.prepare(`
      SELECT id FROM users WHERE username = ?
    `).bind(username.trim()).first();

    if (existingUser) {
      return NextResponse.json({ error: '用户名已存在' }, { status: 400 });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email?.trim()) {
      const existingEmail = await db.prepare(`
        SELECT id FROM users WHERE email = ?
      `).bind(email.trim()).first();

      if (existingEmail) {
        return NextResponse.json({ error: '邮箱已被使用' }, { status: 400 });
      }
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 12);

    // 创建用户
    const userResult = await db.prepare(`
      INSERT INTO users (username, email, password_hash, name, is_active)
      VALUES (?, ?, ?, ?, TRUE)
    `).bind(
      username.trim(),
      email?.trim() || null,
      passwordHash,
      name?.trim() || null
    ).run();

    const userId = userResult.meta.last_row_id;

    // 设置代理默认权限
    const defaultAgentPermissions = [
      'manage_invited_users',
      'view_invited_wallets', 
      'view_invited_transactions',
      'create_invite_codes'
    ];

    const agentPermissions = permissions && permissions.length > 0 ? permissions : defaultAgentPermissions;

    // 创建管理员记录
    await db.prepare(`
      INSERT INTO admins (user_id, role, permissions, parent_admin_id, is_active)
      VALUES (?, 'agent', ?, ?, TRUE)
    `).bind(
      userId,
      JSON.stringify(agentPermissions),
      adminResult.admin_id
    ).run();

    return NextResponse.json({
      success: true,
      message: '代理创建成功',
      agent: {
        id: userId,
        username: username.trim(),
        email: email?.trim() || null,
        name: name?.trim() || null,
        role: 'agent',
        permissions: agentPermissions,
        parent_admin_id: adminResult.admin_id
      }
    });

  } catch (error: any) {
    console.error('创建代理失败:', error);
    return NextResponse.json({ 
      error: error.message || '创建代理失败' 
    }, { status: 500 });
  }
}
