import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createRechargeMonitor } from '@/lib/recharge-monitor';
import { getDbService } from '@/lib/db';

// 全局监控实例
let monitorInstances: Map<string, any> = new Map();

// 获取监控状态
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const status = {
      mainnet: {
        running: monitorInstances.has('mainnet'),
        startTime: monitorInstances.get('mainnet')?.startTime || null
      },
      nile: {
        running: monitorInstances.has('nile'),
        startTime: monitorInstances.get('nile')?.startTime || null
      }
    };

    return NextResponse.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('获取监控状态失败:', error);
    return NextResponse.json({ error: '获取监控状态失败' }, { status: 500 });
  }
}

// 启动/停止监控
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { action, network = 'mainnet' } = body;

    if (!['start', 'stop'].includes(action)) {
      return NextResponse.json({ error: '无效的操作类型' }, { status: 400 });
    }

    if (!['mainnet', 'nile'].includes(network)) {
      return NextResponse.json({ error: '无效的网络类型' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });

    if (action === 'start') {
      // 启动监控
      if (monitorInstances.has(network)) {
        return NextResponse.json({ error: `${network} 网络监控已在运行中` }, { status: 400 });
      }

      try {
        const monitor = createRechargeMonitor(env, network as 'mainnet' | 'nile');
        await monitor.startMonitoring();
        
        // 保存监控实例
        monitorInstances.set(network, {
          monitor,
          startTime: new Date().toISOString()
        });

        return NextResponse.json({
          success: true,
          message: `${network} 网络充值监控已启动`
        });
      } catch (error) {
        console.error(`启动 ${network} 监控失败:`, error);
        return NextResponse.json({ error: `启动 ${network} 监控失败` }, { status: 500 });
      }
    } else {
      // 停止监控
      const instance = monitorInstances.get(network);
      if (!instance) {
        return NextResponse.json({ error: `${network} 网络监控未运行` }, { status: 400 });
      }

      try {
        instance.monitor.stopMonitoring();
        monitorInstances.delete(network);

        return NextResponse.json({
          success: true,
          message: `${network} 网络充值监控已停止`
        });
      } catch (error) {
        console.error(`停止 ${network} 监控失败:`, error);
        return NextResponse.json({ error: `停止 ${network} 监控失败` }, { status: 500 });
      }
    }
  } catch (error) {
    console.error('管理充值监控失败:', error);
    return NextResponse.json({ error: '管理充值监控失败' }, { status: 500 });
  }
}

// 手动处理充值（管理员工具）
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { txHash, userId, amount, fromAddress, toAddress, network = 'mainnet' } = body;

    if (!txHash || !userId || !amount || !fromAddress || !toAddress) {
      return NextResponse.json({ error: '参数不完整' }, { status: 400 });
    }

    if (amount <= 0) {
      return NextResponse.json({ error: '充值金额必须大于0' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 检查交易哈希是否已存在
      const existingRecord = await dbService.getRechargeRecordByTxHash(txHash);
      if (existingRecord) {
        return NextResponse.json({ error: '该交易哈希已存在' }, { status: 400 });
      }

      // 手动处理充值
      const success = await dbService.processUserRecharge(
        userId,
        amount,
        txHash,
        fromAddress,
        toAddress,
        network
      );

      if (success) {
        return NextResponse.json({
          success: true,
          message: '手动充值处理成功'
        });
      } else {
        return NextResponse.json({ error: '手动充值处理失败' }, { status: 500 });
      }
    } catch (error) {
      console.error('手动处理充值失败:', error);
      return NextResponse.json({ error: '手动处理充值失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('手动处理充值失败:', error);
    return NextResponse.json({ error: '手动处理充值失败' }, { status: 500 });
  }
}


