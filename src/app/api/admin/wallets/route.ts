import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取钱包列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ success: false, error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;

    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ success: false, error: 'Token无效' }, { status: 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ success: false, error: '无管理员权限' }, { status: 403 });
    }

    let walletsQuery = '';
    let queryParams: any[] = [];

    if (adminResult.role === 'super_admin') {
      // 超级管理员可以查看所有钱包
      walletsQuery = `
        SELECT
          w.id, w.user_id, w.name, w.address, w.wallet_type, w.network,
          w.is_default, w.is_active, w.created_at,
          u.username, u.email, u.name as user_name
        FROM wallets w
        JOIN users u ON w.user_id = u.id
        ORDER BY w.created_at DESC
      `;
    } else {
      // 代理只能查看自己邀请的用户的钱包
      walletsQuery = `
        SELECT
          w.id, w.user_id, w.name, w.address, w.wallet_type, w.network,
          w.is_default, w.is_active, w.created_at,
          u.username, u.email, u.name as user_name
        FROM wallets w
        JOIN users u ON w.user_id = u.id
        WHERE u.invited_by = ? OR u.invited_by IN (
          SELECT u2.id FROM users u2
          JOIN admins a2 ON u2.id = a2.user_id
          WHERE a2.parent_admin_id = ?
        )
        ORDER BY w.created_at DESC
      `;
      queryParams = [decoded.userId, adminResult.id];
    }

    const wallets = await db.prepare(walletsQuery).bind(...queryParams).all();

    // 处理钱包数据
    const processedWallets = wallets.results?.map((wallet: any) => ({
      id: wallet.id,
      user_id: wallet.user_id,
      name: wallet.name,
      address: wallet.address,
      wallet_type: wallet.wallet_type,
      network: wallet.network,
      is_default: wallet.is_default,
      is_active: wallet.is_active,
      created_at: wallet.created_at,
      username: wallet.username,
      email: wallet.email,
      user_name: wallet.user_name
    })) || [];

    return NextResponse.json({
      success: true,
      data: processedWallets
    });

  } catch (error: any) {
    console.error('获取钱包列表失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message || '获取钱包列表失败'
    }, { status: 500 });
  }
}

// 禁用/启用钱包
export async function PATCH(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;

    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    const body = await request.json();
    const { walletId, isActive } = body;

    if (!walletId) {
      return NextResponse.json({ error: '钱包ID不能为空' }, { status: 400 });
    }

    // 检查钱包是否存在以及权限
    let walletCheckQuery = '';
    let checkParams: any[] = [walletId];

    if (adminResult.role === 'super_admin') {
      walletCheckQuery = `
        SELECT w.id, w.user_id, u.username
        FROM wallets w
        JOIN users u ON w.user_id = u.id
        WHERE w.id = ?
      `;
    } else {
      walletCheckQuery = `
        SELECT w.id, w.user_id, u.username
        FROM wallets w
        JOIN users u ON w.user_id = u.id
        WHERE w.id = ? AND (u.invited_by = ? OR u.invited_by IN (
          SELECT u2.id FROM users u2
          JOIN admins a2 ON u2.id = a2.user_id
          WHERE a2.parent_admin_id = ?
        ))
      `;
      checkParams = [walletId, decoded.userId, adminResult.id];
    }

    const walletCheck = await db.prepare(walletCheckQuery).bind(...checkParams).first();

    if (!walletCheck) {
      return NextResponse.json({ error: '钱包不存在或无权限操作' }, { status: 404 });
    }

    // 更新钱包状态
    await db.prepare(`
      UPDATE wallets SET is_active = ?, updated_at = ?
      WHERE id = ?
    `).bind(isActive, new Date().toISOString(), walletId).run();

    return NextResponse.json({
      success: true,
      message: `钱包已${isActive ? '启用' : '禁用'}`
    });

  } catch (error: any) {
    console.error('更新钱包状态失败:', error);
    return NextResponse.json({
      error: error.message || '更新钱包状态失败'
    }, { status: 500 });
  }
}
