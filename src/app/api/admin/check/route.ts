import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 检查管理员权限
export async function GET(request: NextRequest) {
  try {
    // 从cookie中获取token
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json({ success: false, isAdmin: false, error: '未登录' }, { status: 401 });
    }

    // 验证JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;

    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ success: false, isAdmin: false, error: 'Token无效' }, { status: 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 查找用户和管理员信息
    const userResult = await db.prepare(`
      SELECT
        u.id, u.username, u.email, u.name, u.is_active,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      LEFT JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!userResult) {
      return NextResponse.json({ success: false, isAdmin: false, error: '用户不存在' }, { status: 404 });
    }

    // 检查是否是管理员
    if (!userResult.role || !userResult.admin_active) {
      return NextResponse.json({ success: false, isAdmin: false, error: '无管理员权限' }, { status: 403 });
    }

    // 解析权限
    let permissions: string[] = [];
    if (userResult.permissions) {
      try {
        permissions = JSON.parse(userResult.permissions);
      } catch (error) {
        console.error('解析权限失败:', error);
      }
    }

    return NextResponse.json({
      success: true,
      isAdmin: true,
      user: {
        id: userResult.id,
        username: userResult.username,
        email: userResult.email,
        name: userResult.name,
        role: userResult.role,
        permissions: permissions,
        parent_admin_id: userResult.parent_admin_id,
        is_active: userResult.is_active
      }
    });

  } catch (error: any) {
    console.error('检查管理员权限失败:', error);
    return NextResponse.json({
      success: false,
      isAdmin: false,
      error: error.message || '检查权限失败'
    }, { status: 500 });
  }
}