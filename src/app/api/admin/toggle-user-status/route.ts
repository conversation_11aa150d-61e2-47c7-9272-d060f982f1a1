import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 切换用户状态（启用/禁用）
export async function POST(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;
    
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const body = await request.json();
    const { userId, isActive } = body as {
      userId: number;
      isActive: boolean;
    };

    // 验证参数
    if (!userId) {
      return NextResponse.json({ error: '用户ID不能为空' }, { status: 400 });
    }

    if (typeof isActive !== 'boolean') {
      return NextResponse.json({ error: '状态参数无效' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT 
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    // 获取目标用户信息
    const targetUser = await db.prepare(`
      SELECT 
        u.id, u.username, u.invited_by, u.is_active,
        a.role, a.parent_admin_id
      FROM users u
      LEFT JOIN admins a ON u.id = a.user_id
      WHERE u.id = ?
    `).bind(userId).first();

    if (!targetUser) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 });
    }

    // 不能操作自己
    if (targetUser.id === decoded.userId) {
      return NextResponse.json({ error: '不能操作自己的账户' }, { status: 400 });
    }

    // 权限检查
    if (adminResult.role === 'super_admin') {
      // 超级管理员可以操作所有用户，但不能禁用其他超级管理员
      if (targetUser.role === 'super_admin' && !isActive) {
        return NextResponse.json({ error: '不能禁用其他超级管理员' }, { status: 403 });
      }
    } else {
      // 代理只能操作自己邀请的用户
      const canManage = targetUser.invited_by === decoded.userId || 
                       (targetUser.invited_by && await checkIfUserInvitedByAgent(db, targetUser.invited_by, decoded.userId));
      
      if (!canManage) {
        return NextResponse.json({ error: '只能操作自己邀请的用户' }, { status: 403 });
      }

      // 代理不能操作管理员
      if (targetUser.role) {
        return NextResponse.json({ error: '代理不能操作管理员账户' }, { status: 403 });
      }
    }

    // 更新用户状态
    await db.prepare(`
      UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?
    `).bind(isActive, new Date().toISOString(), userId).run();

    return NextResponse.json({
      success: true,
      message: `用户已${isActive ? '启用' : '禁用'}`,
      userId: userId,
      isActive: isActive
    });

  } catch (error: any) {
    console.error('切换用户状态失败:', error);
    return NextResponse.json({ 
      error: error.message || '切换用户状态失败' 
    }, { status: 500 });
  }
}

// 检查用户是否由指定代理邀请（包括下级代理邀请的用户）
async function checkIfUserInvitedByAgent(db: any, invitedBy: number, agentId: number): Promise<boolean> {
  try {
    const inviter = await db.prepare(`
      SELECT 
        u.id, u.invited_by,
        a.parent_admin_id
      FROM users u
      LEFT JOIN admins a ON u.id = a.user_id
      WHERE u.id = ?
    `).bind(invitedBy).first();

    if (!inviter) {
      return false;
    }

    // 如果直接由代理邀请
    if (inviter.id === agentId) {
      return true;
    }

    // 如果邀请人是代理的下级
    if (inviter.parent_admin_id) {
      const parentAdmin = await db.prepare(`
        SELECT user_id FROM admins WHERE id = ?
      `).bind(inviter.parent_admin_id).first();
      
      return parentAdmin?.user_id === agentId;
    }

    return false;
  } catch (error) {
    console.error('检查邀请关系失败:', error);
    return false;
  }
}
