import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取刷单订单列表（管理员）
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const status = searchParams.get('status');
    const username = searchParams.get('username');
    const productName = searchParams.get('productName');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      const result = await getBrushOrdersForAdmin(dbService, {
        page,
        pageSize,
        status,
        username,
        productName,
        startDate,
        endDate
      });

      return NextResponse.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('获取刷单订单失败:', error);
      return NextResponse.json({ error: '获取刷单订单失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('获取刷单订单失败:', error);
    return NextResponse.json({ error: '获取刷单订单失败' }, { status: 500 });
  }
}

// 更新订单状态（管理员操作）
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { orderId, status, reason } = body;

    if (!orderId || status === undefined) {
      return NextResponse.json({ error: '订单ID和状态不能为空' }, { status: 400 });
    }

    if (![0, 1, 2, 3, 4].includes(status)) {
      return NextResponse.json({ error: '无效的订单状态' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 获取订单信息
      const order = await dbService.getBrushOrderById(orderId);
      if (!order) {
        return NextResponse.json({ error: '订单不存在' }, { status: 404 });
      }

      // 更新订单状态
      const isBurst = status === 4;
      const success = await dbService.updateBrushOrderStatus(
        orderId, 
        status, 
        isBurst, 
        reason || (isBurst ? '管理员手动设置为爆单' : undefined)
      );

      if (!success) {
        return NextResponse.json({ error: '更新订单状态失败' }, { status: 500 });
      }

      // 如果是手动完成订单，需要处理佣金返还
      if (status === 2 && order.status !== 2) {
        await handleOrderCompletion(dbService, order);
      }

      // 如果是手动爆单，需要更新用户统计
      if (status === 4 && order.status !== 4) {
        await handleOrderBurst(dbService, order);
      }

      return NextResponse.json({
        success: true,
        message: '订单状态更新成功'
      });
    } catch (error) {
      console.error('更新订单状态失败:', error);
      return NextResponse.json({ error: '更新订单状态失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('更新订单状态失败:', error);
    return NextResponse.json({ error: '更新订单状态失败' }, { status: 500 });
  }
}

// 获取刷单订单列表（带分页和筛选）
async function getBrushOrdersForAdmin(dbService: any, options: {
  page: number;
  pageSize: number;
  status?: string;
  username?: string;
  productName?: string;
  startDate?: string;
  endDate?: string;
}) {
  const { page, pageSize, status, username, productName, startDate, endDate } = options;
  const offset = (page - 1) * pageSize;

  // 构建WHERE条件
  const whereConditions: string[] = [];
  const params: any[] = [];

  if (status !== null && status !== undefined && status !== '') {
    whereConditions.push('bo.status = ?');
    params.push(parseInt(status));
  }

  if (username) {
    whereConditions.push('u.username LIKE ?');
    params.push(`%${username}%`);
  }

  if (productName) {
    whereConditions.push('bo.product_name LIKE ?');
    params.push(`%${productName}%`);
  }

  if (startDate) {
    whereConditions.push('DATE(bo.created_at) >= ?');
    params.push(startDate);
  }

  if (endDate) {
    whereConditions.push('DATE(bo.created_at) <= ?');
    params.push(endDate);
  }

  const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

  // 获取总数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM brush_orders bo
    LEFT JOIN users u ON bo.user_id = u.id
    ${whereClause}
  `;

  const { results: countResults } = await dbService.db.prepare(countQuery).bind(...params).all();
  const total = (countResults[0] as any).total;

  // 获取分页数据
  const dataQuery = `
    SELECT
      bo.*,
      u.username,
      u.name as user_name,
      COALESCE(bo.product_image, p.image) as product_image
    FROM brush_orders bo
    LEFT JOIN users u ON bo.user_id = u.id
    LEFT JOIN products p ON bo.product_id = p.id
    ${whereClause}
    ORDER BY bo.created_at DESC
    LIMIT ? OFFSET ?
  `;

  const { results } = await dbService.db.prepare(dataQuery).bind(...params, pageSize, offset).all();

  // 格式化数据
  const formattedOrders = results.map((order: any) => ({
    ...order,
    product_price: order.product_price / 100,
    total_amount: order.total_amount / 100,
    commission_amount: order.commission_amount / 100,
    status_name: getOrderStatusName(order.status)
  }));

  return {
    orders: formattedOrders,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}

// 处理订单完成
async function handleOrderCompletion(dbService: any, order: any) {
  try {
    // 获取用户余额
    const userBalance = await dbService.getUserBalance(order.user_id);
    if (!userBalance) return;

    // 返还本金和佣金
    const returnAmount = order.total_amount + order.commission_amount;
    const newBalance = userBalance.balance + returnAmount;

    await dbService.updateUserBalance(order.user_id, {
      balance: newBalance
    });

    // 创建佣金收入记录
    await dbService.createBalanceLog({
      userId: order.user_id,
      type: 3, // 佣金收入
      amount: returnAmount,
      balanceBefore: userBalance.balance,
      balanceAfter: newBalance,
      relatedId: order.id,
      relatedType: 'order',
      description: `管理员手动完成订单: 本金 ${(order.total_amount / 100).toFixed(2)} + 佣金 ${(order.commission_amount / 100).toFixed(2)}`
    });

    // 更新用户统计
    const userStats = await dbService.getUserBrushStats(order.user_id);
    if (userStats) {
      await dbService.updateUserBrushStats(order.user_id, {
        completedOrders: userStats.completed_orders + 1,
        totalCommission: userStats.total_commission + order.commission_amount
      });
    }
  } catch (error) {
    console.error('处理订单完成失败:', error);
  }
}

// 处理订单爆单
async function handleOrderBurst(dbService: any, order: any) {
  try {
    // 更新用户统计
    const userStats = await dbService.getUserBrushStats(order.user_id);
    if (userStats) {
      await dbService.updateUserBrushStats(order.user_id, {
        burstOrders: userStats.burst_orders + 1
      });
    }
  } catch (error) {
    console.error('处理订单爆单失败:', error);
  }
}

// 获取订单状态名称
function getOrderStatusName(status: number): string {
  const statusNames: { [key: number]: string } = {
    0: '待付款',
    1: '已付款',
    2: '已完成',
    3: '已取消',
    4: '爆单'
  };
  return statusNames[status] || '未知';
}
