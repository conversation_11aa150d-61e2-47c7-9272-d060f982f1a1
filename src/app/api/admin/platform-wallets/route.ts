import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取平台钱包列表
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    const wallets = await dbService.getAllPlatformWallets();

    return NextResponse.json({
      success: true,
      data: wallets
    });
  } catch (error) {
    console.error('获取平台钱包列表失败:', error);
    return NextResponse.json({ error: '获取平台钱包列表失败' }, { status: 500 });
  }
}

// 创建平台钱包
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { name, address, network, currency, isActive = true, isDefault = false, description } = body;

    if (!name || !address || !network || !currency) {
      return NextResponse.json({ error: '名称、地址、网络和币种不能为空' }, { status: 400 });
    }

    if (!['mainnet', 'nile', 'shasta'].includes(network)) {
      return NextResponse.json({ error: '无效的网络类型' }, { status: 400 });
    }

    if (!['TRX', 'USDT'].includes(currency)) {
      return NextResponse.json({ error: '无效的币种类型' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 检查地址是否已存在
      const existingWallets = await dbService.getAllPlatformWallets();
      const addressExists = existingWallets.some(wallet => 
        wallet.address === address && wallet.network === network && wallet.currency === currency
      );

      if (addressExists) {
        return NextResponse.json({ error: '该地址在此网络和币种下已存在' }, { status: 400 });
      }

      // 如果设置为默认钱包，需要先取消其他默认钱包
      if (isDefault) {
        const existingDefault = await dbService.getDefaultPlatformWallet(network, currency);
        if (existingDefault) {
          await dbService.updatePlatformWallet(existingDefault.id, { isDefault: false });
        }
      }

      const wallet = await dbService.createPlatformWallet({
        name: name.trim(),
        address: address.trim(),
        network,
        currency,
        isActive,
        isDefault,
        description: description?.trim()
      });

      return NextResponse.json({
        success: true,
        data: wallet
      });
    } catch (error) {
      console.error('创建平台钱包失败:', error);
      return NextResponse.json({ error: '创建平台钱包失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('创建平台钱包失败:', error);
    return NextResponse.json({ error: '创建平台钱包失败' }, { status: 500 });
  }
}

// 更新平台钱包
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { id, name, address, isActive, isDefault, description } = body;

    if (!id) {
      return NextResponse.json({ error: '钱包ID不能为空' }, { status: 400 });
    }

    // 确保ID是数字类型
    const walletId = typeof id === 'string' ? parseInt(id) : id;
    if (isNaN(walletId)) {
      return NextResponse.json({ error: '无效的钱包ID' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 构建更新数据
    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (address !== undefined) updateData.address = address.trim();
    if (isActive !== undefined) updateData.isActive = isActive;
    if (isDefault !== undefined) updateData.isDefault = isDefault;
    if (description !== undefined) updateData.description = description?.trim();

    // 执行更新
    const success = await dbService.updatePlatformWallet(walletId, updateData);

    return NextResponse.json({
      success: true,
      message: '平台钱包更新成功',
      updateResult: success
    });

  } catch (error: any) {
    console.error('更新平台钱包失败:', error);
    return NextResponse.json({
      error: '更新平台钱包失败',
      details: error.message
    }, { status: 500 });
  }
}

// 删除平台钱包
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: '钱包ID不能为空' }, { status: 400 });
    }

    // 确保ID是数字类型
    const walletId = parseInt(id);
    if (isNaN(walletId)) {
      return NextResponse.json({ error: '无效的钱包ID' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 检查钱包是否存在
      const existingWallet = await dbService.getAllPlatformWallets().then(wallets =>
        wallets.find(w => w.id === walletId)
      );
      
      if (!existingWallet) {
        return NextResponse.json({ error: '平台钱包不存在' }, { status: 404 });
      }

      // 检查是否有相关的充值记录
      // 暂时跳过充值记录检查，因为需要添加相应的数据库方法
      // TODO: 添加检查充值记录的方法

      const success = await dbService.deletePlatformWallet(walletId);

      if (!success) {
        return NextResponse.json({ error: '删除平台钱包失败' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        message: '平台钱包删除成功'
      });
    } catch (error) {
      console.error('删除平台钱包失败:', error);
      return NextResponse.json({ error: '删除平台钱包失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('删除平台钱包失败:', error);
    return NextResponse.json({ error: '删除平台钱包失败' }, { status: 500 });
  }
}
