import { NextRequest, NextResponse } from 'next/server';
import { createTronWebInstance } from '@/lib/tronweb-server';

// 测试不同网络的配置
export async function GET(request: NextRequest) {
  const results: any = {};
  
  const networks: ('mainnet' | 'nile' | 'shasta')[] = ['mainnet', 'nile', 'shasta'];
  
  for (const network of networks) {
    try {
      console.log(`测试 ${network} 网络配置...`);
      
      // 创建TronWeb实例
      const tronWeb = createTronWebInstance(network);
      
      // 测试基本连接
      const latestBlock = await tronWeb.trx.getCurrentBlock();
      const blockNumber = latestBlock.block_header.raw_data.number;
      
      // 测试账户查询（使用一个已知地址）
      let testAddress = '';
      switch (network) {
        case 'mainnet':
          testAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // 主网USDT合约
          break;
        case 'nile':
          testAddress = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf'; // Nile测试网USDT合约
          break;
        case 'shasta':
          testAddress = 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs'; // Shasta测试网USDT合约
          break;
      }
      
      const accountInfo = await tronWeb.trx.getAccount(testAddress);
      const balance = await tronWeb.trx.getBalance(testAddress);
      const trxAmount = tronWeb.fromSun(balance);
      
      results[network] = {
        success: true,
        network: network,
        config: {
          fullHost: tronWeb.fullNode.host,
          hasApiKey: !!tronWeb.fullNode.headers['TRON-PRO-API-KEY'],
          apiKeyPrefix: tronWeb.fullNode.headers['TRON-PRO-API-KEY'] ? 
            tronWeb.fullNode.headers['TRON-PRO-API-KEY'].substring(0, 8) + '...' : 
            '无'
        },
        testResults: {
          latestBlock: blockNumber,
          testAddress: testAddress,
          accountExists: !!accountInfo.address,
          trxBalance: trxAmount
        },
        timestamp: new Date().toISOString()
      };
      
      console.log(`${network} 网络测试成功`);
      
    } catch (error: any) {
      console.error(`${network} 网络测试失败:`, error);
      
      results[network] = {
        success: false,
        network: network,
        error: error.message,
        details: {
          name: error.name,
          code: error.code
        },
        timestamp: new Date().toISOString()
      };
    }
  }
  
  // 统计结果
  const summary = {
    totalNetworks: networks.length,
    successfulNetworks: Object.values(results).filter((r: any) => r.success).length,
    failedNetworks: Object.values(results).filter((r: any) => !r.success).length
  };
  
  return NextResponse.json({
    success: summary.failedNetworks === 0,
    message: `网络配置测试完成 - 成功: ${summary.successfulNetworks}, 失败: ${summary.failedNetworks}`,
    summary: summary,
    results: results,
    recommendations: {
      mainnet: "主网需要API密钥以提高请求限制",
      nile: "Nile测试网通常不需要API密钥",
      shasta: "Shasta测试网通常不需要API密钥",
      note: "如果测试网出现限制，可以申请专用的测试网API密钥"
    }
  });
}
