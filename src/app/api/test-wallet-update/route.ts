import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 简单的钱包更新测试
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    console.log('收到的请求体:', body);

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 获取更新前的数据
    const beforeUpdate = await dbService.getAllPlatformWallets();
    const walletBefore = beforeUpdate.find(w => w.id === 2);
    console.log('更新前的钱包:', walletBefore);

    // 简单更新测试
    const newName = '测试更新-' + Date.now();
    const newDesc = '测试描述-' + Date.now();

    const success = await dbService.updatePlatformWallet(2, {
      name: newName,
      description: newDesc
    });

    console.log('更新结果:', success);

    // 获取更新后的数据
    const afterUpdate = await dbService.getAllPlatformWallets();
    const walletAfter = afterUpdate.find(w => w.id === 2);
    console.log('更新后的钱包:', walletAfter);

    return NextResponse.json({
      success: true,
      message: '测试更新成功',
      updateResult: success,
      before: walletBefore,
      after: walletAfter,
      expectedName: newName,
      expectedDesc: newDesc
    });

  } catch (error: any) {
    console.error('测试更新失败:', error);
    return NextResponse.json({ 
      error: '测试更新失败',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
