import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { authenticator } from 'otplib';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 用户登录
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, mfaCode, rememberMe } = body as {
      username: string;
      password: string;
      mfaCode?: string;
      rememberMe?: boolean;
    };

    // 验证必填字段
    if (!username?.trim()) {
      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 });
    }

    if (!password?.trim()) {
      return NextResponse.json({ error: '密码不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 查找用户
    const user = await db.prepare(`
      SELECT id, username, email, password_hash, name, mfa_secret, mfa_enabled, is_active
      FROM users 
      WHERE username = ? AND is_active = TRUE
    `).bind(username.trim()).first();

    if (!user) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }

    // 验证密码
    const passwordValid = await bcrypt.compare(password, user.password_hash);
    if (!passwordValid) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }

    // 检查是否需要MFA验证
    if (user.mfa_enabled && user.mfa_secret) {
      if (!mfaCode) {
        return NextResponse.json({ 
          requireMFA: true,
          message: '需要MFA验证码'
        });
      }

      // 验证MFA代码
      const isValidMFA = authenticator.verify({
        token: mfaCode,
        secret: user.mfa_secret
      });

      if (!isValidMFA) {
        return NextResponse.json({ error: 'MFA验证码错误' }, { status: 401 });
      }
    }

    // 查询用户角色
    const adminRole = await db.prepare(`
      SELECT role, permissions FROM admins WHERE user_id = ? AND is_active = TRUE
    `).bind(user.id).first();

    const userRole = adminRole ? adminRole.role : 'user';

    // 更新最后登录时间
    await db.prepare(`
      UPDATE users SET last_login_at = ? WHERE id = ?
    `).bind(new Date().toISOString(), user.id).run();

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      role: userRole
    };

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const expiresIn = rememberMe ? '30d' : '7d';
    
    const token = jwt.sign(tokenPayload, jwtSecret, { expiresIn });

    // 设置cookie
    const response = NextResponse.json({
      success: true,
      message: '登录成功',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: userRole
      }
    });

    // 设置HttpOnly cookie
    const maxAge = rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60; // 30天或7天（秒）
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: maxAge,
      path: '/'
    });

    return response;

  } catch (error: any) {
    console.error('登录失败:', error);
    return NextResponse.json({ 
      error: error.message || '登录失败' 
    }, { status: 500 });
  }
}
