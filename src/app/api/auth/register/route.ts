import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 用户注册
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, email, password, inviteCode, name } = body as {
      username: string;
      email?: string;
      password: string;
      inviteCode: string;
      name?: string;
    };

    // 验证必填字段
    if (!username?.trim()) {
      return NextResponse.json({ error: '用户名不能为空' }, { status: 400 });
    }

    if (!password?.trim()) {
      return NextResponse.json({ error: '密码不能为空' }, { status: 400 });
    }

    if (password.length < 6) {
      return NextResponse.json({ error: '密码长度至少6位' }, { status: 400 });
    }

    if (!inviteCode?.trim()) {
      return NextResponse.json({ error: '邀请码不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 验证邀请码
    const inviteCodeRecord = await db.prepare(`
      SELECT id, created_by, max_uses, used_count, expires_at, is_active
      FROM invite_codes 
      WHERE code = ? AND is_active = TRUE
    `).bind(inviteCode.trim()).first();

    if (!inviteCodeRecord) {
      return NextResponse.json({ error: '邀请码无效或已失效' }, { status: 400 });
    }

    // 检查邀请码是否过期
    if (inviteCodeRecord.expires_at && new Date(inviteCodeRecord.expires_at) < new Date()) {
      return NextResponse.json({ error: '邀请码已过期' }, { status: 400 });
    }

    // 检查邀请码使用次数
    if (inviteCodeRecord.max_uses !== -1 && inviteCodeRecord.used_count >= inviteCodeRecord.max_uses) {
      return NextResponse.json({ error: '邀请码使用次数已达上限' }, { status: 400 });
    }

    // 检查用户名是否已存在
    const existingUser = await db.prepare(`
      SELECT id FROM users WHERE username = ?
    `).bind(username.trim()).first();

    if (existingUser) {
      return NextResponse.json({ error: '用户名已存在' }, { status: 400 });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email?.trim()) {
      const existingEmail = await db.prepare(`
        SELECT id FROM users WHERE email = ?
      `).bind(email.trim()).first();

      if (existingEmail) {
        return NextResponse.json({ error: '邮箱已被使用' }, { status: 400 });
      }
    }

    // 加密密码
    const passwordHash = await bcrypt.hash(password, 12);

    // 创建用户
    const userResult = await db.prepare(`
      INSERT INTO users (username, email, password_hash, name, invite_code_id, invited_by, is_active)
      VALUES (?, ?, ?, ?, ?, ?, TRUE)
    `).bind(
      username.trim(),
      email?.trim() || null,
      passwordHash,
      name?.trim() || null,
      inviteCodeRecord.id,
      inviteCodeRecord.created_by
    ).run();

    const userId = userResult.meta.last_row_id;

    // 更新邀请码使用情况
    await db.prepare(`
      UPDATE invite_codes 
      SET used_count = used_count + 1, used_by = ?, used_at = ?
      WHERE id = ?
    `).bind(
      userId,
      new Date().toISOString(),
      inviteCodeRecord.id
    ).run();

    // 如果邀请码使用次数达到上限，设为不活跃
    if (inviteCodeRecord.max_uses !== -1 && inviteCodeRecord.used_count + 1 >= inviteCodeRecord.max_uses) {
      await db.prepare(`
        UPDATE invite_codes SET is_active = FALSE WHERE id = ?
      `).bind(inviteCodeRecord.id).run();
    }

    // 检查是否需要创建管理员记录
    // 只有使用 SUPER_ADMIN_INIT 邀请码的用户才成为超级管理员
    let userRole = 'user'; // 默认为普通用户

    if (inviteCodeRecord.code === 'SUPER_ADMIN_INIT') {
      // 创建超级管理员记录
      await db.prepare(`
        INSERT INTO admins (user_id, role, permissions, is_active)
        VALUES (?, 'super_admin', 'all', TRUE)
      `).bind(userId).run();

      userRole = 'super_admin';
      console.log(`用户 ${username} 使用超级管理员邀请码注册，已创建超级管理员权限`);
    } else {
      console.log(`用户 ${username} 使用普通邀请码注册，创建为普通用户`);
    }

    return NextResponse.json({
      success: true,
      message: '注册成功',
      user: {
        id: userId,
        username: username.trim(),
        email: email?.trim() || null,
        role: userRole,
        name: name?.trim() || null
      }
    });

  } catch (error: any) {
    console.error('注册失败:', error);
    return NextResponse.json({ 
      error: error.message || '注册失败' 
    }, { status: 500 });
  }
}
