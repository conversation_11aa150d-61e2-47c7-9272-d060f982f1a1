import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';

// 获取当前用户信息
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ 
        error: authResult.error || '未登录' 
      }, { status: authResult.status || 401 });
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authResult.user.id,
        username: authResult.user.username,
        email: authResult.user.email,
        name: authResult.user.name,
        role: authResult.user.role
      }
    });

  } catch (error: any) {
    console.error('获取用户信息失败:', error);
    return NextResponse.json({ 
      error: error.message || '获取用户信息失败' 
    }, { status: 500 });
  }
}
