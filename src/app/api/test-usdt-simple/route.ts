import { NextRequest, NextResponse } from 'next/server';
import { getAccountBalance } from '@/lib/tronweb-server';

// 简单的USDT余额测试
export async function GET(request: NextRequest) {
  try {
    const address = 'TAjz662ivGjK792yaiTaeJcthDD4wTtX4m';
    const network = 'nile';
    
    console.log(`测试USDT余额 - 地址: ${address}, 网络: ${network}`);
    
    // 测试TRX余额
    const trxBalance = await getAccountBalance(address, 'TRX', network);
    console.log(`TRX余额: ${trxBalance}`);
    
    // 测试USDT余额
    const usdtBalance = await getAccountBalance(address, 'USDT', network);
    console.log(`USDT余额: ${usdtBalance}`);
    
    return NextResponse.json({
      success: true,
      address: address,
      network: network,
      balances: {
        TRX: trxBalance,
        USDT: usdtBalance
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    console.error('USDT余额测试失败:', error);
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack?.split('\n').slice(0, 5)
    }, { status: 500 });
  }
}
