import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 调试API - 检查刷单配置状态
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查系统配置
    const configs = {
      brush_enabled: await dbService.getSystemConfig('brush_enabled'),
      brush_exchange_rate: await dbService.getSystemConfig('brush_exchange_rate'),
      brush_min_recharge: await dbService.getSystemConfig('brush_min_recharge'),
      brush_platform_wallet_mainnet: await dbService.getSystemConfig('brush_platform_wallet_mainnet'),
      brush_platform_wallet_nile: await dbService.getSystemConfig('brush_platform_wallet_nile'),
    };

    // 检查平台钱包
    const platformWallets = await dbService.getAllPlatformWallets();
    const mainnetWallet = await dbService.getDefaultPlatformWallet('mainnet', 'USDT');
    const nileWallet = await dbService.getDefaultPlatformWallet('nile', 'USDT');

    // 检查商品
    const products = await dbService.getAllProducts();

    // 检查刷单规则
    const brushRules = await dbService.getAllBrushRules();

    return NextResponse.json({
      success: true,
      data: {
        configs,
        platformWallets,
        mainnetWallet,
        nileWallet,
        products: products.length,
        brushRules: brushRules.length,
        debug: {
          hasConfigs: Object.values(configs).filter(c => c !== null).length,
          hasPlatformWallets: platformWallets.length,
          hasMainnetWallet: !!mainnetWallet,
          hasNileWallet: !!nileWallet
        }
      }
    });
  } catch (error: any) {
    console.error('调试API错误:', error);
    return NextResponse.json({ 
      error: '调试失败', 
      details: error.message,
      stack: error.stack 
    }, { status: 500 });
  }
}
