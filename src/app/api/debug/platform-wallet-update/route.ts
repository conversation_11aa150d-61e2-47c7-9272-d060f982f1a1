import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 调试平台钱包更新
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { id, name, address, isActive, isDefault, description } = body;

    console.log('收到的更新数据:', body);

    if (!id) {
      return NextResponse.json({ error: '钱包ID不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      // 获取所有钱包
      const allWallets = await dbService.getAllPlatformWallets();
      console.log('所有钱包:', allWallets);

      // 检查钱包是否存在
      const existingWallet = allWallets.find(w => w.id === id);
      console.log('要更新的钱包:', existingWallet);
      
      if (!existingWallet) {
        return NextResponse.json({ 
          error: '平台钱包不存在',
          debug: { id, allWallets: allWallets.map(w => ({ id: w.id, name: w.name })) }
        }, { status: 404 });
      }

      // 如果设置为默认钱包，需要先取消其他默认钱包
      if (isDefault && !existingWallet.is_default) {
        console.log('处理默认钱包设置...');
        const currentDefault = await dbService.getDefaultPlatformWallet(
          existingWallet.network, 
          existingWallet.currency
        );
        console.log('当前默认钱包:', currentDefault);
        
        if (currentDefault && currentDefault.id !== id) {
          console.log('取消其他默认钱包...');
          const cancelResult = await dbService.updatePlatformWallet(currentDefault.id, { isDefault: false });
          console.log('取消默认结果:', cancelResult);
        }
      }

      const updateData: any = {};
      if (name !== undefined) updateData.name = name.trim();
      if (address !== undefined) updateData.address = address.trim();
      if (isActive !== undefined) updateData.isActive = isActive;
      if (isDefault !== undefined) updateData.isDefault = isDefault;
      if (description !== undefined) updateData.description = description?.trim();

      console.log('更新数据:', updateData);

      const success = await dbService.updatePlatformWallet(id, updateData);
      console.log('更新结果:', success);

      if (!success) {
        return NextResponse.json({ 
          error: '更新平台钱包失败',
          debug: { updateData, existingWallet }
        }, { status: 500 });
      }

      // 获取更新后的钱包
      const updatedWallets = await dbService.getAllPlatformWallets();
      const updatedWallet = updatedWallets.find(w => w.id === id);

      return NextResponse.json({
        success: true,
        message: '平台钱包更新成功',
        debug: {
          before: existingWallet,
          after: updatedWallet,
          updateData
        }
      });
    } catch (error: any) {
      console.error('更新平台钱包失败:', error);
      return NextResponse.json({ 
        error: '更新平台钱包失败',
        details: error.message,
        stack: error.stack
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('更新平台钱包失败:', error);
    return NextResponse.json({ 
      error: '更新平台钱包失败',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
