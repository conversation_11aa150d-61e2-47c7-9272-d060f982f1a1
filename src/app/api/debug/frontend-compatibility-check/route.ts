import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';

// 前端兼容性检查
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        success: false,
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ 
        success: false, 
        error: '权限不足' 
      }, { status: 403 });
    }

    // 测试各个页面的API调用
    const pageTests = [
      {
        name: '钱包页面',
        url: '/wallet',
        apis: ['/api/wallets', '/api/wallets/balance'],
        description: '钱包列表和余额查询'
      },
      {
        name: '刷单页面',
        url: '/brush',
        apis: ['/api/brush/balance', '/api/products', '/api/brush/orders', '/api/wallets'],
        description: '刷单功能相关API'
      },
      {
        name: '管理员页面',
        url: '/admin',
        apis: ['/api/admin/users', '/api/admin/wallets', '/api/admin/invite-codes'],
        description: '管理员数据展示'
      },
      {
        name: '刷单管理页面',
        url: '/admin/brush',
        apis: ['/api/admin/brush-rules', '/api/products', '/api/brush/orders'],
        description: '刷单管理功能'
      }
    ];

    const results = [];
    const baseUrl = request.url.replace('/api/debug/frontend-compatibility-check', '');

    for (const page of pageTests) {
      const pageResult = {
        name: page.name,
        url: page.url,
        description: page.description,
        apis: [],
        status: 'unknown',
        issues: []
      };

      // 测试每个API
      for (const apiUrl of page.apis) {
        try {
          const response = await fetch(`${baseUrl}${apiUrl}`, {
            headers: {
              'Cookie': request.headers.get('Cookie') || ''
            }
          });

          const data = await response.json();
          
          const apiResult = {
            url: apiUrl,
            status: response.status,
            hasSuccess: 'success' in data,
            hasData: 'data' in data,
            hasError: 'error' in data,
            format: 'unknown'
          };

          // 判断格式
          if (apiResult.hasSuccess && data.success && apiResult.hasData) {
            apiResult.format = 'correct';
          } else if (apiResult.hasSuccess && !data.success && apiResult.hasError) {
            apiResult.format = 'correct_error';
          } else if (!apiResult.hasSuccess && Array.isArray(data)) {
            apiResult.format = 'legacy_array';
            pageResult.issues.push(`${apiUrl} 返回数组格式，需要前端适配`);
          } else if (!apiResult.hasSuccess && typeof data === 'object') {
            apiResult.format = 'legacy_object';
            pageResult.issues.push(`${apiUrl} 返回对象格式，需要前端适配`);
          }

          pageResult.apis.push(apiResult);
        } catch (error: any) {
          pageResult.apis.push({
            url: apiUrl,
            status: 'error',
            error: error.message,
            format: 'error'
          });
          pageResult.issues.push(`${apiUrl} 请求失败: ${error.message}`);
        }
      }

      // 判断页面整体状态
      const correctApis = pageResult.apis.filter(api => api.format === 'correct').length;
      const totalApis = pageResult.apis.length;
      
      if (pageResult.issues.length === 0) {
        pageResult.status = 'compatible';
      } else if (correctApis > totalApis / 2) {
        pageResult.status = 'mostly_compatible';
      } else {
        pageResult.status = 'needs_fix';
      }

      results.push(pageResult);
    }

    // 生成总结
    const summary = {
      totalPages: results.length,
      compatible: results.filter(r => r.status === 'compatible').length,
      mostlyCompatible: results.filter(r => r.status === 'mostly_compatible').length,
      needsFix: results.filter(r => r.status === 'needs_fix').length,
      totalIssues: results.reduce((sum, r) => sum + r.issues.length, 0)
    };

    return NextResponse.json({
      success: true,
      data: {
        summary,
        results,
        recommendations: [
          '所有页面都应该正确处理 {success: boolean, data?: any, error?: string} 格式',
          '优先修复 needs_fix 状态的页面',
          '检查前端代码中的 .map() 调用，确保数据是数组类型',
          '添加适当的错误处理和加载状态'
        ]
      }
    });

  } catch (error: any) {
    console.error('前端兼容性检查失败:', error);
    return NextResponse.json({ 
      success: false,
      error: '检查失败',
      details: error.message 
    }, { status: 500 });
  }
}
