import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 初始化平台钱包地址（仅用于测试）
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 创建测试用的平台钱包地址
    const mainnetWallet = await dbService.createPlatformWallet({
      name: '主网USDT收款钱包',
      address: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', // 示例地址，请替换为真实地址
      network: 'mainnet',
      currency: 'USDT',
      isActive: true,
      isDefault: true,
      description: '主网USDT充值收款地址'
    });

    const nileWallet = await dbService.createPlatformWallet({
      name: '测试网USDT收款钱包',
      address: 'TYsbWxNnyTgsZaTFaue9hqVFFZZZZZZZZZ', // 示例地址，请替换为真实地址
      network: 'nile',
      currency: 'USDT',
      isActive: true,
      isDefault: true,
      description: '测试网USDT充值收款地址'
    });

    return NextResponse.json({
      success: true,
      message: '平台钱包初始化成功',
      data: {
        mainnetWallet,
        nileWallet
      }
    });
  } catch (error: any) {
    console.error('初始化平台钱包失败:', error);
    return NextResponse.json({ 
      error: '初始化失败', 
      details: error.message 
    }, { status: 500 });
  }
}
