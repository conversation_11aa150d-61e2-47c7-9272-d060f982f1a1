import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';

// API响应格式检查工具
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        success: false,
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ 
        success: false, 
        error: '权限不足' 
      }, { status: 403 });
    }

    // 测试各个API的响应格式
    const apiTests = [
      { name: '钱包列表', url: '/api/wallets' },
      { name: '用户列表', url: '/api/admin/users' },
      { name: '邀请码列表', url: '/api/admin/invite-codes' },
      { name: '商品列表', url: '/api/products' },
      { name: '刷单规则', url: '/api/admin/brush-rules' },
      { name: '刷单订单', url: '/api/brush/orders' },
      { name: '刷单余额', url: '/api/brush/balance' },
      { name: '用户信息', url: '/api/auth/me' }
    ];

    const results = [];
    const baseUrl = request.url.replace('/api/debug/api-format-check', '');

    for (const test of apiTests) {
      try {
        const response = await fetch(`${baseUrl}${test.url}`, {
          headers: {
            'Cookie': request.headers.get('Cookie') || ''
          }
        });

        const data = await response.json();
        
        // 检查响应格式
        const hasSuccess = 'success' in data;
        const hasData = 'data' in data;
        const hasError = 'error' in data;
        
        let formatStatus = 'unknown';
        if (hasSuccess && data.success && hasData) {
          formatStatus = 'correct_success';
        } else if (hasSuccess && !data.success && hasError) {
          formatStatus = 'correct_error';
        } else if (!hasSuccess && Array.isArray(data)) {
          formatStatus = 'legacy_array';
        } else if (!hasSuccess && typeof data === 'object' && !hasError) {
          formatStatus = 'legacy_object';
        } else if (hasError && !hasSuccess) {
          formatStatus = 'partial_error';
        }

        results.push({
          name: test.name,
          url: test.url,
          status: response.status,
          formatStatus,
          hasSuccess,
          hasData,
          hasError,
          isArray: Array.isArray(data),
          sampleKeys: Object.keys(data).slice(0, 5),
          recommendation: getRecommendation(formatStatus)
        });
      } catch (error: any) {
        results.push({
          name: test.name,
          url: test.url,
          status: 'error',
          formatStatus: 'request_failed',
          error: error.message,
          recommendation: '修复API请求错误'
        });
      }
    }

    // 统计结果
    const summary = {
      total: results.length,
      correctFormat: results.filter(r => r.formatStatus?.startsWith('correct')).length,
      legacyFormat: results.filter(r => r.formatStatus?.startsWith('legacy')).length,
      partialFormat: results.filter(r => r.formatStatus === 'partial_error').length,
      errors: results.filter(r => r.formatStatus === 'request_failed').length
    };

    return NextResponse.json({
      success: true,
      data: {
        summary,
        results,
        recommendations: {
          standard: '所有API应使用 { success: boolean, data?: any, error?: string } 格式',
          migration: '优先修复用户常用的API，如钱包、订单、余额等',
          testing: '修改后请测试前端调用是否正常工作'
        }
      }
    });

  } catch (error: any) {
    console.error('API格式检查失败:', error);
    return NextResponse.json({ 
      success: false,
      error: '检查失败',
      details: error.message 
    }, { status: 500 });
  }
}

function getRecommendation(formatStatus: string): string {
  switch (formatStatus) {
    case 'correct_success':
    case 'correct_error':
      return '✅ 格式正确';
    case 'legacy_array':
      return '🔄 需要包装为 { success: true, data: array }';
    case 'legacy_object':
      return '🔄 需要包装为 { success: true, data: object }';
    case 'partial_error':
      return '🔄 错误响应需要添加 success: false';
    case 'request_failed':
      return '❌ 修复API错误';
    default:
      return '❓ 需要检查响应格式';
  }
}
