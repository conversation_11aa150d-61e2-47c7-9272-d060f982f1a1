import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import fs from 'fs';
import path from 'path';

// 检查前端API调用格式
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        success: false,
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin'].includes(authResult.user.role)) {
      return NextResponse.json({ 
        success: false, 
        error: '权限不足' 
      }, { status: 403 });
    }

    const srcPath = path.join(process.cwd(), 'src');
    const issues: any[] = [];

    // 递归扫描文件
    function scanDirectory(dir: string) {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          scanDirectory(filePath);
        } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
          scanFile(filePath);
        }
      }
    }

    function scanFile(filePath: string) {
      try {
        const content = fs.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        const relativePath = path.relative(srcPath, filePath);
        
        lines.forEach((line, index) => {
          const lineNumber = index + 1;
          
          // 检查可能有问题的模式
          const patterns = [
            {
              pattern: /const\s+\w+\s*=\s*await\s+response\.json\(\)\s*as\s+\w+\[\]/,
              issue: '直接将响应转换为数组类型，可能需要适配新的 {success, data} 格式',
              severity: 'high'
            },
            {
              pattern: /const\s+\w+\s*=\s*await\s+response\.json\(\);\s*set\w+\(\w+\);/,
              issue: '直接使用响应数据，可能需要检查 success 字段',
              severity: 'medium'
            },
            {
              pattern: /\.map\s*\(\s*\(\w+\)\s*=>/,
              issue: '使用 .map() 方法，确保数据是数组类型',
              severity: 'low'
            },
            {
              pattern: /fetch\(['"`]\/api\/\w+/,
              issue: 'API调用，需要检查响应格式处理',
              severity: 'info'
            }
          ];

          patterns.forEach(({ pattern, issue, severity }) => {
            if (pattern.test(line)) {
              issues.push({
                file: relativePath,
                line: lineNumber,
                content: line.trim(),
                issue,
                severity,
                suggestion: getSuggestion(line, pattern)
              });
            }
          });
        });
      } catch (error) {
        console.error(`扫描文件失败: ${filePath}`, error);
      }
    }

    function getSuggestion(line: string, pattern: RegExp): string {
      if (pattern.source.includes('as\\s+\\w+\\[\\]')) {
        return '改为: const result = await response.json(); if (result.success && result.data) { ... }';
      }
      if (pattern.source.includes('response\\.json\\(\\)')) {
        return '检查是否需要验证 result.success 字段';
      }
      if (pattern.source.includes('\\.map')) {
        return '确保调用 .map() 的变量是数组类型';
      }
      if (pattern.source.includes('fetch')) {
        return '检查API响应格式是否为 {success: boolean, data?: any, error?: string}';
      }
      return '请检查此行代码是否需要适配新的API响应格式';
    }

    // 扫描所有文件
    scanDirectory(srcPath);

    // 按严重程度分组
    const groupedIssues = {
      high: issues.filter(i => i.severity === 'high'),
      medium: issues.filter(i => i.severity === 'medium'),
      low: issues.filter(i => i.severity === 'low'),
      info: issues.filter(i => i.severity === 'info')
    };

    // 统计信息
    const summary = {
      totalFiles: issues.reduce((acc, issue) => {
        if (!acc.includes(issue.file)) acc.push(issue.file);
        return acc;
      }, [] as string[]).length,
      totalIssues: issues.length,
      highPriority: groupedIssues.high.length,
      mediumPriority: groupedIssues.medium.length,
      lowPriority: groupedIssues.low.length,
      info: groupedIssues.info.length
    };

    return NextResponse.json({
      success: true,
      data: {
        summary,
        issues: groupedIssues,
        recommendations: [
          '优先修复 high 和 medium 级别的问题',
          '检查所有 API 调用是否正确处理 {success, data, error} 格式',
          '确保数组操作前验证数据类型',
          '添加适当的错误处理'
        ]
      }
    });

  } catch (error: any) {
    console.error('前端API使用检查失败:', error);
    return NextResponse.json({ 
      success: false,
      error: '检查失败',
      details: error.message 
    }, { status: 500 });
  }
}
