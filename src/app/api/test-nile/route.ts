import { NextRequest, NextResponse } from 'next/server';
import { createTronWebInstance, getAccountBalance } from '@/lib/tronweb-server';

// 测试Nile网络连接
export async function GET(request: NextRequest) {
  try {
    console.log('开始测试Nile网络连接...');
    
    // 测试创建Nile网络TronWeb实例
    const tronWeb = createTronWebInstance('nile');
    
    // 测试地址（Nile测试网USDT合约地址）
    const testAddress = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
    
    console.log(`测试地址: ${testAddress}`);
    
    // 测试获取账户信息
    const accountInfo = await tronWeb.trx.getAccount(testAddress);
    console.log('Nile网络账户信息获取成功:', accountInfo);
    
    // 测试获取TRX余额
    const trxBalance = await tronWeb.trx.getBalance(testAddress);
    const trxAmount = tronWeb.fromSun(trxBalance);
    console.log(`Nile网络TRX余额: ${trxAmount}`);
    
    // 测试获取最新区块
    const latestBlock = await tronWeb.trx.getCurrentBlock();
    console.log(`Nile网络最新区块: ${latestBlock.block_header.raw_data.number}`);
    
    // 测试USDT余额
    let usdtBalance = 0;
    try {
      const usdtAmount = await getAccountBalance(testAddress, 'USDT', 'nile');
      usdtBalance = usdtAmount;
      console.log(`Nile网络USDT余额: ${usdtBalance}`);
    } catch (usdtError) {
      console.warn('Nile网络USDT余额获取失败:', usdtError);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Nile网络连接测试成功',
      network: 'nile',
      data: {
        testAddress: testAddress,
        accountInfo: {
          address: accountInfo.address,
          trxBalance: trxAmount + ' TRX',
          usdtBalance: usdtBalance + ' USDT'
        },
        latestBlock: latestBlock.block_header.raw_data.number,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error: any) {
    console.error('Nile网络测试失败:', error);
    
    return NextResponse.json({
      success: false,
      network: 'nile',
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        stack: error.stack?.split('\n').slice(0, 5) // 只返回前5行堆栈
      }
    }, { status: 500 });
  }
}
