'use client';

import { useState, useEffect } from 'react';
import { Button, Card, CardBody, Modal, ModalContent, ModalHeader, ModalBody } from "@heroui/react";

export default function TestBrushModal() {
  const [userBalance, setUserBalance] = useState<any>(null);
  const [userWallets, setUserWallets] = useState<any[]>([]);
  const [rechargeInfo, setRechargeInfo] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // 加载余额
      const balanceRes = await fetch('/api/brush/balance');
      if (balanceRes.ok) {
        const balanceData = await balanceRes.json();
        setUserBalance(balanceData.data);
        console.log('余额数据:', balanceData.data);
      }

      // 加载钱包
      const walletsRes = await fetch('/api/wallets');
      if (walletsRes.ok) {
        const walletsResult = await walletsRes.json();
        if (walletsResult.success && walletsResult.data) {
          setUserWallets(walletsResult.data);
          console.log('钱包数据:', walletsResult.data);
        }
      }

      // 加载充值信息
      const rechargeRes = await fetch('/api/brush/recharge?network=mainnet');
      if (rechargeRes.ok) {
        const rechargeData = await rechargeRes.json();
        setRechargeInfo(rechargeData.data);
        console.log('充值信息:', rechargeData.data);
      }

      setLoading(false);
    } catch (error) {
      console.error('加载数据失败:', error);
      setLoading(false);
    }
  };

  const checkModal = () => {
    console.log('检查弹窗条件:');
    console.log('- userBalance:', userBalance);
    console.log('- userWallets:', userWallets);
    console.log('- rechargeInfo:', rechargeInfo);

    if (userBalance && userBalance.balance < 10) {
      console.log('余额不足，应该显示弹窗');
      setShowModal(true);
    } else {
      console.log('余额充足');
    }
  };

  useEffect(() => {
    if (!loading && userBalance && rechargeInfo) {
      setTimeout(checkModal, 1000);
    }
  }, [loading, userBalance, rechargeInfo]);

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">测试刷单弹窗</h1>
        
        <div className="grid gap-6">
          <Card>
            <CardBody>
              <h3 className="text-lg font-semibold mb-4">数据状态</h3>
              <div className="space-y-2 text-sm">
                <p><strong>加载状态:</strong> {loading ? '加载中...' : '已完成'}</p>
                <p><strong>用户余额:</strong> {userBalance ? `${userBalance.balance} 元` : '未加载'}</p>
                <p><strong>钱包数量:</strong> {userWallets.length}</p>
                <p><strong>充值信息:</strong> {rechargeInfo ? '已加载' : '未加载'}</p>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody>
              <h3 className="text-lg font-semibold mb-4">测试按钮</h3>
              <div className="flex gap-4">
                <Button color="primary" onPress={checkModal}>
                  手动检查弹窗
                </Button>
                <Button color="success" onPress={() => setShowModal(true)}>
                  强制显示弹窗
                </Button>
                <Button color="warning" onPress={loadData}>
                  重新加载数据
                </Button>
              </div>
            </CardBody>
          </Card>

          {userBalance && (
            <Card>
              <CardBody>
                <h3 className="text-lg font-semibold mb-4">余额详情</h3>
                <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
                  {JSON.stringify(userBalance, null, 2)}
                </pre>
              </CardBody>
            </Card>
          )}

          {userWallets.length > 0 && (
            <Card>
              <CardBody>
                <h3 className="text-lg font-semibold mb-4">钱包详情</h3>
                <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
                  {JSON.stringify(userWallets, null, 2)}
                </pre>
              </CardBody>
            </Card>
          )}
        </div>

        {/* 测试弹窗 */}
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          size="lg"
        >
          <ModalContent>
            <ModalHeader>
              <h3 className="text-xl font-semibold">🚀 测试弹窗</h3>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <p className="text-sm text-blue-800">
                    这是测试弹窗，用于验证弹窗显示逻辑是否正常。
                  </p>
                </div>
                <div className="space-y-2">
                  <p><strong>当前余额:</strong> {userBalance?.balance || 0} 元</p>
                  <p><strong>是否应该显示:</strong> {userBalance?.balance < 10 ? '是' : '否'}</p>
                </div>
              </div>
            </ModalBody>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
