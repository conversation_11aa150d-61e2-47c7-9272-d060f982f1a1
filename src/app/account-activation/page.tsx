'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@heroui/react";
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function AccountActivationPage() {
  const router = useRouter();
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  // 示例地址（这里应该从URL参数或状态中获取实际地址）
  const inactiveAddress = "TAjz662ivGjK792yaiTaeJcthDD4wTtX4m";

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedAddress(text);
    setTimeout(() => setCopiedAddress(null), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 p-4">
      <div className="max-w-4xl mx-auto pt-8">
        <Card className="border-2 border-blue-200 shadow-lg">
          <CardHeader className="bg-blue-100 border-b border-blue-200">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">🔐</div>
              <div>
                <h1 className="text-2xl font-bold text-blue-800">TRON账户激活指南</h1>
                <p className="text-blue-600">您的钱包需要先激活才能进行转账</p>
              </div>
            </div>
          </CardHeader>
          
          <CardBody className="space-y-6 p-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ 账户未激活</h2>
              <p className="text-yellow-700">
                您的钱包地址 <code className="bg-yellow-100 px-2 py-1 rounded">{inactiveAddress}</code> 
                尚未在TRON网络上激活。在TRON网络中，新创建的账户需要先接收一笔转账才能被激活。
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-blue-800 mb-3">🚀 如何激活账户</h2>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Chip color="primary" variant="flat" size="sm">1</Chip>
                  <div>
                    <p className="font-medium text-blue-700">获取一些TRX</p>
                    <p className="text-blue-600 text-sm">从交易所、朋友或其他已激活的钱包获取至少1 TRX</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Chip color="primary" variant="flat" size="sm">2</Chip>
                  <div>
                    <p className="font-medium text-blue-700">转账到您的钱包</p>
                    <p className="text-blue-600 text-sm">将TRX转账到下面的地址来激活账户</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Chip color="primary" variant="flat" size="sm">3</Chip>
                  <div>
                    <p className="font-medium text-blue-700">等待确认</p>
                    <p className="text-blue-600 text-sm">转账确认后，您的账户就会被激活</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-gray-800 mb-3">📋 您的钱包地址</h2>
              <div className="flex items-center space-x-2 bg-white p-3 rounded border">
                <code className="flex-1 text-sm font-mono break-all">{inactiveAddress}</code>
                <Button
                  size="sm"
                  color={copiedAddress === inactiveAddress ? "success" : "primary"}
                  variant="flat"
                  onClick={() => copyToClipboard(inactiveAddress)}
                >
                  {copiedAddress === inactiveAddress ? "已复制" : "复制"}
                </Button>
              </div>
              <p className="text-gray-600 text-sm mt-2">
                请将此地址发送给朋友，或在交易所提现时使用此地址
              </p>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-green-800 mb-3">💡 激活建议</h2>
              <ul className="text-green-700 space-y-2">
                <li>• <strong>最少金额</strong>: 建议转入至少 1 TRX 来激活账户</li>
                <li>• <strong>推荐金额</strong>: 转入 10-20 TRX 以确保有足够的手续费</li>
                <li>• <strong>网络选择</strong>: 确保使用正确的网络（主网/测试网）</li>
                <li>• <strong>确认时间</strong>: 通常需要1-3分钟确认</li>
              </ul>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-purple-800 mb-3">🔗 获取TRX的方式</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-3 rounded border">
                  <h3 className="font-medium text-purple-700 mb-2">交易所购买</h3>
                  <ul className="text-purple-600 text-sm space-y-1">
                    <li>• Binance</li>
                    <li>• Huobi</li>
                    <li>• OKX</li>
                    <li>• Gate.io</li>
                  </ul>
                </div>
                
                <div className="bg-white p-3 rounded border">
                  <h3 className="font-medium text-purple-700 mb-2">其他方式</h3>
                  <ul className="text-purple-600 text-sm space-y-1">
                    <li>• 朋友转账</li>
                    <li>• TRON水龙头（测试网）</li>
                    <li>• 去中心化交易所</li>
                    <li>• P2P交易</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h2 className="text-lg font-semibold text-red-800 mb-2">⚠️ 注意事项</h2>
              <ul className="text-red-700 space-y-1">
                <li>• 确保使用正确的网络（主网地址不能在测试网使用）</li>
                <li>• 保存好您的私钥和助记词</li>
                <li>• 小额测试后再进行大额转账</li>
                <li>• 激活后才能进行转出操作</li>
              </ul>
            </div>

            <div className="flex justify-center space-x-4 pt-4">
              <Button 
                color="primary" 
                onClick={() => router.push('/wallet')}
                className="px-6"
              >
                返回钱包
              </Button>
              <Button 
                color="secondary" 
                variant="bordered"
                onClick={() => window.location.reload()}
                className="px-6"
              >
                刷新检查
              </Button>
            </div>
          </CardBody>
        </Card>

        <div className="mt-6 text-center text-gray-600">
          <p>如有任何疑问，请联系客服或查看TRON官方文档</p>
          <p className="text-sm mt-2">
            <a 
              href="https://developers.tron.network/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
            >
              TRON开发者文档
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
