'use client';

import { useState } from 'react';
import { Button, Input, Card, CardBody, CardHeader, Select, SelectItem, Divider } from '@heroui/react';

export default function TestNileCompletePage() {
  const [testAddress, setTestAddress] = useState('TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testNileWallet = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-nile-wallet');
      const data = await response.json();
      
      setResults(prev => ({
        ...prev,
        walletTest: {
          success: response.ok,
          data: data,
          timestamp: new Date().toLocaleString()
        }
      }));
      
    } catch (error) {
      setResults(prev => ({
        ...prev,
        walletTest: {
          success: false,
          error: error,
          timestamp: new Date().toLocaleString()
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const testBalance = async (currency: 'TRX' | 'USDT') => {
    setLoading(true);
    try {
      const response = await fetch('/api/wallets/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: testAddress,
          currency: currency,
          network: 'nile'
        }),
      });

      const data = await response.json();
      
      setResults(prev => ({
        ...prev,
        [`${currency}Balance`]: {
          success: response.ok,
          data: data,
          timestamp: new Date().toLocaleString()
        }
      }));
      
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [`${currency}Balance`]: {
          success: false,
          error: error,
          timestamp: new Date().toLocaleString()
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">Nile网络完整功能测试</h1>
        </CardHeader>
        <CardBody className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 测试控制面板 */}
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold">测试控制</h2>
              </CardHeader>
              <CardBody className="space-y-4">
                <Input
                  label="测试地址"
                  value={testAddress}
                  onChange={(e) => setTestAddress(e.target.value)}
                  placeholder="输入Nile网络TRON地址"
                  description="默认为Nile测试网USDT合约地址"
                />
                
                <div className="flex flex-col gap-2">
                  <Button
                    color="primary"
                    onPress={testNileWallet}
                    isLoading={loading}
                    className="w-full"
                  >
                    测试钱包创建
                  </Button>
                  
                  <Button
                    color="secondary"
                    onPress={() => testBalance('TRX')}
                    isLoading={loading}
                    className="w-full"
                  >
                    测试TRX余额
                  </Button>
                  
                  <Button
                    color="success"
                    onPress={() => testBalance('USDT')}
                    isLoading={loading}
                    className="w-full"
                  >
                    测试USDT余额
                  </Button>
                </div>

                <Divider />

                <div className="text-sm text-gray-600">
                  <h3 className="font-semibold mb-2">Nile测试网信息</h3>
                  <ul className="space-y-1">
                    <li>• 网络: Nile Testnet</li>
                    <li>• 浏览器: nile.tronscan.org</li>
                    <li>• USDT合约: TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf</li>
                    <li>• 免费TRX: 可从水龙头获取</li>
                  </ul>
                </div>
              </CardBody>
            </Card>

            {/* 网络状态 */}
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold">网络状态</h2>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>网络:</span>
                    <span className="text-blue-600 font-medium">Nile Testnet</span>
                  </div>
                  <div className="flex justify-between">
                    <span>状态:</span>
                    <span className="text-green-600 font-medium">正常</span>
                  </div>
                  <div className="flex justify-between">
                    <span>API密钥:</span>
                    <span className="text-green-600 font-medium">已配置</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* 测试结果 */}
          {results && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">测试结果</h2>
              
              {results.walletTest && (
                <Card>
                  <CardHeader>
                    <h3 className="text-md font-semibold">钱包创建测试</h3>
                  </CardHeader>
                  <CardBody>
                    <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64">
                      {JSON.stringify(results.walletTest, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}

              {results.TRXBalance && (
                <Card>
                  <CardHeader>
                    <h3 className="text-md font-semibold">TRX余额测试</h3>
                  </CardHeader>
                  <CardBody>
                    <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64">
                      {JSON.stringify(results.TRXBalance, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}

              {results.USDTBalance && (
                <Card>
                  <CardHeader>
                    <h3 className="text-md font-semibold">USDT余额测试</h3>
                  </CardHeader>
                  <CardBody>
                    <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-64">
                      {JSON.stringify(results.USDTBalance, null, 2)}
                    </pre>
                  </CardBody>
                </Card>
              )}
            </div>
          )}

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">测试说明</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 钱包创建测试：验证Nile网络钱包创建和余额查询</li>
              <li>• TRX余额测试：验证Nile网络TRX余额查询功能</li>
              <li>• USDT余额测试：验证Nile网络USDT余额查询功能</li>
              <li>• 所有测试都使用Nile测试网络，不会产生实际费用</li>
            </ul>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
