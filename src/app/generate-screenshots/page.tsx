'use client';

import { useRef, useEffect } from 'react';
import { Button } from '@heroui/react';

export default function GenerateScreenshots() {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const generateScreenshot = (width: number, height: number, title: string, filename: string) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // 添加文字
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    
    // 主标题
    ctx.font = 'bold 32px Arial, sans-serif';
    ctx.fillText('TRON钱包', width / 2, height * 0.4);
    
    // 副标题
    ctx.font = '20px Arial, sans-serif';
    ctx.fillText(title, width / 2, height * 0.5);
    
    // 尺寸信息
    ctx.font = '16px Arial, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.fillText(`${width} × ${height}`, width / 2, height * 0.6);

    // 下载图片
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
      }
    }, 'image/png');
  };

  const screenshots = [
    { width: 390, height: 844, title: '移动端界面', filename: 'mobile-1.png' },
    { width: 390, height: 844, title: '刷单功能', filename: 'mobile-2.png' },
    { width: 1280, height: 800, title: '桌面端界面', filename: 'desktop-1.png' },
    { width: 1280, height: 800, title: '管理后台', filename: 'desktop-2.png' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">生成应用截图</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">截图生成器</h2>
          <p className="text-gray-600 mb-6">
            点击下面的按钮生成应用截图。生成的图片将自动下载到您的设备。
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {screenshots.map((screenshot, index) => (
              <Button
                key={index}
                color="primary"
                variant="bordered"
                onPress={() => generateScreenshot(
                  screenshot.width, 
                  screenshot.height, 
                  screenshot.title, 
                  screenshot.filename
                )}
                className="h-20 flex flex-col"
              >
                <div className="font-semibold">{screenshot.title}</div>
                <div className="text-sm opacity-70">
                  {screenshot.width} × {screenshot.height}
                </div>
                <div className="text-xs opacity-50">{screenshot.filename}</div>
              </Button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">使用说明</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-600">
            <li>点击上方按钮生成对应的截图文件</li>
            <li>将下载的PNG文件放入 <code className="bg-gray-100 px-2 py-1 rounded">public/screenshots/</code> 目录</li>
            <li>确保文件名与按钮上显示的文件名一致</li>
            <li>重启开发服务器以应用更改</li>
          </ol>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">提示</h3>
            <p className="text-blue-700 text-sm">
              这些是占位截图。在生产环境中，建议使用实际的应用界面截图来替换这些生成的图片。
            </p>
          </div>
        </div>

        {/* 隐藏的Canvas用于生成图片 */}
        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />
      </div>
    </div>
  );
}
