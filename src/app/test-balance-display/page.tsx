'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@heroui/react';

// 安全的余额格式化函数
const formatBalance = (balance: number | string | undefined, decimals: number = 6): string => {
  if (balance === undefined || balance === null) {
    return '0.' + '0'.repeat(decimals);
  }
  
  const numBalance = typeof balance === 'string' ? parseFloat(balance) : balance;
  
  if (isNaN(numBalance)) {
    return '0.' + '0'.repeat(decimals);
  }
  
  return numBalance.toFixed(decimals);
};

export default function TestBalanceDisplayPage() {
  const [testData, setTestData] = useState<any>(null);

  const testBalanceFormatting = () => {
    const testCases = [
      { value: 114.935995, type: 'number', expected: '114.94' },
      { value: '114.935995', type: 'string', expected: '114.94' },
      { value: 0, type: 'zero', expected: '0.00' },
      { value: '0', type: 'string zero', expected: '0.00' },
      { value: undefined, type: 'undefined', expected: '0.00' },
      { value: null, type: 'null', expected: '0.00' },
      { value: 'invalid', type: 'invalid string', expected: '0.00' },
      { value: 1234.56789, type: 'large number', expected: '1234.57' },
    ];

    const results = testCases.map(testCase => ({
      ...testCase,
      result: formatBalance(testCase.value, 2),
      success: formatBalance(testCase.value, 2) === testCase.expected
    }));

    setTestData(results);
  };

  const testRealBalance = async () => {
    try {
      const response = await fetch('/api/test-usdt-simple');
      const data = await response.json();
      
      setTestData({
        type: 'real_api',
        success: response.ok,
        data: data,
        formatted: {
          TRX: formatBalance(data.balances?.TRX, 6),
          USDT: formatBalance(data.balances?.USDT, 2)
        }
      });
    } catch (error) {
      setTestData({
        type: 'real_api',
        success: false,
        error: error
      });
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">余额显示测试</h1>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="flex gap-4">
            <Button
              color="primary"
              onPress={testBalanceFormatting}
            >
              测试格式化函数
            </Button>
            
            <Button
              color="secondary"
              onPress={testRealBalance}
            >
              测试真实余额API
            </Button>
          </div>

          {testData && (
            <div className="space-y-4">
              <h2 className="text-lg font-semibold">测试结果</h2>
              
              {Array.isArray(testData) ? (
                // 格式化函数测试结果
                <div className="space-y-2">
                  {testData.map((test, index) => (
                    <Card key={index} className={test.success ? 'bg-green-50' : 'bg-red-50'}>
                      <CardBody>
                        <div className="grid grid-cols-5 gap-4 text-sm">
                          <div>
                            <strong>输入:</strong> {JSON.stringify(test.value)}
                          </div>
                          <div>
                            <strong>类型:</strong> {test.type}
                          </div>
                          <div>
                            <strong>期望:</strong> {test.expected}
                          </div>
                          <div>
                            <strong>结果:</strong> {test.result}
                          </div>
                          <div>
                            <strong>状态:</strong> 
                            <span className={test.success ? 'text-green-600' : 'text-red-600'}>
                              {test.success ? '✅ 通过' : '❌ 失败'}
                            </span>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              ) : testData.type === 'real_api' ? (
                // 真实API测试结果
                <Card className={testData.success ? 'bg-green-50' : 'bg-red-50'}>
                  <CardHeader>
                    <h3 className="font-semibold">真实API测试结果</h3>
                  </CardHeader>
                  <CardBody>
                    {testData.success ? (
                      <div className="space-y-2">
                        <div>
                          <strong>原始数据:</strong>
                          <pre className="text-sm bg-gray-100 p-2 rounded mt-1">
                            {JSON.stringify(testData.data.balances, null, 2)}
                          </pre>
                        </div>
                        <div>
                          <strong>格式化后:</strong>
                          <div className="grid grid-cols-2 gap-4 mt-2">
                            <div className="text-center p-4 bg-blue-100 rounded">
                              <div className="text-2xl font-bold text-blue-600">
                                {testData.formatted.TRX} TRX
                              </div>
                              <div className="text-sm text-gray-600">TRON</div>
                            </div>
                            <div className="text-center p-4 bg-green-100 rounded">
                              <div className="text-2xl font-bold text-green-600">
                                {testData.formatted.USDT} USDT
                              </div>
                              <div className="text-sm text-gray-600">Tether USD</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-red-600">
                        错误: {JSON.stringify(testData.error)}
                      </div>
                    )}
                  </CardBody>
                </Card>
              ) : null}
            </div>
          )}

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">说明</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 测试格式化函数：验证不同类型的余额数据格式化</li>
              <li>• 测试真实API：从实际API获取余额并格式化显示</li>
              <li>• 确保所有类型的余额数据都能正确显示</li>
              <li>• 处理undefined、null、字符串、数字等各种情况</li>
            </ul>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
