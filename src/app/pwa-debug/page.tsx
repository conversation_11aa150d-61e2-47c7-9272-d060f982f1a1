'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardBody, CardHead<PERSON>, <PERSON><PERSON>, Chip } from '@heroui/react';

interface PWADebugInfo {
  isSecure: boolean;
  hasServiceWorker: boolean;
  hasManifest: boolean;
  isStandalone: boolean;
  userAgent: string;
  isAndroid: boolean;
  isChrome: boolean;
  manifestUrl: string | null;
  serviceWorkerState: string;
  installPromptAvailable: boolean;
}

export default function PWADebugPage() {
  const [debugInfo, setDebugInfo] = useState<PWADebugInfo | null>(null);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [installResult, setInstallResult] = useState<string | null>(null);

  useEffect(() => {
    // 收集PWA调试信息
    const collectDebugInfo = async () => {
      const userAgent = navigator.userAgent;
      const isAndroid = /Android/.test(userAgent);
      const isChrome = userAgent.includes('Chrome');
      
      // 检查manifest
      const manifestLink = document.querySelector('link[rel="manifest"]') as HTMLLinkElement;
      const manifestUrl = manifestLink?.href || null;
      
      // 检查Service Worker状态
      let serviceWorkerState = 'Not supported';
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.getRegistration();
          if (registration) {
            serviceWorkerState = registration.active ? 'Active' : 'Registered but not active';
          } else {
            serviceWorkerState = 'Not registered';
          }
        } catch (error) {
          serviceWorkerState = 'Error checking';
        }
      }

      const info: PWADebugInfo = {
        isSecure: location.protocol === 'https:' || location.hostname === 'localhost',
        hasServiceWorker: 'serviceWorker' in navigator,
        hasManifest: manifestLink !== null,
        isStandalone: window.matchMedia('(display-mode: standalone)').matches,
        userAgent,
        isAndroid,
        isChrome,
        manifestUrl,
        serviceWorkerState,
        installPromptAvailable: deferredPrompt !== null
      };

      setDebugInfo(info);
    };

    collectDebugInfo();

    // 监听PWA安装事件
    const handleBeforeInstallPrompt = (e: Event) => {
      console.log('beforeinstallprompt event fired');
      e.preventDefault();
      setDeferredPrompt(e);
      collectDebugInfo(); // 更新调试信息
    };

    const handleAppInstalled = () => {
      console.log('appinstalled event fired');
      setInstallResult('应用安装成功！');
      setDeferredPrompt(null);
      collectDebugInfo(); // 更新调试信息
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [deferredPrompt]);

  const handleTestInstall = async () => {
    if (!deferredPrompt) {
      setInstallResult('没有可用的安装提示');
      return;
    }

    try {
      setInstallResult('正在显示安装提示...');
      
      // 显示安装提示
      await deferredPrompt.prompt();
      
      // 等待用户选择
      const choiceResult = await deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        setInstallResult('用户接受了安装');
      } else {
        setInstallResult('用户拒绝了安装');
      }
      
      setDeferredPrompt(null);
      
    } catch (error: any) {
      setInstallResult(`安装失败: ${error.message}`);
    }
  };

  const handleCheckManifest = async () => {
    if (!debugInfo?.manifestUrl) {
      alert('未找到manifest文件');
      return;
    }

    try {
      const response = await fetch(debugInfo.manifestUrl);
      const manifest = await response.json();
      
      console.log('Manifest内容:', manifest);
      alert(`Manifest检查完成，请查看控制台。\n\n应用名称: ${manifest.name}\n图标数量: ${manifest.icons?.length || 0}`);
      
    } catch (error: any) {
      alert(`Manifest检查失败: ${error.message}`);
    }
  };

  const handleCheckServiceWorker = async () => {
    if (!('serviceWorker' in navigator)) {
      alert('浏览器不支持Service Worker');
      return;
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration();
      
      if (registration) {
        console.log('Service Worker注册信息:', registration);
        alert(`Service Worker状态: ${registration.active ? '活跃' : '已注册但未激活'}\n\n请查看控制台获取详细信息。`);
      } else {
        alert('Service Worker未注册');
      }
      
    } catch (error: any) {
      alert(`Service Worker检查失败: ${error.message}`);
    }
  };

  if (!debugInfo) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardBody className="text-center">
              <p>正在收集PWA调试信息...</p>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* 页面标题 */}
        <Card>
          <CardHeader>
            <div className="text-center w-full">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                PWA 安装调试
              </h1>
              <p className="text-gray-600">
                检查PWA安装条件和调试安装问题
              </p>
            </div>
          </CardHeader>
        </Card>

        {/* PWA条件检查 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">PWA安装条件检查</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex justify-between items-center">
                <span>HTTPS/本地环境:</span>
                <Chip color={debugInfo.isSecure ? 'success' : 'danger'} size="sm">
                  {debugInfo.isSecure ? '✓ 安全' : '✗ 不安全'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Service Worker:</span>
                <Chip color={debugInfo.hasServiceWorker ? 'success' : 'danger'} size="sm">
                  {debugInfo.hasServiceWorker ? '✓ 支持' : '✗ 不支持'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Manifest文件:</span>
                <Chip color={debugInfo.hasManifest ? 'success' : 'danger'} size="sm">
                  {debugInfo.hasManifest ? '✓ 存在' : '✗ 缺失'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>已安装状态:</span>
                <Chip color={debugInfo.isStandalone ? 'success' : 'default'} size="sm">
                  {debugInfo.isStandalone ? '✓ 已安装' : '未安装'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Android设备:</span>
                <Chip color={debugInfo.isAndroid ? 'success' : 'default'} size="sm">
                  {debugInfo.isAndroid ? '✓ 是' : '否'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>Chrome浏览器:</span>
                <Chip color={debugInfo.isChrome ? 'success' : 'warning'} size="sm">
                  {debugInfo.isChrome ? '✓ 是' : '否'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>安装提示可用:</span>
                <Chip color={debugInfo.installPromptAvailable ? 'success' : 'warning'} size="sm">
                  {debugInfo.installPromptAvailable ? '✓ 可用' : '不可用'}
                </Chip>
              </div>
              
              <div className="flex justify-between items-center">
                <span>SW状态:</span>
                <Chip color={debugInfo.serviceWorkerState === 'Active' ? 'success' : 'warning'} size="sm">
                  {debugInfo.serviceWorkerState}
                </Chip>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* 设备信息 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">设备信息</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-2">
              <div>
                <span className="font-medium">User Agent:</span>
                <p className="text-sm text-gray-600 mt-1 break-all">{debugInfo.userAgent}</p>
              </div>
              
              {debugInfo.manifestUrl && (
                <div>
                  <span className="font-medium">Manifest URL:</span>
                  <p className="text-sm text-gray-600 mt-1 break-all">{debugInfo.manifestUrl}</p>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* 测试功能 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">测试功能</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <Button
                color="primary"
                onPress={handleTestInstall}
                disabled={!deferredPrompt}
                className="w-full"
              >
                测试安装
              </Button>
              
              <Button
                color="secondary"
                variant="bordered"
                onPress={handleCheckManifest}
                className="w-full"
              >
                检查Manifest
              </Button>
              
              <Button
                color="secondary"
                variant="bordered"
                onPress={handleCheckServiceWorker}
                className="w-full"
              >
                检查SW
              </Button>
            </div>
            
            {installResult && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 text-sm">{installResult}</p>
              </div>
            )}
          </CardBody>
        </Card>

        {/* 解决方案 */}
        <Card className="bg-yellow-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-yellow-800">常见问题解决方案</h2>
          </CardHeader>
          <CardBody>
            <div className="text-sm text-yellow-700 space-y-3">
              <div>
                <h3 className="font-semibold">安装提示不出现:</h3>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>确保使用HTTPS或localhost</li>
                  <li>确保Service Worker已注册并激活</li>
                  <li>确保manifest.json文件可访问</li>
                  <li>确保manifest中有必需的图标</li>
                  <li>尝试刷新页面或清除缓存</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold">Android Chrome特殊要求:</h3>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>需要至少192x192和512x512的图标</li>
                  <li>manifest中display必须是standalone或fullscreen</li>
                  <li>需要start_url字段</li>
                  <li>用户需要与页面有交互</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
