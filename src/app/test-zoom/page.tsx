'use client';

import { <PERSON>, CardBody, CardHeader, <PERSON><PERSON> } from '@heroui/react';

export default function TestZoomPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <h1 className="text-2xl font-bold text-center w-full">防缩放测试页面</h1>
          </CardHeader>
          <CardBody className="space-y-6">
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-4">测试说明</h2>
              <p className="text-gray-600 mb-4">
                此页面已启用防缩放功能，请尝试以下操作来验证：
              </p>
            </div>

            <div className="space-y-4">
              <Card className="bg-blue-50">
                <CardBody>
                  <h3 className="font-semibold text-blue-800 mb-2">移动端测试</h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• 双指捏合缩放 - 应该被阻止</li>
                    <li>• 双击缩放 - 应该被阻止</li>
                    <li>• 长按选择文本 - 应该被阻止</li>
                  </ul>
                </CardBody>
              </Card>

              <Card className="bg-green-50">
                <CardBody>
                  <h3 className="font-semibold text-green-800 mb-2">桌面端测试</h3>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Ctrl + 滚轮缩放 - 应该被阻止</li>
                    <li>• Ctrl + Plus/Minus 键缩放 - 应该被阻止</li>
                    <li>• Ctrl + 0 重置缩放 - 应该被阻止</li>
                  </ul>
                </CardBody>
              </Card>

              <Card className="bg-yellow-50">
                <CardBody>
                  <h3 className="font-semibold text-yellow-800 mb-2">正常功能</h3>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• 输入框文本选择 - 应该正常工作</li>
                    <li>• 按钮点击 - 应该正常工作</li>
                    <li>• 页面滚动 - 应该正常工作</li>
                  </ul>
                </CardBody>
              </Card>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">交互测试</h3>
              
              <input
                type="text"
                placeholder="测试输入框 - 文本应该可以选择"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                defaultValue="这是一段可以选择的文本"
              />

              <textarea
                placeholder="测试文本域 - 文本应该可以选择和编辑"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                defaultValue="这是一段可以选择和编辑的文本内容。"
              />

              <div className="flex gap-4">
                <Button 
                  color="primary"
                  onPress={() => alert('按钮点击正常工作！')}
                >
                  测试按钮
                </Button>
                
                <Button 
                  color="secondary"
                  onPress={() => window.location.reload()}
                >
                  刷新页面
                </Button>
              </div>
            </div>

            <div className="bg-gray-100 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">技术实现</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• viewport meta标签设置 user-scalable=no</li>
                <li>• CSS touch-action: manipulation</li>
                <li>• JavaScript事件监听器阻止缩放手势</li>
                <li>• 键盘快捷键拦截</li>
              </ul>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                如果以上缩放操作都被成功阻止，说明防缩放功能正常工作。
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
