'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/SessionProvider';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardBody,
  CardHeader,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from "@heroui/react";
import BrushStatsPanel from '@/components/admin/BrushStatsPanel';
import BrushProductManager from '@/components/admin/BrushProductManager';
import BrushOrderManager from '@/components/admin/BrushOrderManager';
import BrushRuleManager from '@/components/admin/BrushRuleManager';
import PlatformWalletManager from '@/components/admin/PlatformWalletManager';
import RechargeMonitorPanel from '@/components/admin/RechargeMonitorPanel';

export default function AdminBrushPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('stats');

  // 检查登录状态和权限
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/auth/signin');
      return;
    }

    // 检查管理员权限
    if (!user.role || !['super_admin', 'agent'].includes(user.role)) {
      router.push('/wallet');
      return;
    }

    setLoading(false);
  }, [user, authLoading, router]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!user || !user.role || !['super_admin', 'agent'].includes(user.role)) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🎯 刷单系统管理</h1>
            <p className="text-gray-600 mt-1">管理刷单商品、订单、规则和统计数据</p>
          </div>
          <div className="flex gap-3">
            <Button
              color="primary"
              variant="bordered"
              onPress={() => router.push('/admin')}
            >
              返回管理后台
            </Button>
            <Button
              color="secondary"
              variant="bordered"
              onPress={() => router.push('/brush')}
            >
              查看刷单页面
            </Button>
          </div>
        </div>

        {/* 主要内容 */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          className="w-full"
          size="lg"
        >
          <Tab key="stats" title="📊 数据统计">
            <div className="mt-6">
              <BrushStatsPanel />
            </div>
          </Tab>

          <Tab key="products" title="🛍️ 商品管理">
            <div className="mt-6">
              <BrushProductManager />
            </div>
          </Tab>

          <Tab key="orders" title="📋 订单管理">
            <div className="mt-6">
              <BrushOrderManager />
            </div>
          </Tab>

          <Tab key="rules" title="⚙️ 规则配置">
            <div className="mt-6">
              <BrushRuleManager />
            </div>
          </Tab>

          <Tab key="wallets" title="💰 平台钱包">
            <div className="mt-6">
              <PlatformWalletManager />
            </div>
          </Tab>

          <Tab key="monitor" title="🔍 充值监控">
            <div className="mt-6">
              <RechargeMonitorPanel />
            </div>
          </Tab>
        </Tabs>
      </div>
    </div>
  );
}
