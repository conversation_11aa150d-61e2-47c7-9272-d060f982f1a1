'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardBody,
  CardHeader,
  Chip,
  Spacer,
  Spinner,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Input,
  Select,
  SelectItem,
  Tabs,
  Tab
} from "@heroui/react";
import MobileUserList from '@/components/MobileUserList';
import MobileWalletList from '@/components/MobileWalletList';
import MobileInviteList from '@/components/MobileInviteList';

interface AdminUser {
  id: number;
  username: string;
  email?: string;
  name?: string;
  role: 'super_admin' | 'agent' | 'user';
  permissions: string[];
  parent_admin_id?: number;
  is_active: boolean;
  created_at: string;
  invited_users_count?: number;
  invite_code?: string;
  invited_by_username?: string;
}

interface AdminWallet {
  id: number;
  user_id: number;
  name: string;
  address: string;
  wallet_type: 'created' | 'imported';
  network: 'mainnet' | 'nile' | 'shasta';
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  username: string;
  email?: string;
  user_name?: string;
}

interface InviteCode {
  id: number;
  code: string;
  created_by?: number;
  used_by?: number;
  is_active: boolean;
  max_uses: number;
  used_count: number;
  expires_at?: string;
  created_at: string;
  used_at?: string;
}

export default function AdminPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null);
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [wallets, setWallets] = useState<AdminWallet[]>([]);
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [showCreateAgentModal, setShowCreateAgentModal] = useState(false);
  const [showCreateInviteModal, setShowCreateInviteModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState('users');
  const [isMobile, setIsMobile] = useState(false);

  // 创建代理表单
  const [agentForm, setAgentForm] = useState({
    username: '',
    email: '',
    password: '',
    name: '',
    permissions: [] as string[]
  });

  // 创建邀请码表单
  const [inviteForm, setInviteForm] = useState({
    maxUses: '1',
    expiresIn: '30' // 天数
  });

  const [creating, setCreating] = useState(false);

  useEffect(() => {
    checkAdminAccess();

    // 检测移动端
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const checkAdminAccess = async () => {
    try {
      const response = await fetch('/api/admin/check');
      if (response.ok) {
        const data = await response.json();
        if (data.isAdmin) {
          setCurrentUser(data.user);
          await loadData();
        } else {
          router.push('/auth/signin');
        }
      } else {
        router.push('/auth/signin');
      }
    } catch (error) {
      console.error('检查管理员权限失败:', error);
      router.push('/auth/signin');
    } finally {
      setLoading(false);
    }
  };

  const loadData = async () => {
    try {
      // 加载用户列表
      const usersResponse = await fetch('/api/admin/users');
      if (usersResponse.ok) {
        const usersResult = await usersResponse.json();
        if (usersResult.success && usersResult.data) {
          setUsers(usersResult.data);
        }
      }

      // 加载钱包列表
      const walletsResponse = await fetch('/api/admin/wallets');
      if (walletsResponse.ok) {
        const walletsResult = await walletsResponse.json();
        if (walletsResult.success && walletsResult.data) {
          setWallets(walletsResult.data);
        }
      }

      // 加载邀请码列表
      const inviteResponse = await fetch('/api/admin/invite-codes');
      if (inviteResponse.ok) {
        const inviteResult = await inviteResponse.json();
        if (inviteResult.success && inviteResult.data) {
          setInviteCodes(inviteResult.data);
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  const handleCreateAgent = async () => {
    if (!agentForm.username.trim() || !agentForm.password.trim()) {
      alert('请填写用户名和密码');
      return;
    }

    try {
      setCreating(true);
      const response = await fetch('/api/admin/create-agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(agentForm)
      });

      if (response.ok) {
        alert('代理创建成功！');
        setShowCreateAgentModal(false);
        setAgentForm({ username: '', email: '', password: '', name: '', permissions: [] });
        await loadData();
      } else {
        const error = await response.json();
        alert(`创建失败: ${error.message}`);
      }
    } catch (error) {
      console.error('创建代理失败:', error);
      alert('创建代理失败，请重试');
    } finally {
      setCreating(false);
    }
  };

  const toggleWalletStatus = async (walletId: number, currentStatus: boolean) => {
    try {
      const response = await fetch('/api/admin/wallets', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          walletId: walletId,
          isActive: !currentStatus
        })
      });

      if (response.ok) {
        await loadData();
        alert(`钱包已${!currentStatus ? '启用' : '禁用'}`);
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.message}`);
      }
    } catch (error) {
      console.error('切换钱包状态失败:', error);
      alert('操作失败，请重试');
    }
  };

  const handleCreateInviteCode = async () => {
    try {
      setCreating(true);
      const response = await fetch('/api/admin/create-invite-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          maxUses: parseInt(inviteForm.maxUses),
          expiresIn: parseInt(inviteForm.expiresIn)
        })
      });

      if (response.ok) {
        const data = await response.json();
        alert(`邀请码创建成功！\n邀请码: ${data.code}`);
        setShowCreateInviteModal(false);
        setInviteForm({ maxUses: '1', expiresIn: '30' });
        await loadData();
      } else {
        const error = await response.json();
        alert(`创建失败: ${error.message}`);
      }
    } catch (error) {
      console.error('创建邀请码失败:', error);
      alert('创建邀请码失败，请重试');
    } finally {
      setCreating(false);
    }
  };

  const toggleUserStatus = async (userId: number, isActive: boolean) => {
    try {
      const response = await fetch('/api/admin/toggle-user-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, isActive: !isActive })
      });

      if (response.ok) {
        await loadData();
      } else {
        const error = await response.json();
        alert(`操作失败: ${error.message}`);
      }
    } catch (error) {
      console.error('切换用户状态失败:', error);
      alert('操作失败，请重试');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!currentUser) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4 px-2 sm:py-8 sm:px-4">
      <div className="max-w-7xl mx-auto">
        {/* 头部 - 移动端适配 */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-xl sm:text-3xl font-bold text-gray-900">管理后台</h1>
            <div className="flex flex-col sm:flex-row sm:items-center mt-1">
              <p className="text-gray-600">
                欢迎，{currentUser.name || currentUser.username}
              </p>
              <Chip size="sm" color="primary" variant="flat" className="mt-1 sm:mt-0 sm:ml-2 w-fit">
                {currentUser.role === 'super_admin' ? '超级管理员' : '代理'}
              </Chip>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              color="warning"
              variant="solid"
              onPress={() => router.push('/admin/brush')}
              size="sm"
              className="w-full sm:w-auto font-semibold"
            >
              🎯 刷单管理
            </Button>
            <Button
              color="primary"
              variant="bordered"
              onPress={() => router.push('/wallet')}
              size="sm"
              className="w-full sm:w-auto"
            >
              返回钱包
            </Button>
            <Button
              color="danger"
              variant="light"
              onPress={() => {
                // 登出逻辑
                document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                window.location.href = '/';
              }}
              size="sm"
              className="w-full sm:w-auto"
            >
              退出登录
            </Button>
          </div>
        </div>

        {/* 主要内容 */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          className="w-full"
        >
          <Tab key="users" title="用户管理">
            <Card>
              <CardHeader className="flex justify-between">
                <h3 className="text-lg font-semibold">用户列表</h3>
                {currentUser.role === 'super_admin' && (
                  <Button
                    color="primary"
                    onPress={() => setShowCreateAgentModal(true)}
                  >
                    创建代理
                  </Button>
                )}
              </CardHeader>
              <CardBody>
                {isMobile ? (
                  <MobileUserList
                    users={users}
                    currentUser={currentUser}
                    onToggleStatus={toggleUserStatus}
                  />
                ) : (
                  <div className="overflow-x-auto">
                    <Table aria-label="用户列表" className="min-w-full">
                      <TableHeader>
                        <TableColumn className="min-w-[100px]">用户名</TableColumn>
                        <TableColumn className="min-w-[150px]">邮箱</TableColumn>
                        <TableColumn className="min-w-[100px]">角色</TableColumn>
                        <TableColumn className="min-w-[80px]">状态</TableColumn>
                        <TableColumn className="min-w-[100px]">注册时间</TableColumn>
                        <TableColumn className="min-w-[80px]">操作</TableColumn>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>{user.username}</TableCell>
                            <TableCell>{user.email || '-'}</TableCell>
                            <TableCell>
                              <Chip
                                size="sm"
                                color={
                                  user.role === 'super_admin' ? 'danger' :
                                  user.role === 'agent' ? 'primary' : 'default'
                                }
                                variant="flat"
                              >
                                {user.role === 'super_admin' ? '超级管理员' :
                                 user.role === 'agent' ? '代理' : '普通用户'}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              <Chip
                                size="sm"
                                color={user.is_active ? 'success' : 'default'}
                                variant="flat"
                              >
                                {user.is_active ? '活跃' : '禁用'}
                              </Chip>
                            </TableCell>
                            <TableCell>
                              {new Date(user.created_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              {currentUser.role === 'super_admin' && user.id !== currentUser.id && (
                                <Button
                                  size="sm"
                                  color={user.is_active ? 'danger' : 'success'}
                                  variant="light"
                                  onPress={() => toggleUserStatus(user.id, user.is_active)}
                                >
                                  {user.is_active ? '禁用' : '启用'}
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardBody>
            </Card>
          </Tab>

          <Tab key="wallets" title="钱包管理">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">钱包列表</h3>
              </CardHeader>
              <CardBody>
                <Table aria-label="钱包列表">
                  <TableHeader>
                    <TableColumn>钱包名称</TableColumn>
                    <TableColumn>地址</TableColumn>
                    <TableColumn>用户</TableColumn>
                    <TableColumn>类型</TableColumn>
                    <TableColumn>网络</TableColumn>
                    <TableColumn>状态</TableColumn>
                    <TableColumn>创建时间</TableColumn>
                    <TableColumn>操作</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {wallets.map((wallet) => (
                      <TableRow key={wallet.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{wallet.name}</div>
                            {wallet.is_default && (
                              <Chip size="sm" color="warning" variant="flat">
                                默认
                              </Chip>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {wallet.address.slice(0, 8)}...{wallet.address.slice(-6)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{wallet.username}</div>
                            {wallet.user_name && (
                              <div className="text-sm text-gray-500">{wallet.user_name}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={wallet.wallet_type === 'created' ? 'primary' : 'secondary'}
                            variant="flat"
                          >
                            {wallet.wallet_type === 'created' ? '创建' : '导入'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={
                              wallet.network === 'mainnet' ? 'success' :
                              wallet.network === 'nile' ? 'primary' : 'warning'
                            }
                            variant="flat"
                          >
                            {wallet.network === 'mainnet' ? '主网' :
                             wallet.network === 'nile' ? 'Nile' : 'Shasta'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={wallet.is_active ? 'success' : 'danger'}
                            variant="flat"
                          >
                            {wallet.is_active ? '正常' : '禁用'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {new Date(wallet.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {currentUser.role === 'super_admin' && (
                            <Button
                              size="sm"
                              color={wallet.is_active ? 'danger' : 'success'}
                              variant="light"
                              onPress={() => toggleWalletStatus(wallet.id, wallet.is_active)}
                            >
                              {wallet.is_active ? '禁用' : '启用'}
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardBody>
            </Card>
          </Tab>

          <Tab key="invites" title="邀请码管理">
            <Card>
              <CardHeader className="flex justify-between">
                <h3 className="text-lg font-semibold">邀请码列表</h3>
                <Button
                  color="primary"
                  onPress={() => setShowCreateInviteModal(true)}
                >
                  创建邀请码
                </Button>
              </CardHeader>
              <CardBody>
                <Table aria-label="邀请码列表">
                  <TableHeader>
                    <TableColumn>邀请码</TableColumn>
                    <TableColumn>最大使用次数</TableColumn>
                    <TableColumn>已使用次数</TableColumn>
                    <TableColumn>状态</TableColumn>
                    <TableColumn>过期时间</TableColumn>
                    <TableColumn>创建时间</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {inviteCodes.map((code) => (
                      <TableRow key={code.id}>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                            {code.code}
                          </code>
                        </TableCell>
                        <TableCell>{code.max_uses === -1 ? '无限制' : code.max_uses}</TableCell>
                        <TableCell>{code.used_count}</TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={code.is_active ? 'success' : 'default'}
                            variant="flat"
                          >
                            {code.is_active ? '活跃' : '已失效'}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {code.expires_at
                            ? new Date(code.expires_at).toLocaleDateString()
                            : '永不过期'
                          }
                        </TableCell>
                        <TableCell>
                          {new Date(code.created_at).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardBody>
            </Card>
          </Tab>
        </Tabs>

        {/* 创建代理模态框 */}
        <Modal isOpen={showCreateAgentModal} onClose={() => setShowCreateAgentModal(false)}>
          <ModalContent>
            <ModalHeader>创建代理</ModalHeader>
            <ModalBody>
              <Input
                label="用户名"
                placeholder="输入用户名"
                value={agentForm.username}
                onChange={(e) => setAgentForm({ ...agentForm, username: e.target.value })}
              />
              <Input
                label="邮箱"
                type="email"
                placeholder="输入邮箱（可选）"
                value={agentForm.email}
                onChange={(e) => setAgentForm({ ...agentForm, email: e.target.value })}
              />
              <Input
                label="显示名称"
                placeholder="输入显示名称（可选）"
                value={agentForm.name}
                onChange={(e) => setAgentForm({ ...agentForm, name: e.target.value })}
              />
              <Input
                label="密码"
                type="password"
                placeholder="输入密码"
                value={agentForm.password}
                onChange={(e) => setAgentForm({ ...agentForm, password: e.target.value })}
              />
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowCreateAgentModal(false)}>
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleCreateAgent}
                isLoading={creating}
              >
                创建代理
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 创建邀请码模态框 */}
        <Modal isOpen={showCreateInviteModal} onClose={() => setShowCreateInviteModal(false)}>
          <ModalContent>
            <ModalHeader>创建邀请码</ModalHeader>
            <ModalBody>
              <Input
                label="最大使用次数"
                type="number"
                placeholder="输入最大使用次数"
                value={inviteForm.maxUses}
                onChange={(e) => setInviteForm({ ...inviteForm, maxUses: e.target.value })}
                description="输入-1表示无限制"
              />
              <Input
                label="有效期（天）"
                type="number"
                placeholder="输入有效期天数"
                value={inviteForm.expiresIn}
                onChange={(e) => setInviteForm({ ...inviteForm, expiresIn: e.target.value })}
                description="输入0表示永不过期"
              />
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowCreateInviteModal(false)}>
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleCreateInviteCode}
                isLoading={creating}
              >
                创建邀请码
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
