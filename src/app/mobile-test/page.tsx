'use client';

import { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Textarea, Select, SelectItem } from '@heroui/react';
import MobileInput from '@/components/MobileInput';

export default function MobileTestPage() {
  const [formData, setFormData] = useState({
    text: '',
    email: '',
    password: '',
    number: '',
    textarea: '',
    select: ''
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 px-4">
      <div className="max-w-md mx-auto space-y-4">
        <Card>
          <CardHeader>
            <h1 className="text-xl font-bold text-center w-full">移动端输入框测试</h1>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="text-center text-sm text-gray-600">
              <p>在移动设备上测试输入框是否会导致页面缩放</p>
            </div>
          </CardBody>
        </Card>

        {/* 标准HeroUI输入框 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">标准输入框</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <Input
              label="文本输入"
              placeholder="点击测试是否缩放"
              value={formData.text}
              onChange={(e) => setFormData({ ...formData, text: e.target.value })}
            />
            
            <Input
              label="邮箱输入"
              type="email"
              placeholder="输入邮箱地址"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            />
            
            <Input
              label="密码输入"
              type="password"
              placeholder="输入密码"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            />
            
            <Input
              label="数字输入"
              type="number"
              placeholder="输入数字"
              value={formData.number}
              onChange={(e) => setFormData({ ...formData, number: e.target.value })}
            />
          </CardBody>
        </Card>

        {/* 优化的移动端输入框 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">移动端优化输入框</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <MobileInput
              label="优化文本输入"
              placeholder="防缩放输入框"
              value={formData.text}
              onChange={(e) => setFormData({ ...formData, text: e.target.value })}
            />
            
            <MobileInput
              label="优化邮箱输入"
              type="email"
              placeholder="防缩放邮箱输入"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            />
            
            <MobileInput
              label="优化密码输入"
              type="password"
              placeholder="防缩放密码输入"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            />
          </CardBody>
        </Card>

        {/* 其他表单元素 */}
        <Card>
          <CardHeader>
            <h2 className="text-lg font-semibold">其他表单元素</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <Textarea
              label="文本域"
              placeholder="输入多行文本"
              value={formData.textarea}
              onChange={(e) => setFormData({ ...formData, textarea: e.target.value })}
              classNames={{
                input: "text-base sm:text-sm"
              }}
            />
            
            <Select
              label="选择框"
              placeholder="选择一个选项"
              selectedKeys={formData.select ? [formData.select] : []}
              onSelectionChange={(keys) => {
                const selected = Array.from(keys)[0] as string;
                setFormData({ ...formData, select: selected });
              }}
            >
              <SelectItem key="option1">选项1</SelectItem>
              <SelectItem key="option2">选项2</SelectItem>
              <SelectItem key="option3">选项3</SelectItem>
            </Select>
            
            <Button
              color="primary"
              className="w-full"
              onPress={() => alert('按钮点击测试')}
            >
              测试按钮
            </Button>
          </CardBody>
        </Card>

        {/* 测试说明 */}
        <Card className="bg-blue-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-blue-800">测试说明</h2>
          </CardHeader>
          <CardBody>
            <div className="text-sm text-blue-700 space-y-2">
              <h3 className="font-semibold">移动端测试步骤：</h3>
              <ol className="list-decimal list-inside space-y-1">
                <li>在移动设备上打开此页面</li>
                <li>点击各种输入框</li>
                <li>观察页面是否发生缩放</li>
                <li>测试输入和编辑功能</li>
                <li>测试按钮和选择框</li>
              </ol>
              
              <h3 className="font-semibold mt-4">期望结果：</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>点击输入框时页面不缩放</li>
                <li>输入框可以正常输入文字</li>
                <li>键盘弹出时布局正常</li>
                <li>所有交互功能正常工作</li>
              </ul>
            </div>
          </CardBody>
        </Card>

        {/* 技术信息 */}
        <Card className="bg-green-50">
          <CardHeader>
            <h2 className="text-lg font-semibold text-green-800">技术实现</h2>
          </CardHeader>
          <CardBody>
            <div className="text-sm text-green-700 space-y-2">
              <h3 className="font-semibold">防缩放措施：</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>viewport meta标签设置user-scalable=no</li>
                <li>输入框字体大小设为16px（iOS要求）</li>
                <li>CSS防缩放样式</li>
                <li>JavaScript事件监听和处理</li>
                <li>专门的移动端输入框组件</li>
              </ul>
              
              <h3 className="font-semibold mt-4">兼容性：</h3>
              <ul className="list-disc list-inside space-y-1">
                <li>iOS Safari</li>
                <li>Android Chrome</li>
                <li>移动端其他浏览器</li>
                <li>桌面端正常显示</li>
              </ul>
            </div>
          </CardBody>
        </Card>

        {/* 当前表单数据 */}
        <Card className="bg-gray-50">
          <CardHeader>
            <h2 className="text-lg font-semibold">当前表单数据</h2>
          </CardHeader>
          <CardBody>
            <pre className="text-xs bg-white p-2 rounded overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
