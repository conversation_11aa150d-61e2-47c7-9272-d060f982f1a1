'use client';

import { Card, CardBody, CardHeader, Divider } from '@heroui/react';

export default function WalletPasswordHelpPage() {
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <h1 className="text-2xl font-bold">钱包密码说明</h1>
        </CardHeader>
        <CardBody className="space-y-6">
          {/* 什么是钱包密码 */}
          <Card className="bg-blue-50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-blue-800">🔐 什么是钱包密码？</h2>
            </CardHeader>
            <CardBody>
              <p className="text-blue-700">
                钱包密码是您在创建或导入钱包时设置的密码，用于保护您的私钥安全。
                私钥会使用此密码进行加密存储，确保即使数据库被访问，您的私钥也是安全的。
              </p>
            </CardBody>
          </Card>

          {/* 密码的作用 */}
          <Card className="bg-green-50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-green-800">🛡️ 密码的作用</h2>
            </CardHeader>
            <CardBody>
              <ul className="text-green-700 space-y-2">
                <li>• <strong>保护私钥</strong>：私钥使用密码加密存储，防止泄露</li>
                <li>• <strong>转账验证</strong>：每次转账时需要输入密码来解密私钥</li>
                <li>• <strong>安全保障</strong>：即使他人获得数据库访问权，也无法使用您的钱包</li>
                <li>• <strong>本地验证</strong>：密码验证在本地进行，不会传输到服务器</li>
              </ul>
            </CardBody>
          </Card>

          {/* 何时需要密码 */}
          <Card className="bg-yellow-50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-yellow-800">⏰ 何时需要输入密码？</h2>
            </CardHeader>
            <CardBody>
              <ul className="text-yellow-700 space-y-2">
                <li>• <strong>创建钱包</strong>：设置钱包密码（必需）</li>
                <li>• <strong>导入钱包</strong>：设置钱包密码（必需）</li>
                <li>• <strong>发送转账</strong>：输入密码验证身份（必需）</li>
                <li>• <strong>查看余额</strong>：无需密码</li>
                <li>• <strong>接收转账</strong>：无需密码</li>
              </ul>
            </CardBody>
          </Card>

          {/* 密码要求 */}
          <Card className="bg-purple-50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-purple-800">📋 密码要求</h2>
            </CardHeader>
            <CardBody>
              <ul className="text-purple-700 space-y-2">
                <li>• <strong>最小长度</strong>：至少6位字符</li>
                <li>• <strong>建议长度</strong>：8位或以上</li>
                <li>• <strong>字符类型</strong>：支持字母、数字、特殊字符</li>
                <li>• <strong>安全建议</strong>：使用大小写字母、数字和符号的组合</li>
              </ul>
            </CardBody>
          </Card>

          <Divider />

          {/* 安全提示 */}
          <Card className="bg-red-50">
            <CardHeader>
              <h2 className="text-lg font-semibold text-red-800">⚠️ 重要安全提示</h2>
            </CardHeader>
            <CardBody>
              <div className="text-red-700 space-y-3">
                <div>
                  <h3 className="font-semibold">密码丢失风险</h3>
                  <p>如果忘记钱包密码，将无法进行转账操作。请务必记住您的密码！</p>
                </div>
                
                <div>
                  <h3 className="font-semibold">密码保管</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>不要与他人分享您的钱包密码</li>
                    <li>不要在不安全的地方记录密码</li>
                    <li>建议使用密码管理器安全存储</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold">密码强度</h3>
                  <ul className="list-disc list-inside space-y-1">
                    <li>使用复杂密码，避免简单的数字组合</li>
                    <li>不要使用生日、姓名等容易猜测的信息</li>
                    <li>定期更换密码（暂不支持，请重新创建钱包）</li>
                  </ul>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* 常见问题 */}
          <Card>
            <CardHeader>
              <h2 className="text-lg font-semibold">❓ 常见问题</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-gray-800">Q: 忘记密码怎么办？</h3>
                  <p className="text-gray-600">
                    A: 目前无法重置密码。如果忘记密码，您需要使用私钥重新导入钱包并设置新密码。
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800">Q: 可以修改密码吗？</h3>
                  <p className="text-gray-600">
                    A: 目前不支持直接修改密码。如需更换密码，请导出私钥后重新导入钱包。
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800">Q: 密码是否会被传输到服务器？</h3>
                  <p className="text-gray-600">
                    A: 密码仅在本地使用，用于加密/解密私钥，不会传输到服务器。
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800">Q: 为什么需要设置密码？</h3>
                  <p className="text-gray-600">
                    A: 密码是保护您数字资产的重要安全措施，确保只有您本人可以使用钱包进行转账。
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* 操作流程 */}
          <Card className="bg-gray-50">
            <CardHeader>
              <h2 className="text-lg font-semibold">📝 操作流程</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-blue-600">创建新钱包</h3>
                  <ol className="list-decimal list-inside text-gray-600 space-y-1">
                    <li>点击"创建钱包"按钮</li>
                    <li>输入钱包名称</li>
                    <li>设置钱包密码（至少6位）</li>
                    <li>选择网络（建议选择Nile测试网）</li>
                    <li>点击"创建"完成</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-semibold text-green-600">导入现有钱包</h3>
                  <ol className="list-decimal list-inside text-gray-600 space-y-1">
                    <li>点击"导入钱包"按钮</li>
                    <li>输入钱包名称</li>
                    <li>输入64位私钥</li>
                    <li>设置钱包密码（至少6位）</li>
                    <li>选择网络</li>
                    <li>点击"导入"完成</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-semibold text-orange-600">发送转账</h3>
                  <ol className="list-decimal list-inside text-gray-600 space-y-1">
                    <li>选择要转账的钱包</li>
                    <li>点击"转账"按钮</li>
                    <li>输入接收地址和金额</li>
                    <li>输入钱包密码进行验证</li>
                    <li>确认转账信息</li>
                    <li>等待交易确认</li>
                  </ol>
                </div>
              </div>
            </CardBody>
          </Card>
        </CardBody>
      </Card>
    </div>
  );
}
