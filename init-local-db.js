#!/usr/bin/env node

/**
 * 本地开发环境数据库初始化脚本
 * 使用SQLite创建本地开发数据库
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DB_FILE = 'local.db';
const SCHEMA_FILE = 'schema.sql';

console.log('🚀 初始化本地开发数据库...');

// 检查是否安装了sqlite3
try {
  execSync('sqlite3 --version', { stdio: 'ignore' });
} catch (error) {
  console.error('❌ 错误: 未找到sqlite3命令');
  console.log('请先安装SQLite3:');
  console.log('  macOS: brew install sqlite3');
  console.log('  Ubuntu: sudo apt-get install sqlite3');
  console.log('  Windows: 下载并安装 https://sqlite.org/download.html');
  process.exit(1);
}

// 检查schema.sql文件是否存在
if (!fs.existsSync(SCHEMA_FILE)) {
  console.error(`❌ 错误: 未找到${SCHEMA_FILE}文件`);
  process.exit(1);
}

// 删除现有数据库文件（如果存在）
if (fs.existsSync(DB_FILE)) {
  console.log('🗑️  删除现有数据库文件...');
  fs.unlinkSync(DB_FILE);
}

// 创建新数据库并导入schema
try {
  console.log('📊 创建数据库并导入schema...');
  execSync(`sqlite3 ${DB_FILE} < ${SCHEMA_FILE}`, { stdio: 'inherit' });
  
  // 插入系统邀请码
  console.log('🎫 插入系统邀请码...');
  const insertInviteCode = `
    INSERT INTO invite_codes (code, created_by, is_active, max_uses, used_count) 
    VALUES ('SUPER_ADMIN_INIT', 0, TRUE, 1, 0);
  `;
  execSync(`sqlite3 ${DB_FILE} "${insertInviteCode}"`, { stdio: 'inherit' });
  
  console.log('✅ 数据库初始化完成!');
  console.log(`📁 数据库文件: ${path.resolve(DB_FILE)}`);
  console.log('');
  console.log('🔧 下一步:');
  console.log('1. 启动开发服务器: pnpm run dev');
  console.log('2. 访问 http://localhost:3000/auth/register');
  console.log('3. 使用邀请码 "SUPER_ADMIN_INIT" 注册第一个用户');
  console.log('4. 手动设置为超级管理员 (参考DEVELOPMENT_SETUP.md)');
  
} catch (error) {
  console.error('❌ 数据库初始化失败:', error.message);
  process.exit(1);
}
