#!/usr/bin/env node

/**
 * 测试TRON API密钥是否正常工作
 */

const { TronWeb } = require('tronweb');

async function testTronAPI() {
  console.log('🚀 开始测试TRON API...');
  
  const apiKey = '3f67c2cc-9119-468b-a336-f3f95b7bfec3';
  
  // 创建TronWeb实例
  const tronWeb = new TronWeb({
    fullHost: 'https://api.trongrid.io',
    headers: { 
      'TRON-PRO-API-KEY': apiKey,
      'Content-Type': 'application/json'
    },
    privateKey: '01' // 临时私钥
  });

  console.log(`📡 API密钥: ${apiKey}`);
  console.log(`🌐 网络: https://api.trongrid.io`);

  try {
    // 测试1: 获取账户信息
    console.log('\n📋 测试1: 获取USDT合约账户信息...');
    const usdtContract = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
    const accountInfo = await tronWeb.trx.getAccount(usdtContract);
    console.log('✅ 账户信息获取成功');
    console.log(`   地址: ${accountInfo.address}`);
    console.log(`   余额: ${tronWeb.fromSun(accountInfo.balance || 0)} TRX`);

    // 测试2: 获取TRX余额
    console.log('\n💰 测试2: 获取TRX余额...');
    const balance = await tronWeb.trx.getBalance(usdtContract);
    console.log('✅ TRX余额获取成功');
    console.log(`   余额: ${tronWeb.fromSun(balance)} TRX`);

    // 测试3: 获取USDT合约信息
    console.log('\n🔗 测试3: 获取USDT合约信息...');
    const contract = await tronWeb.contract().at(usdtContract);
    const name = await contract.name().call();
    const symbol = await contract.symbol().call();
    const decimals = await contract.decimals().call();
    console.log('✅ USDT合约信息获取成功');
    console.log(`   名称: ${name}`);
    console.log(`   符号: ${symbol}`);
    console.log(`   小数位: ${decimals}`);

    // 测试4: 获取最新区块
    console.log('\n📦 测试4: 获取最新区块信息...');
    const latestBlock = await tronWeb.trx.getCurrentBlock();
    console.log('✅ 最新区块信息获取成功');
    console.log(`   区块号: ${latestBlock.block_header.raw_data.number}`);
    console.log(`   时间戳: ${new Date(latestBlock.block_header.raw_data.timestamp).toLocaleString()}`);

    console.log('\n🎉 所有测试通过！TRON API密钥工作正常。');
    
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('错误详情:', error);
    
    if (error.message.includes('ApiKey not exists')) {
      console.log('\n💡 解决方案:');
      console.log('1. 检查API密钥是否正确');
      console.log('2. 确保API密钥已激活');
      console.log('3. 检查请求头格式是否正确');
    }
  }
}

// 运行测试
testTronAPI().catch(console.error);
