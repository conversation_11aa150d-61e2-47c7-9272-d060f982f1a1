# TRON钱包应用

这是一个专业的TRON区块链钱包应用，支持TRX和USDT管理、转账、收款，以及完整的管理后台系统。

## 功能特性

### 💰 H5钱包应用（面向客户）
- **钱包管理**: 创建和导入TRON钱包
- **多币种支持**: TRX和USDT资产管理
- **转账功能**: 安全快速的转账操作
- **收款功能**: 二维码收款和地址分享
- **实时余额**: 实时查询钱包余额
- **交易记录**: 完整的交易历史记录
- **多钱包**: 支持管理多个钱包地址

### 🔐 安全特性
- **私钥加密**: 安全的私钥存储和管理
- **MFA验证**: 双重身份验证保护
- **邀请码注册**: 基于邀请码的注册机制
- **权限控制**: 分级权限管理系统

### 🛠️ 管理后台（内部管理）
- **用户管理**: 查看和管理所有用户
- **代理系统**: 超级管理员和代理分级管理
- **邀请码管理**: 创建和管理邀请码
- **权限控制**: 细粒度的权限管理
- **数据统计**: 用户和交易数据统计

## 技术栈

- **前端**: Next.js 15, React, TypeScript, HeroUI, Tailwind CSS
- **后端**: Next.js API Routes, Cloudflare Workers
- **数据库**: Cloudflare D1 (SQLite)
- **区块链**: TronWeb SDK, TRON网络集成
- **认证**: JWT Token, bcrypt密码加密, OTP双重验证
- **部署**: Cloudflare Pages/Workers

## 本地开发

### 1. 环境准备
```bash
# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env.local
```

### 2. 配置 Cloudflare D1
```bash
# 创建 D1 数据库
npx wrangler d1 create tron-wallet

# 初始化数据库架构
npx wrangler d1 execute tron-wallet --local --file=./schema.sql
```

### 3. 配置环境变量
在 `.env.local` 中配置:
```env
# JWT密钥
JWT_SECRET=your_jwt_secret_key

# TRON API密钥（可选，用于提高API限制）
TRON_API_KEY=your_tron_api_key

# 应用基础URL
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000

## 使用流程

### 用户注册和登录
1. **获取邀请码**: 从管理员或代理获取邀请码
2. **注册账户**: 使用邀请码注册，设置用户名和密码
3. **登录系统**: 使用用户名密码登录，支持MFA验证

### 钱包管理
1. **创建钱包**: 系统生成新的TRON钱包地址和私钥
2. **导入钱包**: 通过私钥导入现有钱包
3. **查看余额**: 实时查询TRX和USDT余额
4. **管理多钱包**: 支持创建和管理多个钱包

### 转账操作
1. **发起转账**: 输入接收地址、金额和币种
2. **确认交易**: 输入钱包密码确认转账
3. **等待确认**: 交易提交到TRON网络等待确认
4. **查看记录**: 在交易记录中查看转账状态

### 管理后台
1. **超级管理员**: 通过SQL初始化，拥有所有权限
2. **创建代理**: 超级管理员可以创建代理账户
3. **用户管理**: 管理员可以查看和管理用户
4. **邀请码管理**: 创建和管理邀请码

## 初始化超级管理员

1. 使用邀请码 `SUPER_ADMIN_INIT` 注册第一个用户
2. 手动执行SQL将该用户设置为超级管理员：

```sql
-- 假设注册的用户ID为1
INSERT INTO admins (user_id, role, permissions, is_active) VALUES
(1, 'super_admin', '["manage_users", "manage_admins", "view_all_wallets", "view_all_transactions", "system_config"]', TRUE);
```

## 部署

```bash
# 构建并部署到 Cloudflare
npm run deploy
```

## 安全注意事项

- 🔒 私钥采用加密存储，用户需要设置钱包密码
- 🔐 支持MFA双重验证，建议启用
- 🎫 基于邀请码的注册机制，确保用户来源可控
- 👥 分级权限管理，代理只能管理自己邀请的用户
- 🔑 JWT Token认证，支持会话管理

## 许可证

MIT License
